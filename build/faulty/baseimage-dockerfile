FROM ubuntu:18.04

ARG TARGETARCH
ARG MIRROR_SOURCE=mirrors.aliyun.com
ARG TOMCAT_VERSION=8.5.73

ENV PATH="/usr/lib/go-1.10/bin:$PATH"
ENV JAVA_HOME="/usr/local/jdk1.8"
ENV JRE_HOME="${JAVA_HOME}/jre"
ENV CLASSPATH=".:${JAVA_HOME}/lib:${JRE_HOME}/lib"
ENV PATH=".:/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/lib/go-1.10/bin:${JAVA_HOME}/bin"

RUN sed -i "s!archive.ubuntu.com/!${MIRROR_SOURCE}/!g" /etc/apt/sources.list \
    && DEBIAN_FRONTEND=noninteractive apt-get update \
    && DEBIAN_FRONTEND=noninteractive apt-get install -y --fix-missing \
    libpolkit-gobject-1-dev \
    automake \
    gcc \
    make \
    sudo \
    golang-1.10 \
    vim \
    netcat \
    net-tools \
    strace \
    software-properties-common \
    curl \
    python3.8 \
    python3-pip \
    dnsutils \
    tcpdump \
    nmap \
    tshark \
    ngrep \
    wget \
    gawk \
    busybox \
    ruby \
    telnet \
    apache2 \
    php7.2 \
    libapache2-mod-php7.2 \
    git \
    jq \
    redis-tools \
    policykit-1 \
    && rm /usr/bin/python3 \
    && ln -s /usr/bin/python3.8 /usr/bin/python3 \
    && pip3 install --upgrade pip \
    && pip3 install PyMySQL setuptools setuptools_rust paramiko -i https://pypi.tuna.tsinghua.edu.cn/simple

RUN if [ "$TARGETARCH" = "amd64" ]; then \
    wget -O /tmp/docker-19.03.9.tgz https://mirrors.tuna.tsinghua.edu.cn/docker-ce/linux/static/stable/x86_64/docker-19.03.9.tgz \
    && wget -O /jdk.tar.gz https://repo.huaweicloud.com/java/jdk/8u151-b12/jdk-8u151-linux-x64.tar.gz; \
    elif [ "$TARGETARCH" = "arm64" ]; then \
    wget -O /tmp/docker-19.03.9.tgz https://mirrors.tuna.tsinghua.edu.cn/docker-ce/linux/static/stable/aarch64/docker-19.03.9.tgz \
    && wget -O /jdk.tar.gz https://repo.huaweicloud.com/java/jdk/8u151-b12/jdk-8u151-linux-arm64-vfp-hflt.tar.gz; \
    fi

RUN tar xvzf /jdk.tar.gz \
    && mv /jdk1.8.0_151 /usr/local/jdk1.8 \
    && rm /jdk.tar.gz \
    && TOMCAT_VERSION=`curl --silent  https://mirrors.cnnic.cn/apache/tomcat/tomcat-8/ | grep v8 | head -1 | awk '{split($5,c,">v") ; split(c[2],d,"/") ; print d[1]}'` \
    && wget -O /usr/local/tomcat.tar.gz https://mirrors.cnnic.cn/apache/tomcat/tomcat-8/v${TOMCAT_VERSION}/bin/apache-tomcat-${TOMCAT_VERSION}.tar.gz \
    && tar -zxvf /usr/local/tomcat.tar.gz -C /usr/local/ \
    && chmod 755 -R /usr/local/apache-tomcat-${TOMCAT_VERSION}/ \
    && rm /usr/local/tomcat.tar.gz \