package riskexplorer

import (
	"context"
	"fmt"

	"gitlab.com/piccolo_su/vegeta/pkg/echelper"
)

type EventReporter struct {
	sherlockCli *echelper.SherlockClient
}

func NewEventReporter(sherlockClient *echelper.SherlockClient) *EventReporter {
	return &EventReporter{
		sherlockCli: sherlockClient,
	}
}
func (r *EventReporter) Name() string {
	return "signal_reporter"
}

func (r *EventReporter) LoadSummary(ctx context.Context, assetsSummary []*NamespaceSummary) (TotalSummary, error) {
	riskStatsMap := map[string]map[string][]echelper.RiskStatsItem{}
	clusterSet := map[string]struct{}{}
	for _, v := range assetsSummary {
		if _, ok := clusterSet[v.ClusterKey]; ok {
			continue
		} else {
			clusterSet[v.ClusterKey] = struct{}{}
		}

		res, err := r.sherlock<PERSON>li.RiskStats(ctx, v.ClusterKey)
		if err != nil {
			return nil, err
		}

		riskStatsMap[v.ClusterKey] = res
	}

	return EventSummary{
		riskStatsMap: riskStatsMap,
	}, nil
}

type EventSummary struct {
	riskStatsMap map[string]map[string][]echelper.RiskStatsItem
}

func (s EventSummary) ResourceSummary(ctx context.Context, clusterKey, namespace, resourceKind, resourceName string) (map[string]Summary, error) {
	cMap, ok := s.riskStatsMap[clusterKey]
	if !ok {
		return map[string]Summary{}, nil
	}

	key := fmt.Sprintf("%s%s(%s)", namespace, resourceName, resourceKind)

	sums := map[string]Summary{}
	for _, risk := range cMap[key] {
		sums[risk.EnKey] = Summary{
			Count:    risk.Count,
			Severity: getSeverityFromEventSeverity(risk.Severity),
			RiskType: RiskTypeDesc{
				Key:       risk.EnKey,
				DisplayZh: risk.ZhKey,
				DisplayEn: risk.EnKey,
			},
		}
	}

	return sums, nil
}

func getSeverityFromEventSeverity(severity int) Severity {
	if severity <= 3 {
		return SeverityHigh
	} else if severity <= 5 {
		return SeverityMedium
	} else if severity <= 7 {
		return SeverityLow
	} else {
		return SeverityUnknown
	}
}

func (s EventSummary) Name() string {
	return "event_reporter"
}
