package license

import (
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"errors"
	"fmt"
	"net/http"
	"os"
	"reflect"
	"regexp"
	"testing"
	"time"
	"unsafe"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/jarcoal/httpmock"
	"github.com/stretchr/testify/assert"
	"gitlab.com/piccolo_su/vegeta/pkg/util"
	"gitlab.com/security-rd/go-pkg/databases"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

func mockGorm() (*databases.RDBInstance, sqlmock.Sqlmock, error) {
	db, mock, err := sqlmock.New()
	if nil != err {
		return nil, nil, fmt.Errorf("init sqlmock failed, err: %w", err)
	}

	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		SkipInitializeWithVersion: true,
		Conn:                      db,
	}), &gorm.Config{})
	if nil != err {
		return nil, nil, fmt.Errorf("init DB with sqlmock failed, err %w", err)
	}

	rdb := &databases.RDBInstance{}
	v1 := reflect.ValueOf(rdb).Elem().FieldByName("followerDB")
	newV1 := reflect.NewAt(v1.Type(), unsafe.Pointer(v1.UnsafeAddr())).Elem()
	rv1 := reflect.ValueOf(gormDB)
	newV1.Set(rv1)

	v2 := reflect.ValueOf(rdb).Elem().FieldByName("primaryDB")
	newV2 := reflect.NewAt(v2.Type(), unsafe.Pointer(v2.UnsafeAddr())).Elem()
	rv2 := reflect.ValueOf(gormDB)
	newV2.Set(rv2)

	return rdb, mock, nil
}

var privateKey []byte

func mockRSA() {
	publicKey = []byte(`-----BEGIN RSA PUBLIC KEY-----
MIIBCgKCAQEA2YHUuqUAy8V935fbxWJ1A19lFJ0WHbV9fol5/Eu1h+cJxekFE4vW
+28aA9Nz6nZVwTuYk0DmAkebjIq18SXfq/G65a3qnLbQE1xteVKwYVnhLqBTOkiM
RmOX7VXeIV05gnClq94HRaokxLIVlqbZhkSqB5Uwhlpr1qLhcn8TB0PdN4ARCfkK
eL+nKCn1ZsmNavrrEcUuALUtjtEK113QDcv1AActTnUxBUXco2Z4+np86aU+KfYB
9khGrMIN1JZn6MV7L4BYxtl/IGU1cV625OPhjYbppD9zwAM3i4/Nx55BIcxIdmcT
344MHicA7FTYSZxiSms2G71qw69HGGNczwIDAQAB
-----END RSA PUBLIC KEY-----`)

	privateKey = []byte(`***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`)
}

func loadPrivateKey(data []byte) (*rsa.PrivateKey, error) {
	block, _ := pem.Decode(data)
	if block == nil {
		return nil, errors.New("private key not in pem format")
	}

	key, err := x509.ParsePKCS1PrivateKey(block.Bytes)
	if err != nil {
		return nil, fmt.Errorf("failed to key file: %w", err)
	}

	return key, nil
}

func TestGenerateEnvKey(t *testing.T) {
	rdb, mock, err := mockGorm()
	if err != nil {
		t.Fatal(err)
	}
	mockRSA()

	// mock http
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()
	_ = os.Setenv("CLUSTER_MANAGER_URL", "fake")
	httpmock.RegisterRegexpResponder(http.MethodGet, regexp.MustCompile(".*/internal/cluster$"),
		httpmock.NewStringResponder(200, `{"key":"fake"}`))

	mock.ExpectQuery("^SELECT").WillReturnRows(sqlmock.NewRows([]string{}))
	err = Init(rdb)
	assert.NoError(t, err)

	envKey, err := GenerateEnvKey()
	assert.NoError(t, err)
	encrypted, err := base64.StdEncoding.DecodeString(envKey)
	assert.NoError(t, err)

	key, err := loadPrivateKey(privateKey)
	assert.NoError(t, err)

	decrypted, err := rsa.DecryptPKCS1v15(rand.Reader, key, encrypted)
	assert.NoError(t, err)

	ek := envKeyInfo{}
	err = json.Unmarshal(decrypted, &ek)
	assert.NoError(t, err)
	assert.Equal(t, int64(0), ek.NodeNum)
}

func TestRefreshLicenseInfo(t *testing.T) {
	rdb, mock, err := mockGorm()
	if err != nil {
		t.Fatal(err)
	}
	mockRSA()
	// mock http
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()
	_ = os.Setenv("CLUSTER_MANAGER_URL", "fake")
	httpmock.RegisterRegexpResponder(http.MethodGet, regexp.MustCompile(".*/internal/cluster$"),
		httpmock.NewStringResponder(200, `{"key":"fake"}`))

	mock.ExpectQuery("^SELECT").WillReturnRows(sqlmock.NewRows([]string{}))
	err = Init(rdb)
	assert.NoError(t, err)

	key, err := loadPrivateKey(privateKey)
	assert.NoError(t, err)

	eigenvalue, err := manager.getEnvEigenvalue()
	assert.NoError(t, err)

	tests := []struct {
		info         Info
		expectErr    bool
		expectStatus Status
	}{
		{
			info: Info{
				SerialNo:    "fake",
				LicenseType: "fake",
				ExpireAt:    time.Now().Add(-time.Minute).Unix(),
				NodeLimit:   0,
				Module:      "fake",
				Eigenvalue:  eigenvalue,
			},
			expectErr: true,
		},
		{
			info: Info{
				SerialNo:    "fake",
				LicenseType: "fake",
				ExpireAt:    time.Now().Add(time.Minute).Unix(),
				NodeLimit:   0,
				Module:      "fake",
				Eigenvalue:  eigenvalue,
			},
			expectErr:    false,
			expectStatus: Status{Valid: true, DeadlineState: DeadlineStatusWillExpire},
		},
		{
			info: Info{
				SerialNo:    "fake",
				LicenseType: "fake",
				ExpireAt:    time.Now().Add(time.Minute).Unix(),
				NodeLimit:   0,
				Module:      "fake",
				Eigenvalue:  "fake",
			},
			expectErr: true,
		},
		{
			info: Info{
				SerialNo:    "fake",
				LicenseType: "fake",
				ExpireAt:    time.Now().Add(time.Hour * 24 * 15).Unix(),
				NodeLimit:   0,
				Module:      "fake",
				Eigenvalue:  eigenvalue,
			},
			expectErr:    false,
			expectStatus: Status{Valid: true},
		},
	}

	for _, test := range tests {
		rawSign := util.SHA256(fmt.Sprintf(signTpl, test.info.Eigenvalue, test.info.ExpireAt, test.info.LicenseType, test.info.Module, test.info.NodeLimit, test.info.SerialNo))
		sign, err := rsa.SignPKCS1v15(rand.Reader, key, crypto.SHA256, rawSign)
		assert.NoError(t, err)
		test.info.Sign = base64.StdEncoding.EncodeToString(sign)

		licenseCode, err := json.Marshal(test.info)
		assert.NoError(t, err)

		licenseCode, err = util.AesEncryptCBC(licenseCode, publicKey[31:47])
		assert.NoError(t, err)

		err = RefreshLicenseInfo(base64.StdEncoding.EncodeToString(licenseCode))
		if test.expectErr {
			assert.Error(t, err)
		} else {
			assert.NoError(t, err)
			assert.Equal(t, test.expectStatus, ValidateLicense(false))
		}
	}
}
