basePath: /
definitions:
  api.LoginResponse:
    properties:
      currentAuthority:
        type: string
      status:
        type: string
      token:
        type: string
      type:
        type: string
    type: object
  api.User:
    properties:
      avatar:
        type: string
      email:
        type: string
      group:
        type: string
      name:
        type: string
      title:
        type: string
      userid:
        type: string
      username:
        type: string
    type: object
info:
  contact: {}
  description: This is the Vegeta central server - Console
  license: {}
  title: Vegeta API
  version: "1.0"
paths:
  /api/v1/audit/config:
    get:
      description: Get audit config
      operationId: v1-audit-config-get
      produces:
      - application/json
      summary: Get audit config
    put:
      description: Update audit config
      operationId: v1-audit-config-put
      parameters:
      - description: hotStorageDays
        in: body
        name: hotStorageDays
        required: true
        schema:
          type: integer
      - description: coldStorageDays
        in: body
        name: coldStorageDays
        required: true
        schema:
          type: integer
      produces:
      - application/json
      summary: Update audit config
  /api/v1/auth/login:
    post:
      consumes:
      - application/json
      description: Login by username/password
      operationId: v1-auth-login
      parameters:
      - description: Username
        in: body
        name: username
        required: true
        schema:
          type: string
      - description: Password
        in: body
        name: password
        required: true
        schema:
          type: string
      produces:
      - application/json
      responses:
        "200":
          description: Login response
          schema:
            $ref: '#/definitions/api.LoginResponse'
      summary: Login API
  /api/v1/auth/logout:
    post:
      description: Logout
      operationId: v1-auth-logout
      produces:
      - application/json
      summary: Logout API
  /api/v1/auth/user:
    get:
      description: Get current user
      operationId: v1-auth-user
      produces:
      - application/json
      responses:
        "200":
          description: Current user
          schema:
            $ref: '#/definitions/api.User'
      summary: User API
  /api/v1/cleanup/gc:
    post:
      description: Run hot storage garbage collection
      operationId: v1-cleanup-gc-post
      parameters:
      - description: hotStorageDays
        in: body
        name: hotStorageDays
        required: true
        schema:
          type: integer
      produces:
      - application/json
      summary: Run hot storage garbage collection
  /api/v1/cleanup/gc/{gcID}:
    get:
      description: Get garbage collection task
      operationId: v1-cleanup-gctask-get
      parameters:
      - description: gcID
        in: path
        name: gcID
        required: true
        type: string
      produces:
      - application/json
      summary: Get garbage collection task
  /api/v1/cleanup/hotStorage:
    get:
      description: Get hot storage view
      operationId: v1-cleanup-hot-storage-view-get
      produces:
      - application/json
      summary: Get hot storage view
  /api/v1/config/clusters:
    get:
      description: Get all cluster information
      operationId: v1-config-cluster-get-all
      parameters:
      - description: from offset
        in: query
        name: offset
        type: integer
      - description: returned data limit
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      summary: Get all clusters information
    post:
      description: Add new cluster
      operationId: v1-config-cluster-post
      parameters:
      - description: clusterName
        in: body
        name: name
        required: true
        schema:
          type: string
      - description: 'kubeConfig -- base64String '
        in: body
        name: config
        required: true
        schema:
          type: string
      produces:
      - application/json
      summary: Add new cluster
  /api/v1/config/clusters/{clusterID}:
    delete:
      description: Delete a cluster by cluster ID
      operationId: v1-config-cluster-delete
      parameters:
      - description: clusterID
        in: path
        name: clusterID
        required: true
        type: string
      produces:
      - application/json
      summary: Delete a cluster
    get:
      description: Get single cluster information
      operationId: v1-config-cluster-get
      parameters:
      - description: clusterID
        in: path
        name: clusterID
        required: true
        type: string
      produces:
      - application/json
      summary: Get single cluster information
    put:
      description: Update single cluster information
      operationId: v1-config-cluster-put
      parameters:
      - description: clusterID
        in: path
        name: clusterID
        required: true
        type: string
      - description: clusterID
        in: body
        name: name
        required: true
        schema:
          type: string
      - description: kubeConfig -- base64String
        in: body
        name: config
        required: true
        schema:
          type: string
      produces:
      - application/json
      summary: Update single cluster information
  /api/v1/onlineVulnerabilities/current:
    get:
      description: List current online vulnerabilities
      parameters:
      - description: from offset
        in: query
        name: offset
        type: integer
      - description: returned data limit
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      summary: List current online vulnerabilities
  /api/v1/onlineVulnerabilities/details/{namespace}/{resourceKind}/{resourceName}:
    get:
      description: Get details of online vulnerabiilty
      parameters:
      - description: case-sensitive resource kind
        in: query
        name: resourceKind
        type: string
      - description: case-sensitive resource name
        in: query
        name: resourceName
        type: string
      produces:
      - application/json
      summary: Get details of online vulnerabiilty
  /api/v1/runtimeDetectionConfig/rules:
    get:
      description: List runtime detection rules
      operationId: v1-runtime-detection-rules-get
      parameters:
      - description: from offset
        in: query
        name: offset
        type: integer
      - description: returned data limit
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      summary: List runtime detection rules
  /api/v1/runtimeDetectionConfig/rules/{ruleID}/disable:
    post:
      description: Disable single rule
      operationId: v1-runtime-detection-rule-disable
      parameters:
      - description: ruleID
        in: path
        name: ruleID
        required: true
        type: string
      produces:
      - application/json
      summary: Disable single rule
  /api/v1/runtimeDetectionConfig/rules/{ruleID}/enable:
    post:
      description: Enable single rule
      operationId: v1-runtime-detection-rule-enable
      parameters:
      - description: ruleID
        in: path
        name: ruleID
        required: true
        type: string
      produces:
      - application/json
      summary: Enable single rule
  /api/v1/scanner/harbor/abortScanAll:
    post:
      description: Abort current and future scan tasks for currently running Harbor scan all job.
      summary: Abort current and future scan tasks for currently running Harbor scan all job.
  /api/v1/scanner/harbor/scanAllNow:
    post:
      description: Trigger scan of all images in Harbor.
      summary: Trigger scan of all images in Harbor.
  /api/v1/scanner/harbor/scanConfig:
    get:
      description: Get link to scan configuration screen in Harbor.
      summary: Get link to scan configuration screen in Harbor.
  /api/v1/scanner/harbor/scanStatus:
    get:
      description: Get scan all status.
      summary: Get scan all status.
  /api/v1/scanner/report/{taskID}:
    get:
      description: Get image and its vulnerabilities
      parameters:
      - description: scan task ID
        in: path
        name: taskID
        required: true
        type: string
      produces:
      - application/json
      summary: Get image and its vulnerabilities
  /api/v1/scanner/reportsByImage:
    get:
      description: List images and their vulnerabilities
      parameters:
      - description: from offset
        in: query
        name: offset
        type: integer
      - description: returned data limit
        in: query
        name: limit
        type: integer
      - description: asc/desc
        in: query
        name: sortOrder
        type: string
      - description: return only images that have only scans younger than this number; 0 or empty disables
        in: query
        name: maxImageAgeInHours
        type: integer
      - description: finishedAt/overallSeverity/repository/tag/imageDigest
        in: query
        name: sortBy
        type: string
      produces:
      - application/json
      summary: List images and their vulnerabilities
  /api/v1/scanner/reportsBySeverity:
    get:
      description: List reports by severity
      parameters:
      - description: from offset
        in: query
        name: offset
        type: integer
      - description: returned data limit
        in: query
        name: limit
        type: integer
      - description: risk explorarion filter (none(default)/medToCrit/networkBased)
        in: query
        name: riskFilter
        type: string
      - description: asc/desc
        in: query
        name: sortOrder
        type: string
      produces:
      - application/json
      summary: List reports by severity
  /api/v1/scanner/scan:
    post:
      description: Tell scanner to scan an image
      operationId: v1-scanner-scan
      parameters:
      - description: image name
        in: body
        name: image
        required: true
        schema:
          type: string
      - description: force rescan the image
        in: body
        name: rescan
        required: true
        schema:
          type: boolean
      produces:
      - application/json
      summary: Tell scanner to scan an image
  /api/v1/scanner/task/{taskID}:
    get:
      description: Get a scan task
      operationId: v1-scanner-task-get
      parameters:
      - description: scan task ID
        in: path
        name: taskID
        required: true
        type: string
      produces:
      - application/json
      summary: Get a scan task by scantask ID
  /api/v1/scap/{checkType}/{clusterID}:
    post:
      description: Run compliance check on specified cluster
      parameters:
      - description: kube/docker/host
        in: path
        name: checkType
        required: true
        type: string
      - description: cluster ID
        in: path
        name: clusterID
        required: true
        type: string
      produces:
      - application/json
      summary: Run compliance check on specified cluster
  /api/v1/scap/{checkType}/{clusterID}/cron:
    get:
      description: Get cron configured for this checkType and cluster
      parameters:
      - description: kube/docker/host
        in: path
        name: checkType
        required: true
        type: string
      - description: cluster ID
        in: path
        name: clusterID
        required: true
        type: string
      produces:
      - application/json
      summary: Get cron configured for this checkType and cluster
    put:
      description: Update cron configured for this checkType and cluster. To disable, send empty string.
      parameters:
      - description: kube/docker/host
        in: path
        name: checkType
        required: true
        type: string
      - description: cluster ID
        in: path
        name: clusterID
        required: true
        type: string
      - description: new crontab string to assign; empty string to disable
        in: body
        name: cronString
        required: true
        schema:
          type: string
      produces:
      - application/json
      summary: Update cron configured for this checkType and cluster
  /api/v1/scap/{checkType}/{clusterID}/reports:
    get:
      description: Get scap report for cluster and filter criteria
      operationId: v1-scap-job-get
      parameters:
      - description: kube/docker/host
        in: path
        name: checkType
        required: true
        type: string
      - description: cluster ID
        in: path
        name: clusterID
        required: true
        type: string
      - description: check ID
        in: query
        name: checkID
        type: string
      - description: node name
        in: query
        name: nodeName
        type: string
      - description: status (inprogress/error/completed)
        in: query
        name: status
        type: string
      produces:
      - application/json
      summary: Get scap reports
  /api/v1/scap/{checkType}/{nodeName}/{checkID}/details:
    get:
      description: Get node check details
      operationId: v1-node-check-details-get
      parameters:
      - description: kube/docker/host
        in: path
        name: checkType
        required: true
        type: string
      - description: nodeName
        in: path
        name: nodeName
        required: true
        type: string
      - description: checkID
        in: path
        name: checkID
        required: true
        type: string
      produces:
      - application/json
      summary: Get node check details
  /api/v1/scap/{checkType}/breakdown/{checkID}:
    get:
      description: Get scap job breakdown
      operationId: v1-scap-job-breakdown
      parameters:
      - description: kube/docker/host
        in: path
        name: checkType
        required: true
        type: string
      - description: check ID
        in: path
        name: checkID
        required: true
        type: string
      - description: policy number
        in: query
        name: policyNumber
        type: string
      - description: from offset
        in: query
        name: offset
        type: integer
      - description: returned data limit
        in: query
        name: limit
        type: integer
      - description: asc/desc
        in: query
        name: sortOrder
        type: string
      - description: policyNumber/name/numFailed/numSuccessful/numInfo/numWarn
        in: query
        name: sortBy
        type: string
      produces:
      - application/json
      summary: Get scap job breakdown
  /api/v1/scap/{checkType}/breakdown/{checkID}/{policyNumber}/details:
    get:
      description: Get scap job policy
      operationId: v1-scap-job-policy
      parameters:
      - description: kube/docker/host
        in: path
        name: checkType
        required: true
        type: string
      - description: check ID
        in: path
        name: checkID
        required: true
        type: string
      - description: policy number
        in: path
        name: policyNumber
        required: true
        type: string
      produces:
      - application/json
      summary: Get scap job policy
  /api/v1/scap/{checkType}/history:
    get:
      description: Get scap history
      operationId: v1-scap-history
      parameters:
      - description: kube/docker/host
        in: path
        name: checkType
        required: true
        type: string
      - description: checkID
        in: query
        name: checkID
        type: string
      - description: clusterID
        in: query
        name: clusterID
        type: string
      - description: from offset
        in: query
        name: offset
        type: integer
      - description: returned data limit
        in: query
        name: limit
        type: integer
      - description: asc/desc
        in: query
        name: sortOrder
        type: string
      - description: createdAt/finishedAt/checkID/clusterID/numSuccessful/numFailed/numError/numWaiting/numInconclusive
        in: query
        name: sortBy
        type: string
      produces:
      - application/json
      summary: Get scap history
  /api/v1/scap/crons:
    get:
      description: List all crons (for all check types and clusters)
      produces:
      - application/json
      summary: List all crons
swagger: "2.0"
