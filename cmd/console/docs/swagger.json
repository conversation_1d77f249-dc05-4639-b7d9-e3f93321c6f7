{"swagger": "2.0", "info": {"description": "This is the Vegeta central server - Console", "title": "Vegeta API", "contact": {}, "license": {}, "version": "1.0"}, "basePath": "/", "paths": {"/api/v1/audit/config": {"get": {"description": "Get audit config", "produces": ["application/json"], "summary": "Get audit config", "operationId": "v1-audit-config-get"}, "put": {"description": "Update audit config", "produces": ["application/json"], "summary": "Update audit config", "operationId": "v1-audit-config-put", "parameters": [{"description": "hotStorageDays", "name": "hotStorageDays", "in": "body", "required": true, "schema": {"type": "integer"}}, {"description": "coldStorageDays", "name": "coldStorageDays", "in": "body", "required": true, "schema": {"type": "integer"}}]}}, "/api/v1/auth/login": {"post": {"description": "Login by username/password", "consumes": ["application/json"], "produces": ["application/json"], "summary": "Login API", "operationId": "v1-auth-login", "parameters": [{"description": "Username", "name": "username", "in": "body", "required": true, "schema": {"type": "string"}}, {"description": "Password", "name": "password", "in": "body", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Login response", "schema": {"$ref": "#/definitions/api.LoginResponse"}}}}}, "/api/v1/auth/logout": {"post": {"description": "Logout", "produces": ["application/json"], "summary": "Logout API", "operationId": "v1-auth-logout"}}, "/api/v1/auth/user": {"get": {"description": "Get current user", "produces": ["application/json"], "summary": "User API", "operationId": "v1-auth-user", "responses": {"200": {"description": "Current user", "schema": {"$ref": "#/definitions/api.User"}}}}}, "/api/v1/cleanup/gc": {"post": {"description": "Run hot storage garbage collection", "produces": ["application/json"], "summary": "Run hot storage garbage collection", "operationId": "v1-cleanup-gc-post", "parameters": [{"description": "hotStorageDays", "name": "hotStorageDays", "in": "body", "required": true, "schema": {"type": "integer"}}]}}, "/api/v1/cleanup/gc/{gcID}": {"get": {"description": "Get garbage collection task", "produces": ["application/json"], "summary": "Get garbage collection task", "operationId": "v1-cleanup-gctask-get", "parameters": [{"type": "string", "description": "gcID", "name": "gcID", "in": "path", "required": true}]}}, "/api/v1/cleanup/hotStorage": {"get": {"description": "Get hot storage view", "produces": ["application/json"], "summary": "Get hot storage view", "operationId": "v1-cleanup-hot-storage-view-get"}}, "/api/v1/config/clusters": {"get": {"description": "Get all cluster information", "produces": ["application/json"], "summary": "Get all clusters information", "operationId": "v1-config-cluster-get-all", "parameters": [{"type": "integer", "description": "from offset", "name": "offset", "in": "query"}, {"type": "integer", "description": "returned data limit", "name": "limit", "in": "query"}]}, "post": {"description": "Add new cluster", "produces": ["application/json"], "summary": "Add new cluster", "operationId": "v1-config-cluster-post", "parameters": [{"description": "clusterName", "name": "name", "in": "body", "required": true, "schema": {"type": "string"}}, {"description": "kubeConfig -- base64String ", "name": "config", "in": "body", "required": true, "schema": {"type": "string"}}]}}, "/api/v1/config/clusters/{clusterID}": {"get": {"description": "Get single cluster information", "produces": ["application/json"], "summary": "Get single cluster information", "operationId": "v1-config-cluster-get", "parameters": [{"type": "string", "description": "clusterID", "name": "clusterID", "in": "path", "required": true}]}, "put": {"description": "Update single cluster information", "produces": ["application/json"], "summary": "Update single cluster information", "operationId": "v1-config-cluster-put", "parameters": [{"type": "string", "description": "clusterID", "name": "clusterID", "in": "path", "required": true}, {"description": "clusterID", "name": "name", "in": "body", "required": true, "schema": {"type": "string"}}, {"description": "kubeConfig -- base64String", "name": "config", "in": "body", "required": true, "schema": {"type": "string"}}]}, "delete": {"description": "Delete a cluster by cluster ID", "produces": ["application/json"], "summary": "Delete a cluster", "operationId": "v1-config-cluster-delete", "parameters": [{"type": "string", "description": "clusterID", "name": "clusterID", "in": "path", "required": true}]}}, "/api/v1/onlineVulnerabilities/current": {"get": {"description": "List current online vulnerabilities", "produces": ["application/json"], "summary": "List current online vulnerabilities", "parameters": [{"type": "integer", "description": "from offset", "name": "offset", "in": "query"}, {"type": "integer", "description": "returned data limit", "name": "limit", "in": "query"}]}}, "/api/v1/onlineVulnerabilities/details/{namespace}/{resourceKind}/{resourceName}": {"get": {"description": "Get details of online vulnerabiilty", "produces": ["application/json"], "summary": "Get details of online vulnerabiilty", "parameters": [{"type": "string", "description": "case-sensitive resource kind", "name": "resourceKind", "in": "query"}, {"type": "string", "description": "case-sensitive resource name", "name": "resourceName", "in": "query"}]}}, "/api/v1/runtimeDetectionConfig/rules": {"get": {"description": "List runtime detection rules", "produces": ["application/json"], "summary": "List runtime detection rules", "operationId": "v1-runtime-detection-rules-get", "parameters": [{"type": "integer", "description": "from offset", "name": "offset", "in": "query"}, {"type": "integer", "description": "returned data limit", "name": "limit", "in": "query"}]}}, "/api/v1/runtimeDetectionConfig/rules/{ruleID}/disable": {"post": {"description": "Disable single rule", "produces": ["application/json"], "summary": "Disable single rule", "operationId": "v1-runtime-detection-rule-disable", "parameters": [{"type": "string", "description": "ruleID", "name": "ruleID", "in": "path", "required": true}]}}, "/api/v1/runtimeDetectionConfig/rules/{ruleID}/enable": {"post": {"description": "Enable single rule", "produces": ["application/json"], "summary": "Enable single rule", "operationId": "v1-runtime-detection-rule-enable", "parameters": [{"type": "string", "description": "ruleID", "name": "ruleID", "in": "path", "required": true}]}}, "/api/v1/scanner/harbor/abortScanAll": {"post": {"description": "Abort current and future scan tasks for currently running Harbor scan all job.", "summary": "Abort current and future scan tasks for currently running Harbor scan all job."}}, "/api/v1/scanner/harbor/scanAllNow": {"post": {"description": "Trigger scan of all images in Harbor.", "summary": "Trigger scan of all images in Harbor."}}, "/api/v1/scanner/harbor/scanConfig": {"get": {"description": "Get link to scan configuration screen in Harbor.", "summary": "Get link to scan configuration screen in Harbor."}}, "/api/v1/scanner/harbor/scanStatus": {"get": {"description": "Get scan all status.", "summary": "Get scan all status."}}, "/api/v1/scanner/report/{taskID}": {"get": {"description": "Get image and its vulnerabilities", "produces": ["application/json"], "summary": "Get image and its vulnerabilities", "parameters": [{"type": "string", "description": "scan task ID", "name": "taskID", "in": "path", "required": true}]}}, "/api/v1/scanner/reportsByImage": {"get": {"description": "List images and their vulnerabilities", "produces": ["application/json"], "summary": "List images and their vulnerabilities", "parameters": [{"type": "integer", "description": "from offset", "name": "offset", "in": "query"}, {"type": "integer", "description": "returned data limit", "name": "limit", "in": "query"}, {"type": "string", "description": "asc/desc", "name": "sortOrder", "in": "query"}, {"type": "integer", "description": "return only images that have only scans younger than this number; 0 or empty disables", "name": "maxImageAgeInHours", "in": "query"}, {"type": "string", "description": "finishedAt/overallSeverity/repository/tag/imageDigest", "name": "sortBy", "in": "query"}]}}, "/api/v1/scanner/reportsBySeverity": {"get": {"description": "List reports by severity", "produces": ["application/json"], "summary": "List reports by severity", "parameters": [{"type": "integer", "description": "from offset", "name": "offset", "in": "query"}, {"type": "integer", "description": "returned data limit", "name": "limit", "in": "query"}, {"type": "string", "description": "risk explorarion filter (none(default)/medToCrit/networkBased)", "name": "riskFilter", "in": "query"}, {"type": "string", "description": "asc/desc", "name": "sortOrder", "in": "query"}]}}, "/api/v1/scanner/scan": {"post": {"description": "Tell scanner to scan an image", "produces": ["application/json"], "summary": "Tell scanner to scan an image", "operationId": "v1-scanner-scan", "parameters": [{"description": "image name", "name": "image", "in": "body", "required": true, "schema": {"type": "string"}}, {"description": "force rescan the image", "name": "rescan", "in": "body", "required": true, "schema": {"type": "boolean"}}]}}, "/api/v1/scanner/task/{taskID}": {"get": {"description": "Get a scan task", "produces": ["application/json"], "summary": "Get a scan task by scantask ID", "operationId": "v1-scanner-task-get", "parameters": [{"type": "string", "description": "scan task ID", "name": "taskID", "in": "path", "required": true}]}}, "/api/v1/scap/crons": {"get": {"description": "List all crons (for all check types and clusters)", "produces": ["application/json"], "summary": "List all crons"}}, "/api/v1/scap/{checkType}/breakdown/{checkID}": {"get": {"description": "Get scap job breakdown", "produces": ["application/json"], "summary": "Get scap job breakdown", "operationId": "v1-scap-job-breakdown", "parameters": [{"type": "string", "description": "kube/docker/host", "name": "checkType", "in": "path", "required": true}, {"type": "string", "description": "check ID", "name": "checkID", "in": "path", "required": true}, {"type": "string", "description": "policy number", "name": "policyNumber", "in": "query"}, {"type": "integer", "description": "from offset", "name": "offset", "in": "query"}, {"type": "integer", "description": "returned data limit", "name": "limit", "in": "query"}, {"type": "string", "description": "asc/desc", "name": "sortOrder", "in": "query"}, {"type": "string", "description": "policyNumber/name/numFailed/numSuccessful/numInfo/numWarn", "name": "sortBy", "in": "query"}]}}, "/api/v1/scap/{checkType}/breakdown/{checkID}/{policyNumber}/details": {"get": {"description": "Get scap job policy", "produces": ["application/json"], "summary": "Get scap job policy", "operationId": "v1-scap-job-policy", "parameters": [{"type": "string", "description": "kube/docker/host", "name": "checkType", "in": "path", "required": true}, {"type": "string", "description": "check ID", "name": "checkID", "in": "path", "required": true}, {"type": "string", "description": "policy number", "name": "policyNumber", "in": "path", "required": true}]}}, "/api/v1/scap/{checkType}/history": {"get": {"description": "Get scap history", "produces": ["application/json"], "summary": "Get scap history", "operationId": "v1-scap-history", "parameters": [{"type": "string", "description": "kube/docker/host", "name": "checkType", "in": "path", "required": true}, {"type": "string", "description": "checkID", "name": "checkID", "in": "query"}, {"type": "string", "description": "clusterID", "name": "clusterID", "in": "query"}, {"type": "integer", "description": "from offset", "name": "offset", "in": "query"}, {"type": "integer", "description": "returned data limit", "name": "limit", "in": "query"}, {"type": "string", "description": "asc/desc", "name": "sortOrder", "in": "query"}, {"type": "string", "description": "createdAt/finishedAt/checkID/clusterID/numSuccessful/numFailed/numError/numWaiting/numInconclusive", "name": "sortBy", "in": "query"}]}}, "/api/v1/scap/{checkType}/{clusterID}": {"post": {"description": "Run compliance check on specified cluster", "produces": ["application/json"], "summary": "Run compliance check on specified cluster", "parameters": [{"type": "string", "description": "kube/docker/host", "name": "checkType", "in": "path", "required": true}, {"type": "string", "description": "cluster ID", "name": "clusterID", "in": "path", "required": true}]}}, "/api/v1/scap/{checkType}/{clusterID}/cron": {"get": {"description": "Get cron configured for this checkType and cluster", "produces": ["application/json"], "summary": "Get cron configured for this checkType and cluster", "parameters": [{"type": "string", "description": "kube/docker/host", "name": "checkType", "in": "path", "required": true}, {"type": "string", "description": "cluster ID", "name": "clusterID", "in": "path", "required": true}]}, "put": {"description": "Update cron configured for this checkType and cluster. To disable, send empty string.", "produces": ["application/json"], "summary": "Update cron configured for this checkType and cluster", "parameters": [{"type": "string", "description": "kube/docker/host", "name": "checkType", "in": "path", "required": true}, {"type": "string", "description": "cluster ID", "name": "clusterID", "in": "path", "required": true}, {"description": "new crontab string to assign; empty string to disable", "name": "cronString", "in": "body", "required": true, "schema": {"type": "string"}}]}}, "/api/v1/scap/{checkType}/{clusterID}/reports": {"get": {"description": "Get scap report for cluster and filter criteria", "produces": ["application/json"], "summary": "Get scap reports", "operationId": "v1-scap-job-get", "parameters": [{"type": "string", "description": "kube/docker/host", "name": "checkType", "in": "path", "required": true}, {"type": "string", "description": "cluster ID", "name": "clusterID", "in": "path", "required": true}, {"type": "string", "description": "check ID", "name": "checkID", "in": "query"}, {"type": "string", "description": "node name", "name": "nodeName", "in": "query"}, {"type": "string", "description": "status (inprogress/error/completed)", "name": "status", "in": "query"}]}}, "/api/v1/scap/{checkType}/{nodeName}/{checkID}/details": {"get": {"description": "Get node check details", "produces": ["application/json"], "summary": "Get node check details", "operationId": "v1-node-check-details-get", "parameters": [{"type": "string", "description": "kube/docker/host", "name": "checkType", "in": "path", "required": true}, {"type": "string", "description": "nodeName", "name": "nodeName", "in": "path", "required": true}, {"type": "string", "description": "checkID", "name": "checkID", "in": "path", "required": true}]}}}, "definitions": {"api.LoginResponse": {"type": "object", "properties": {"currentAuthority": {"type": "string"}, "status": {"type": "string"}, "token": {"type": "string"}, "type": {"type": "string"}}}, "api.User": {"type": "object", "properties": {"avatar": {"type": "string"}, "email": {"type": "string"}, "group": {"type": "string"}, "name": {"type": "string"}, "title": {"type": "string"}, "userid": {"type": "string"}, "username": {"type": "string"}}}}}