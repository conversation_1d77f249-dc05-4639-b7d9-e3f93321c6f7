package consts

const (
	Svn           = "Svn"
	TFS           = "TFS"
	Gerrit        = "Gerrit"
	Mercurial     = "Mercurial"
	Coding        = "Coding"
	Artifactory   = "Artifactory"
	Firefly       = "Firefly"
	DefaultExpire = "2099-01-01 00:00:00"

	ProjectUrlHeadHttps       = 0
	ProjectGitAuthMethodToken = 1
	SourceCheckPullWayToken   = 2
	DefaultScanTimeout        = 60 * 20
	DefaultBatchSize          = 10
)

const (
	ModelSourceCheck  = "sourceCheck"
	ModelCodesec      = "codesec"
	ModelGetScanFaild = "getScanFaild"
)

const (
	ProjectGitTypeGitLab = 1
	ProjectGitTypeGitHub = 2
	ProjectGitTypeGitee  = 3
	GitTypeGit           = "git"
	GitlabApiVersionV3   = "V3"
	GitlabApiVersionV4   = "V4"

	SourceCheckProjectTypeGit = "1"
)

const (
	DuplicateKey    = "Duplicate"
	PortalAdminName = "portalAdmin"
)

const SourceCheckStatusOK = 1

const (
	ScanStatusNotScan  = "not_scan"
	ScanStatusScanning = "scanning"
	ScanStatusSuccess  = "success"
	ScanStatusFailed   = "failed"
	ScanSuccessMsg     = "成功"
	ScanFinishMsg      = "完成"
	ScanFailedMsg      = "失败"
	ScanTerMsg         = "中止"
)

const (
	ScanFailedTimeout         = "扫描超时"
	ScanFailedCantGetProgress = "无法获取扫描进度"
	ScanFailedCantGetResult   = "无法获取扫描结果"
)
