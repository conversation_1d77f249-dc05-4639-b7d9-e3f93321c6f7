# 项目风险级别字段更新逻辑修正

## 问题描述

原有的项目风险级别（RiskLevel）字段更新逻辑存在以下问题：

1. **更新逻辑混乱**：RiskLevel 的计算逻辑在 `Project.Serialize()` 方法中，但该方法在多个地方被调用，导致逻辑不清晰
2. **字段更新不一致**：`scanSuccessUpdater()` 函数只更新了部分字段，没有更新 RiskLevel
3. **缺少统一的风险级别计算**：没有统一的方法来计算和更新项目的整体风险级别

## 修正方案

### 1. 重构 Project.Serialize() 方法

**修改文件**: `cmd/portal/model/project.go`

- 将 RiskLevel 计算逻辑从 `Serialize()` 方法中移除
- 新增 `UpdateRiskLevel()` 方法专门用于计算和更新风险级别
- 考虑了超危级别（CRITICAL）的情况

```go
// 原来的 Serialize() 方法包含风险级别计算
// 现在分离为独立的 UpdateRiskLevel() 方法
func (vi *Project) UpdateRiskLevel() {
    // 考虑超危级别（CRITICAL）
    if vi.CodesecHighSeverity >= SeverityCriticalInt || vi.SourceCheckHighSeverity >= SeverityCriticalInt {
        vi.RiskLevel = SeverityCritical
    } else if vi.CodesecHighSeverity >= SeverityHighInt || vi.SourceCheckHighSeverity >= SeverityHighInt {
        vi.RiskLevel = SeverityHigh
    } else if vi.CodesecHighSeverity >= SeverityMediumInt || vi.SourceCheckHighSeverity >= SeverityMediumInt {
        vi.RiskLevel = SeverityMedium
    } else if vi.CodesecHighSeverity >= SeverityLowInt || vi.SourceCheckHighSeverity >= SeverityLowInt {
        vi.RiskLevel = SeverityLow
    } else {
        vi.RiskLevel = SeverityUnknown
    }
}
```

### 2. 修正扫描成功后的更新逻辑

**修改文件**: `cmd/portal/service/project2.go`

#### 2.1 新增风险级别计算函数

```go
// calculateProjectRiskLevel 计算项目整体风险级别
func calculateProjectRiskLevel(codesecSeverity, sourceCheckSeverity int64) string {
    // 考虑超危级别（CRITICAL）
    if codesecSeverity >= model.SeverityCriticalInt || sourceCheckSeverity >= model.SeverityCriticalInt {
        return model.SeverityCritical
    } else if codesecSeverity >= model.SeverityHighInt || sourceCheckSeverity >= model.SeverityHighInt {
        return model.SeverityHigh
    } else if codesecSeverity >= model.SeverityMediumInt || sourceCheckSeverity >= model.SeverityMediumInt {
        return model.SeverityMedium
    } else if codesecSeverity >= model.SeverityLowInt || sourceCheckSeverity >= model.SeverityLowInt {
        return model.SeverityLow
    } else {
        return model.SeverityUnknown
    }
}
```

#### 2.2 修正 updateProjectCodesec 方法

在 CodeSec 扫描成功后，同时更新项目整体风险级别：

```go
updater := scanSuccessUpdater(consts.ModelCodesec, sev, risk)

// 计算并更新项目整体风险级别
currentSourceCheckSeverity := pro.SourceCheckHighSeverity
projectRiskLevel := calculateProjectRiskLevel(risk, currentSourceCheckSeverity)
updater["risk_level"] = projectRiskLevel
```

#### 2.3 修正 updateProjectSourceCheck 方法

在 SourceCheck 扫描成功后，同时更新项目整体风险级别：

```go
updater := scanSuccessUpdater(consts.ModelSourceCheck, sev, risk)

// 计算并更新项目整体风险级别
currentCodesecSeverity := pro.CodesecHighSeverity
projectRiskLevel := calculateProjectRiskLevel(currentCodesecSeverity, risk)
updater["risk_level"] = projectRiskLevel
```

### 3. 优化其他更新函数

#### 3.1 scanFailedUpdater 函数

扫描失败时不重置风险级别相关字段，保持之前的扫描结果：

```go
func scanFailedUpdater(scanType string, err error) map[string]interface{} {
    // ...
    // 注意：扫描失败时不重置风险级别相关字段，保持之前的扫描结果
}
```

#### 3.2 scanStarUpdater 函数

开始扫描时不重置风险级别相关字段，保持之前的扫描结果：

```go
func scanStarUpdater(scanType string) map[string]interface{} {
    // ...
    // 注意：开始扫描时不重置风险级别相关字段，保持之前的扫描结果
}
```

### 4. 新增统一更新方法

新增 `updateProjectRiskLevel` 方法，用于统一更新项目的整体风险级别：

```go
// updateProjectRiskLevel 统一更新项目的整体风险级别
func (s *projectService) updateProjectRiskLevel(ctx context.Context, projectID int64) error {
    // 获取项目信息
    // 计算新的风险级别
    // 只有风险级别发生变化时才更新
}
```

## 修正效果

1. **逻辑清晰**：风险级别的计算和更新逻辑更加清晰，职责分离
2. **数据一致性**：确保扫描成功后项目的整体风险级别能够正确更新
3. **性能优化**：只有风险级别发生变化时才进行数据库更新
4. **扩展性好**：新增的方法可以在需要时被其他地方调用

## 风险级别计算规则

项目整体风险级别按以下规则计算：

1. **超危（CRITICAL）**：任一组件扫描结果达到超危级别
2. **高危（HIGH）**：任一组件扫描结果达到高危级别
3. **中危（MEDIUM）**：任一组件扫描结果达到中危级别
4. **低危（LOW）**：任一组件扫描结果达到低危级别
5. **未知（UNKNOWN）**：未扫描或未扫描到任何结果的项目

## 注意事项

1. 扫描开始时不重置风险级别，保持之前的扫描结果
2. 扫描失败时不重置风险级别，保持之前的扫描结果
3. 只有扫描成功时才更新风险级别
4. 考虑了超危级别的情况，与现有的风险级别常量保持一致
