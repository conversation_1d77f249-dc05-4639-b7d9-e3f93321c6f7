




公司概况

公司简介
SecZone开源网安成立于2013年5月，是国内软件安全行业创领者和领先的软件安全开发生命周期（S-SDLC）解决方案提供商，专注于软件安全领域的技术研究。SecZone开源网安团队由来自思科、微软、惠普、Google、华为等行业顶级的安全专家组成，团队成员从业经验均为10年以上。SecZone开源网安总部在深圳，同时在北京、广州、武汉、合肥、成都、南宁设有分支机构。
SecZone开源网安始终以自主创新为发展源动力，以S-SDLC解决方案为核心，以S-SDLC平台为载体，向不同行业的客户提供覆盖软件开发全生命周期的软件安全开发咨询和落地服务，包括但不限于安全开发培训、安全需求识别、安全架构设计、安全代码实现、安全确认、安全审核及安全运营的完整业务生态，同时提供配套的工具链支持。帮助客户提升软件安全开发能力，构建安全可靠的软件产品。
未来，SecZone开源网安将持续聚焦软件安全领域，努力成为全球软件安全领域极具竞争力的领导品牌。
公司愿景
让企业交付更安全的软件
核心价值
专业、开源、信赖
联系方式
电话:4000-983-183
网址:www.seczone.cn 
邮箱:<EMAIL>
微信公众号


1版本变更记录

序号	发布日期	版本号	说明
1	2022/4/30	V1.0	初始创建
2	2022/5/30	V1.0.1	增加组件的无漏洞版本
3	2022/7/13	V1.0.2	增加调用数据中心获取组件信息
4	2022/8/24	V1.0.3	普通应用检测、仓库应用检测，增加callBackUrl入参；
项目详情-组件信息，修改过滤条件；
应用详情-组件信息，修改过滤条件
5	2022/11/16	V1.0.4	修改组件详情；
修改项目/应用下的组件列表；
增加应用下组件修复建议接口
增加查询应用的cycloneDX清单的接口
6	2022/12/1	V1.0.5	漏洞-漏洞详情，增加出参；
预警-漏洞预警查询，入参属性调整；
应用-应用列表添加detectionBranch 分支信息返回字段；
应用-组件/应用下的组件列表新添controlStatusName黑白名单中文返回字段，依赖字段说明，projectWhiteControlStatus黑名单是否豁免
7	2023/1/4	V1.0.6	预警-漏洞预警查询接口增加0day出参；
项目/应用下的组件列表接口、资产组件列表接口、组讲详情接口，增加所属国家的返回字段
8	2023/1/13	V1.0.7	项目/应用下的组件列表接口增加组件类型的返回字段；
新增获取项目成员的接口
9	2023/4/7	V1.0.8	新增私服列表、私服组件列表、私服漏洞列表、私服许可列表的接口；
项目、应用报告生成接口增加出参；
报告下载修改入参；
代码仓库添加接口增加过滤字段；
新增许可接口；
组件详情接口，返回参数增加组件完整性和许可完整性字段；
增加组件使用建议接口；
增加组件依赖关系接口；
代码仓库添加接口新增detectionTags字段
新增报告生成状态查询接口
私服组件列表接口出参增加黑名单
10	2023/6/15	V3.0.5.1	3.29应用--应用检测进度添加检开始，结束时间和检测时长3个字段
11	2023/9/5	V3.1.2	新增3.80-3.87接口，更新导出报告接口字段
12	2023/9/12	V3.1.2	新增3.88-3.90接口，知识库组件漏洞支持
13	2023/10/20	V3.1.4	新增3.91 - 3.93接口，允许对应用组件进行标记
修改 3.33 接口，新增remarks的模糊搜索
14	2023/11/06	V3.2.0	新增3.94接口，检测历史报告导出
修改 3.33 接口，remarks 改为remarkTypes 的模糊搜索
修改3.91 接口，remark 改为 remarkType 
15	2023/12/28	V3.2.2	优化3.18接口描述信息
16	2024/2/5	V3.2.3	3.94接口增加注意事项
优化3.42 增加影响组件数返回参数
17	2024/07/08	V3.2.7	新增3.95, 3.96接口
18	2024/07/18	V3.2.7.1	3.26新增返回字段:版权信息
3.37新增返回字段:所属组织、所属软件、开发者、组件描述、投毒组件标识
3.33新增返回字段:投毒组件标识
19	2024/7/22	V3.2.7.1	3.97 应用--应用下的敏感信息
20	2024/7/23	V3.2.7.1	 3.79接口  新增请求字段 spdxVersion(SPDX版本)
21	2024/8/16	V3.5.2	3.14、3.19接口新增返回参数errorUuidList
3.22接口参数accessToken描述变更；
3.23.1接口Content-Type改为application/x-www-form-urlencoded，请求正文示例变更；
3.44接口描述超管改为企业管理员，请求参数state描述变更，返回参数appName描述变更，新增sourceType字段的字典值为APPMAINSOURCE；
3.57、3.58、3.59、3.60、3.83、3.84、3.85接口已废弃；
22	2024/8/28	V3.5.2	3.46接口变更





目录
1	概述	8
2	接口清单	9
3	接口详细	12
3.1认证--创建token	13
3.2认证--刷新token	14
3.3认证--获取token列表	15
3.4认证--jwt方式获取token	16
3.5用户--新增用户	17
3.6用户--获取用户列表	18
3.7用户--用户删除	19
3.8用户--用户修改	19
3.9项目--普通项目添加	21
3.10项目--项目列表	21
3.11项目--项目删除	23
3.12项目--项目修改	23
3.13项目--项目详情	24
3.14项目--项目成员增加	25
3.15项目--项目相关统计	26
3.16项目--项目报告生成	27
3.17项目--获取项目/应用权限列表	28
3.18项目--获取用户列表(增加项目/应用成员时使用)	29
3.19项目--项目成员删除	30
3.20应用--普通应用添加	31
3.21应用--本地上传检测	31
3.22应用--代码仓库添加	33
3.23应用--仓库应用检测	36
3.23.1应用--仓库应用检测	37
3.24应用--应用列表	38
3.25应用—应用删除	40
3.26应用--应用详情	40
3.27应用--应用修改	41
3.28应用--应用分享	43
3.29应用--应用检测进度	44
3.30应用--应用相关统计	45
3.31应用--应用报告生成	46
3.32应用--应用阻断列表	46
3.33应用--项目/应用下的组件列表	47
3.34应用--应用下的漏洞列表	51
3.35应用--应用下的许可列表	51
3.36应用--获取资产组件列表	52
3.37应用--组件详情	53
3.38组件--获取组件的所有版本	56
3.39组件--获取组件的所有漏洞	57
3.40许可--许可列表	59
3.41许可--许可详情	60
3.42漏洞--漏洞列表	62
3.43漏洞--漏洞详情	64
3.44检测任务--检测任务列表	67
3.45报告--报告列表查询	68
3.46报告--报告删除	69
3.47报告--报告下载	70
3.48定时任务--定时任务列表	70
3.49定时任务--定时任务设置	72
3.50定时任务--定时任务开启/关闭	73
3.51预警--漏洞预警查询	74
3.52预警--站内信查询	76
3.53版本--产品版本信息	77
3.54字典--通过字典编码获取字典	77
3.55调用数据中心获取组件信息	78
3.56获取ca许可的详细信息	81
3.57容器--创建容器检测	82
3.58容器--容器列表	83
3.59容器--容器删除	85
3.60容器--获取容器的详细信息	85
3.61获取应用下的检测历史	86
3.62应用--应用下组件修复建议	87
3.63项目--获取项目成员列表	89
3.64私服--获取私服列表	91
3.65私服--获取私服组件列表	93
3.66私服--获取私服漏洞列表	96
3.67私服--获取私服许可列表	98
3.68Ca许可--获取机器码	99
3.69Ca许可--Ca文件上传	100
3.70Ca许可--查询Ca许可状态	101
3.71应用--组件使用建议	101
3.72应用--组件依赖关系列表查询	103
3.73报告--报告生成状态查询	104
3.74应用--生成应用-应用安全报告PDF格式	105
3.75应用--生成应用-应用安全报告DOC格式	107
3.76应用—生成应用-组件清单报告	108
3.77应用—生成应用-漏洞清单报告	110
3.78应用—生成应用-许可清单报告	112
3.79应用—生成应用-软件物料清单SBOM报告	113
3.80应用—源码溯源获取相关统计信息	114
3.81应用—源码溯源文件分布	114
3.82应用—源码溯源组件文件列表信息	115
3.83容器—容器下组件列表	116
3.84容器—容器下漏洞列表	118
3.85容器—容器下许可列表	119
3.86应用—批量删除应用	121
3.87报告--获取报告导出配置列表	121
3.88知识库--漏洞详情	124
3.89知识库--组件模糊查询	127
3.90知识库--组件详情查询	128
3.91 应用--组件添加标记	131
3.92 应用--组件取消标记	131
3.93 应用--沿用上次标签添加适配的检测结果	132
3.94 应用—检测历史报告导出	133
3.95 资产--组件影响的应用	134
3.96 资产--组件详情导出报告	135




REST API 接口说明书
2概述
SourceCheck基于"应用","服务器","攻击事件"和"用户"提供了对应的 REST API 接口，调用方按照该REST API 文档进行对应的业务操作来获取 SourceCheck平台检测到的数据。同样为了保证接口的安全性，当您调用SourceCheck提供的 API 进行相关操作时，我们需要 API 访问凭证来确保您的访问是合法的。那么，如何在调用 API 的时候设置 API 访问凭证呢？
如何获取访问授权（token）？
1.调用获取token列表获取token
2.登录SourceCheck平台，进入菜单【设置】-【个人中心】-【私人令牌】界面

如何设置token进行接口调用？
1.token放入请求头中,参数为Authorization,对应值为“BASIC-API:私人令牌”

如何通过url进行接口调用？
通过访问http://{ip:端口}/{接口路径}或者https://{ip:端口}/{接口路径}，例：http://**********:30000/sca/v1/tokens

3接口清单 

序号	分类	描述	new API
1	认证	创建token	POST  /sca/v1/tokens 
2		刷新token	PUT   /sca/v1/tokens
3		获取token列表	POST  /sca/v1/tokens/query
4		jwt方式获取token	POST  /sca/v1/jwt/token
5	用户	新增用户	POST  /sca/v1/users
6		获取用户列表	GET   /sca/v1/users?pageIndex=xx&pageSize=yy
7		用户删除	DELETE /sca/v1/users/{userUuid}
8		用户修改	PUT   /sca/v1/users
9	项目	普通项目添加	POST  /sca/v1/projects/local
10		项目列表	GET   /sca/v1/projects?pageIndex=xx&pageSize=yy
11		项目删除	DELETE /sca/v1/projects/{projectUuid}
12		项目修改	PUT   /sca/v1/projects
13		项目详情	GET	  /sca/v1/project/{projectUuid}
14		项目成员增加	POST  /sca/v1/projects/users
15		项目相关统计	GET   /sca/v1/statistics/projects/{projectUuid}
16		项目报告生成	POST  /sca/v1/projects/files/create
17		获取项目/应用权限列表	GET   /sca/v1/auths
18		获取用户列表(增加项目成员时使用)	GET   /sca/v1/projects/users
19		项目成员删除	DELETE /sca/v1/projects/users
20	应用	普通应用添加	POST  /sca/v1/applications/local
21		普通应用检测	POST  /sca/v1/applications/local/detection
22		仓库应用添加	POST  /sca/v1/applications/repository
23		仓库应用检测	 GET  /sca/v1/applications/repositorys/detection/{appUuid}
24		应用列表	 GET  /sca/v1/applications?pageIndex=xx&pageSize=yy
25		应用删除	DELETE /sca/v1/applications/{appUuid}
26		应用详情	GET   /sca/v1/applications/{appUuid}
27		应用修改	PUT   /sca/v1/applications
28		应用分享	POST  /sca/v1/applications/user
29		应用检测进度	GET   /sca/v1/applications/progress/{checkNo}
30		应用相关统计	GET   /sca/v1/statistics/applications/{appUuid}
31		应用报告生成	GET   /sca/v1/applications/file/create/{appUuid}
32		应用阻断列表	POST
/sca/v1/applications/blocks/query
33		应用下的组件列表	GET   /sca/v1/applications/components
34		应用下的漏洞列表	GET   /sca/v1/vulnerabilities?pageIndex=xx&pageSize=yy
35		应用下的许可列表	GET   /sca/v1/licenses?pageIndex=xx&pageSize=yy
36	组件	获取资产组件列表	GET  /sca/v1/components?pageIndex=xx&pageSize=yy
37		组件详情	GET   /sca/v1/component/{componentUuid}
38		获取组件的所有版本	POST  /sca/v1/components/versions/query
39		获取组件的所有漏洞	POST  /sca/v1/components/vuls/query
40	许可	许可列表	GET   /sca/v1/licenses?pageIndex=xx&pageSize=yy
41		许可详情	POST  /sca/v1/license
42	漏洞	漏洞列表	GET   /sca/v1/vulnerabilities?pageIndex=xx&pageSize=yy
43		漏洞详情	GET   /sca/v1/vulnerability/{customNo}
44	检测任务	检测任务列表	GET   /sca/v1/tasks?pageIndex=xx&pageSize=yy
45	报告	报告列表查询	GET   /sca/v1/reports?pageIndex=xx&pageSize=yy
46		报告删除	DELETE /sca/v1/reports/download/{reportUuid}
47		报告下载	GET   /sca/v1/reports/download
48	定时任务	定时任务列表	GET   /sca/v1/schedules
49		定时任务设置	POST  /sca/v1/schedules
50		定时任务开启/关闭	PUT   /sca/v1/schedules
51	预警	漏洞预警查询	GET  /sca/v1/warnings/vulnerabilities/components?pageIndex=1&pageSize=10
52		站内信查询	GET   /sca/v1/warnings/statistics
53	其他接口	通过字典编码获取字典	GET   /sca/v1/dictionaries/details
54		产品版本信息	GET   /sca/v1/versions
55	数据中心	获取数据中心的组件	GET   /sca/v1/component/detail
56	Ca许可	获取ca许可的详细信息	GET   /sca/v1/license/detail
57	容器	创建容器检测	POST /sca/v1/docker
58		容器列表	GET   /sca/v1/dockers
59		容器删除	DELETE /sca/v1/docker/{appUuid}
60		获取容器详情	GET   /sca/v1/docker/{appUuid}
61	应用	获取应用检测历史	GET   /sca/v1/application/history
62	应用	获取应用下组件修复建议	POST  /sca/v1/application/component/opinionList
63	项目	获取项目成员列表	GET  /sca/v1/project/shareduser/list?projectUuid=&pageIndex=xx&pageSize=yy
64	私服	获取私服列表	GET  /sca/v1/nexus/list?pageIndex=xx&pageSize=yy&filter=
65		获取私服组件列表	GET  /sca/v1/nexus/component/list?pageIndex=xx&pageSize=yy&nexusId=
66		获取私服漏洞列表	GET /sca/v1/nexus/vulList?pageIndex=xx&pageSize=yy&nexusId=
67		获取私服许可列表	GET  /sca/v1/nexus/licenseList?pageIndex=xx&pageSize=yy&nexusId=
68	Ca许可	获取机器码	GET  /sca/v1/permission/machine/{seriesNum}
69		Ca文件上传	POST  /sca/v1/permission/file/upload
70		查询Ca许可状态	GET  /sca/v1/permission/vertification
71	应用	应用-组件使用建议	GET /sca/v1/component/advise
72		应用-组件依赖关系列表查询	GET /sca/v1/applications/componentDependency/{appUuid}?componentUuid=
73	报告	报告-报告生产状态查询	POST  /sca/v1/reports/status
74	应用	应用--生成应用-应用安全报告PDF格式	POST  /sca/v1/reports/pdf/application
75		应用--生成应用-应用安全报告DOC格式	POST  /sca/v1/reports/doc/application
76		应用—生成应用-组件清单报告	POST  /sca/v1/reports/excel/component
77		应用—生成应用-漏洞清单报告	POST  /sca/v1/reports/excel/vul
78		应用—生成应用-许可清单报告	POST  /sca/v1/reports/excel/license

79		应用—生成应用-软件物料清单SBOM报告	POST  /sca/v1/reports/dependencyCheck/sbom
80		应用—源码溯源获取相关统计信息	GET  /sca/v1/source/statistics 
81		应用—源码溯源文件分布	GET /sca/v1/source/file/statistics
82		应用—源码溯源组件文件列表信息	GET /sca/v1/source/components 
83	容器	容器—容器下组件列表	GET /sca/v1/docker/components
84		容器—容器下漏洞列表	GET /sca/v1/docker/vulnerabilities
85		容器—容器下许可列表	GET /sca/v1/docker/licenses
86	应用	应用—批量删除应用	POST  /sca/v1/applications/batchDelete
87	报告	报告--获取报告导出配置列表	GET  /sca/v1/exports/template/select/{exportType}
88	知识库	漏洞详情	GET /sca/v1/vulnerability/knowledge/{customNo}
89		组件模糊查询	GET /sca/v1/component/knowledge/list
90		组件详情	GET /sca/v1/component/knowledge/detail
91	应用	组件添加标记	POST /sca/v1/applications/components/audit/label
92		组件取消标记	DELETE/sca/v1/applications/components/audit/label
93		沿用上次标签添加适配的检测结果	PUT  /sca/v1/applications/label/affect/result
94		检测历史报告导出	GET  /sca/v1/application/file/export
95	资产	组件影响的应用	GET  /sca/v1/component/influence/app/list
96		组件详情导出报告	GET  /sca/v1/component/create/report




4接口详细

状态码
状态码	描述	消息提示
0	ERROR	错误
-1	BUG_AUTH_ERROR	BUG权限异常
1	SUCCESS	成功
2	MESSAGE_TIP	消息提示
5	INIT_LDAP	LDAP初始化
6	SYSTEM_START_CHECK	系统启动检查
10	NEED_LOGIN	需要登录
20	ILLEGAL_ARGUMENT	非法授权
21	ILLEGAL_APP_ASSIGN_AUTH	非法应用分配权限
30	LICENSE_ERROR	许可错误
40	AGENT_DETECT_DISABLE	代理检测禁用
50	AGENT_GLOBAL_DISABLE	许可错误
80	APP_NO_ILLEGAL	无应用授权
90	INVALID_名称_ERROR	参数异常
100	NO_OPERATION_PERMISSION	无操作权限
110	TOKEN_ERROR_CODE	Token错误
404	REQUEST_MISSING	请求未找到


3.1认证--创建token
描述：调用api接口，需要token授权，调用这个接口获取token
POST  /sca/v1/tokens
Content-Type: application/json

请求参数
参数	类型	长度/大小	是否必填	描述
title	String	100	是	标题
expirationTime	Number	1671071236000 – 2^63-1	是	到期时间（时间戳毫秒）
username	String	64	是	邮箱
password	String	64	是	密码

响应参数
名称	名称1	类型	业务描述
status		Number	返回状态码
data	expirationTime	Number	过期时间
	token	String	登录Token
	tokenUuid	String	Token唯一标识
msg		String	返回信息

代码示例
请求正文：
{
	"title": "增加token", 
"expirationTime": 1671971236000,
"username": "<EMAIL>", 
"password": "123"
}

返回示例：
{
"status": 1,
"data": {
"expirationTime":1671971236000,
	"token":"b3JnYWRtaW5Ac2Vjem9uZS5jbjo1MWFhOWM4MDhhNTU1ODA1OTYzOTJmMjJlYTdkNzY0MQ=",
"tokenUuid":"a009cd12e2ed422090f38f8952ed58db"
},
"msg": "OK"
}
3.2认证--刷新token
描述：调用这个接口获取新的token
PUT  /sca/v1/tokens
Content-Type: application/json

请求参数
参数	类型	长度/大小	是否必填	描述
tokenUuid	String	200	是	Token唯一标识
username	String	64	是	邮箱
password	String	64	是	密码

响应参数
名称	名称1	类型	业务描述
status		Number	返回状态码
data	expirationTime	Number	过期时间
	token	String	登录Token
	tokenUuid	String	Token唯一标识
msg		String	返回信息

代码示例
请求正文：
{
	"tokenUuid ": " a009cd12e2ed422090f38f8952ed58db ", 
"username": "<EMAIL>", 
"password": "123"
}

返回示例：
{
"status": 1,
"data": {
"expirationTime":1671971236000,
	"token":"b3JnYWRtaW5Ac2Vjem9uZS5jbjo1MWFhOWM4MDhhNTU1ODA1OTYzOTJmMjJlYTdkNzY0MQ=",
"tokenUuid":"a009cd12e2ed422090f38f8952ed58db"
},
"msg": "OK"
}



3.3认证--获取token列表
描述：获取指定用户创建的token列表
POST  /sca/v1/tokens/query
Content-Type: application/json

请求参数
参数	类型	长度/大小	是否必填	描述
username	String	64	是	邮箱
password	String	64	是	密码
pageIndex	Number	大于0	是	当前页面下标
pageSize	Number	1 ~ 500	是	每页展示行数

响应参数
名称	名称1	名称2	类型	业务描述
status			Number	返回状态码
data	total		Number	总行数
	dataList	expirationTime	Number	过期时间
		token	String	登录Token
		tokenUuid	String	Token唯一标识
	pageIndex		Number	当前页面下标
	pageSize		Number	每页展示行数
msg			String	返回信息

代码示例
请求正文：
{ 
"username": "<EMAIL>", 
"password": "123",
" pageIndex ":1,
" pageSize ":10
}

返回示例：
{
"status": 1,
"data": {
"total ":1,
	"dataList ": [
{
"expirationTime":1671971236000,
"token":" b3JnYWRtaW5Ac2Vjem9uZS5jbjo1MWFhOWM4MDhhNTU1ODA1OTYzOTJmMjJlYTdkNzY0MQ="
"tokenUuid":"a009cd12e2ed422090f38f8952ed58db"
}
],
"pageIndex ":1,
"pageSize ":10
},
"msg": "OK"
}


3.4认证--jwt方式获取token
描述：获取临时的token，有效期1天
POST  /sca/v1/jwt/token
Content-Type: application/json

请求参数
参数	类型	长度/大小	是否必填	描述
username	String	60	是	邮箱
password	String	60	是	密码

响应参数
名称	名称1	类型	业务描述
status		Number	返回状态码
data	expirationTime	Number	过期时间
	token	String	登录Token
msg		String	返回信息

代码示例
请求正文：
{
"username": "<EMAIL>", 
"password": "123"
}

返回示例：
{
"status": 1,
"data": {
"expirationTime":1671971236000,
	"token":"b3JnYWRtaW5Ac2Vjem9uZS5jbjo1MWFhOWM4MDhhNTU1ODA1OTYzOTJmMjJlYTdkNzY0MQ="
},
"msg": "OK"
}


3.5用户--新增用户
描述：新增用户
POST  /sca/v1/users
Content-Type: application/json

请求参数
参数	类型	长度/大小	是否必填	描述
email	String	60	是	邮箱
lastName	String	15	是	姓氏
name	String	15	是	名字
password	String	8 ~ 60	是	密码

响应参数
名称	类型	业务描述
status	Number	返回状态码
msg	String	返回信息

代码示例
请求正文：
{ 
"email ": "<EMAIL>", 
"lastName ": "诸葛",
"name ": "亮",
"password": "12345678",
}

返回示例：
{
"status": 1,
"msg": "OK"
}


3.6用户--获取用户列表
描述：获取用户列表，可以根据邮箱进行模糊查询
GET  /sca/v1/users?pageIndex=xx&pageSize=yy

请求参数
参数	类型	长度/大小	是否必填	描述
pageIndex	Number	大于0	是	当前页面下标
pageSize	Number	1 ~ 500	是	每页展示行数
email	String	60	否	邮箱

响应参数
名称	名称1	名称2	类型	业务描述
status			Number	返回状态码
data	total		Number	总行数
	dataList	email	String	邮箱
		userUuid	String	用户唯一标识
		fullName	String	用户全名
	pageIndex		Number	当前页面下标
	pageSize		Number	每页展示行数
msg			String	返回信息

代码示例
请求正文：
{ 
"username": "<EMAIL>", 
"password": "123",
"pageIndex ":1,
"pageSize ":10
}

返回示例：
{
"status": 1,
"data": {
"total ":1,
	"dataList ": [
{
"email ": "<EMAIL>",
"userUuid ":"a009cd12e2ed422090f38f8952ed58db"
"fullName ":"诸葛亮"
}
],
"pageIndex ":1,
"pageSize ":10
},
"msg": "OK"
}


3.7用户--用户删除
描述：根据用户唯一标识删除用户
DELETE  /sca/v1/users/{userUuid}

请求参数
参数	类型	长度/大小	是否必填	描述
userUuid	String	64	是	用户唯一标识

响应参数
名称	类型	业务描述
status	Number	返回状态码
msg	String	返回信息

代码示例
返回示例：
{
"status": 1,
"msg": "OK"
}


3.8用户--用户修改
描述：根据用户唯一标识修改用户信息
PUT  /sca/v1/users
Content-Type: application/json

请求参数
参数	类型	长度/大小	是否必填	描述
userUuid	String	64	是	用户唯一标识
email	String	60	否	邮箱
lastName	String	15	否	姓氏
name	String	15	否	名字
password	String	8 ~ 60	否	密码
status	Number	1	否	用户状态（1激活，0未激活）
validityDate
	String	10	否	用户有效期
格式yyyy-MM-dd

status为1时才生效


响应参数
名称	类型	业务描述
status	Number	返回状态码
msg	String	返回信息

代码示例
请求正文：
{
	"userUuid ":"a009cd12e2ed422090f38f8952ed58db"
"email ": "<EMAIL>", 
"lastName ": "诸葛",
"name ": "亮121",
"validityDate": "2021-01-01",
"password": "12345678",
"status": 1
}

返回示例：
{
"status": 1,
"msg": "OK"
}


3.9项目--普通项目添加
描述：添加普通项目，要求：项目名称不能重复
POST  /sca/v1/projects/local
Content-Type: application/json

请求参数
参数	类型	长度/大小	是否必填	描述
projectName	String	80	是	项目名
describe	String	80	否	描述

响应参数
名称	名称1	类型	业务描述
status		Number	返回状态码
data	projectVersionId	Number	项目版本id
	projectUuid	String	项目唯一标识
msg		String	返回信息

代码示例
请求正文：
{ 
"projectName": "testadmin", 
"describe": "this is a describe"

}

返回示例：
{
"status": 1,
"data":{
	"projectVersionId": 3382,
	"projectUuid": "9c58d420b16f4c518b0bde594627823a"
}
"msg": "OK"
}


3.10项目--项目列表
描述：获取项目列表，可以使用项目名进行模糊查询
GET  /sca/v1/projects?pageIndex=xx&pageSize=yy

请求参数
参数	类型	长度/大小	是否必填	描述
pageIndex	Number	大于0	是	当前页面下标
pageSize	Number	1 ~ 500	是	每页展示行数
projectName	String	80	否	项目名

响应参数
名称	名称1	名称2	类型	业务描述
status			Number	返回状态码
data	total		Number	总行数
	dataList	projectName	String	项目名
		projectUuid	String	项目唯一标识
		grade	String	项目风险等级
字典值 字典编码PROJECTRISK(详情见接口3.54)
		label	String	项目标签
		createUser	String	创建人
		updateTime	Number	更新时间
		projectScope	String	项目范围（个人项目/分享项目）
		describe	String	描述
		projectType	String	项目类型
		gradeDesc	String	项目风险等级描述
	pageIndex		Number	当前页面下标
	pageSize		Number	每页展示行数
msg			String	返回信息

返回示例：
{
"status": 1,
"data": {
"total ":1,
	"dataList ": [
{
        "projectName": "testadmin",
        "projectUuid": "9c58d420b16f4c518b0bde594627823a",
        "grade": "NONE",
        "label": null,
        "createUser": "AdminOrg",
        "updateTime": 1671157489000,
        "projectScope": "个人项目",
        "describe": "this is a describe",
        "projectType": "0",
        "gradeDesc": null
}
],
"pageIndex ":1,
"pageSize ":10
},
"msg": "OK"
}


3.11项目--项目删除
描述：根据项目唯一标识删除项目
DELETE  /sca/v1/projects/{projectUuid}

请求参数
参数	类型	长度/大小	是否必填	描述
projectUuid	String	80	是	项目唯一标识

响应参数
名称	类型	业务描述
status	Number	返回状态码
msg	String	返回信息

代码示例
返回示例：
{
"status": 1,
"msg": "OK"
}


3.12项目--项目修改
描述：根据项目唯一标识修改项目
PUT  /sca/v1/projects
Content-Type: application/json

请求参数
参数	类型	长度/大小	是否必填	描述
projectUuid	String	80	是	项目唯一标识
projectName	String	80	否	项目名
describe	String	80	否	描述

响应参数
名称	名称1	类型	业务描述
status		Number	返回状态码
data	projectVersionId	Number	项目版本号id
	projectUuid	String	项目唯一标识
msg		String	返回信息

代码示例
请求正文：
{ 
	"projectUuid": "9c58d420b16f4c518b0bde594627823a",
"projectName": "testadmin123", 
"describe": "this is a describe update"

}

返回示例：
{
"status": 1,
"data":{
	"projectVersionId": 3382,
	"projectUuid": "9c58d420b16f4c518b0bde594627823a"
}
"msg": "OK"
}


3.13项目--项目详情
描述：根据项目唯一标识获取项目的详细信息
GET  /sca/v1/project/{projectUuid}

请求参数
参数	类型	长度/大小	是否必填	描述
projectUuid	String	80	是	项目唯一标识

响应参数
名称	名称1	类型	业务描述
status		Number	返回状态码
data	projectName	String	项目名
	projectUuid	String	项目唯一标识
	grade	String	项目风险等级
字典值 字典编码PROJECTRISK(详情见接口3.54)
	label	String	项目标签
	createUser	String	创建人
	updateTime	Number	更新时间
	projectScope	String	项目范围（个人项目/分享项目）
	describe	String	描述
	projectType	String	项目类型
	gradeDesc	String	项目风险等级描述
msg		String	返回信息

返回示例：
{
"status": 1,
"data": {
   "projectName": "testadmin",
   "projectUuid": "9c58d420b16f4c518b0bde594627823a",
   "grade": "NONE",
   "label": null,
   "createUser": "AdminOrg",
   "updateTime": 1671157489000,
   "projectScope": "个人项目",
   "describe": "this is a describe",
   "projectType": "0",
   "gradeDesc": null
},
"msg": "OK"
}


3.14项目--项目成员增加
描述：添加普通项目，要求：项目名称不能重复
POST  /sca/v1/projects/users
Content-Type: application/json

请求参数
参数	参数	类型	长度/大小	是否必填	描述
projectUuid		String	80	是	项目唯一标识
authList	userUuid	String	80	是	用户唯一标识 
（3.18接口获取,传userUuid值）
	auth	Number		是	权限（3.17接口获取,传code值）

响应参数
名称	类型	业务描述
status	Number	返回状态码
msg	String	返回信息
errorUuidList	Array	添加失败的成员uuid

代码示例
请求正文：
{ 
"projectUuid": "9c58d420b16f4c518b0bde594627823a", 
" authList ": [
{
   		" userUuid ": "15a17679c34f477fa451665ebae512e3",
   		" auth ": 1
}
]
}

返回示例：
{
"status": 1,
"msg": "OK",
"errorUuidList": ["15a17679c34f477fa451665ebae512e3"]
}


3.15项目--项目相关统计
描述：根据项目唯一标识统计项目的相关数量
GET  /sca/v1/statistics/projects/{projectUuid}

请求参数
参数	类型	长度/大小	是否必填	描述
projectUuid	String	80	是	项目唯一标识
grade	String		否	风险等级（多个用,拼接）
字典编码PROJECTRISK(详情见接口3.54)

响应参数
名称	名称1	类型	业务描述
status		Number	返回状态码
data	appNum	Number	应用数量
	componentNum	Number	组件数量
	vulNum	Number	漏洞数量
	licenseNum	Number	许可数量
msg		String	返回信息

返回示例：
{
"status": 1,
"data": {
	"appNum": 1,
	" componentNum ": 3,
	" vulNum ": 1,
	" licenseNum ": 9
},
"msg": "OK"
}


3.16项目--项目报告生成
描述：根据项目唯一标识异步生成相关的PDF文档，完成后请到相关页面的报告处下载
POST  /sca/v1/projects/files/create
Content-Type: application/json

请求参数
参数	类型	长度/大小	是否必填	描述
projectUuid	String	80	是	项目唯一标识
type	String	10	是	暂时只支持PDF（参数传pdf）
templateId	Number		否	报告配置模板id

响应参数
名称	类型	业务描述
status	Number	返回状态码
data	String	报告唯一标识，下载用
msg	String	返回信息

代码示例
请求正文：
{ 
	"templateId ": 19,
"projectUuid": "9c58d420b16f4c518b0bde594627823a", 
" type ": "pdf"
}

返回示例：
{
"status": 1,
"data": "wsYISp+L6hd6y5rwNlOIfQ==",
"msg": "OK"
}


3.17项目--获取项目/应用权限列表
描述：获取项目应用权限
GET  /sca/v1/auths?code=

请求参数
参数	类型	长度/大小	是否必填	描述
code	Number	1	是	项目 见项目列表:
projectScope字段
1:个人
2:分享

应用 见应用列表:
appType字段
1:个人
2:分享
(传1不查owner，传2不查owner和master)

响应参数
名称	名称1	类型	业务描述
status		Number	返回状态码
data	label	String	权限
	code	Number	编码
msg		String	返回信息

返回示例：
{
"status": 1,
"data": [
        {
            "label": "master",
            "code": 2
        },
        {
            "label": "dev",
            "code": 3
        }
],
"msg": "OK"
}


3.18项目--获取用户列表(增加项目/应用成员时使用)
描述：获取企业下的用户列表 (增加项目/应用成员时使用)
GET  /sca/v1/projects/users?projectUuid=&appUuid=

请求参数
参数	类型	长度/大小	是否必填	描述
projectUuid	String	80	否(与appUuid二选一)	项目唯一标识
appUuid	String	80	否(与projectUuid二选一)	应用唯一标识

响应参数
名称	名称1	类型	业务描述
status		Number	返回状态码
data	userUuid	String	用户唯一标识
	userName	String	用户名称(用户注册邮箱)
msg		String	返回信息

返回示例：
{
"status": 1,
"data":[
        {
            "userUuid": "e75df78b6e3946e89c949603e8f6a611",
            "userName": "王合肥(<EMAIL>)"
        },
        {
            "userUuid": "e664b67518d84d58be13930a466cd58b",
            "userName": "普通用户(<EMAIL>)"
        }
],
"msg": "OK"
}


3.19项目--项目成员删除
描述：根据项目唯一标识和用户唯一标识删除项目下的成员
DELETE  /sca/v1/projects/users
Content-Type: application/json

请求参数
参数	类型	长度/大小	是否必填	描述
projectUuid	String	80	是	项目唯一标识
userUuid	String	80	是	用户唯一标识

响应参数
名称	类型	业务描述
status	Number	返回状态码
msg	String	返回信息
errorUuidList	Array	删除失败的成员uuid

代码示例
请求正文：
{ 
	"projectUuid": "9c58d420b16f4c518b0bde594627823a", 
"userUuid": "e75df78b6e3946e89c949603e8f6a611"
}

返回示例：
{
"status": 1,
"msg": "OK",
"errorUuidList": ["e75df78b6e3946e89c949603e8f6a611"]
}


3.20应用--普通应用添加
描述：添加普通应用，要求：相同项目下应用名称不能重复
POST  /sca/v1/applications/local
Content-Type: application/json

请求参数
参数	类型	长度/大小	是否必填	描述
appName	String	80	是	应用名称
projectUuid	String	80	是	项目唯一标识id

响应参数
名称	名称1	类型	业务描述
status		Number	返回状态码
data	appUuid	String	应用唯一标识
msg		String	返回信息

代码示例
请求正文：
{ 
"appName": "testadmin", 
"projectUuid": "37d38c55c3174181af85747e9c661c57"

}

返回示例：
{
"status": 1,
"data":{
	"appUuid": "6a56c76759b042359383fc44b3f99dee"
}
"msg": "OK"
}


3.21应用--本地上传检测
描述：本地离线包上传检测接口，可接收源码包、构建后制品包进行上传检测
POST  /sca/v1/applications/local/detection
Content-Type: multipart/form-data

请求参数
参数	类型	长度/大小	是否必填	描述
appUuid	String	64	是	应用唯一标识
file	MultipartFile	2G	是	需要检测的文件
type	String	1	是	应用来源
本地上传：0
特征文件：11
scanLevel	String	2	否	扫描层级,范围0-10整数， 默认为3
callBackUrl	String	1024	否	回调接口地址(post接口，json参数体)
codeTraceabilityAnalysis	boolean	1	否	源码溯源,true开启(溯源必须许可文件授权以后才有此功能)
limitNumber	number	0~1000	否	文件限制行数 推荐值 10
源码溯源开启必填
threshold	String	0~1.0	否	阈值 推荐值0.8
源码溯源开启必填
fileFormatFilter	String	200	否	源码溯源文件格式过滤
默认为空，文件后缀用分号隔开。例：zip;txt
fileDirectoryFilter	String	200	否	源码溯源文件目录过滤
默认为空，路径从项目根目录开始，目录间用分号隔开。例：/sc/assets/text;/sc
fileFilter	String	200	否	源码溯源指定文件过滤
默认为空，路径从项目根目录开始，文件间用分号隔开。例：/sc/a.zip;/sc/b.txt


响应参数
名称	名称1	类型	业务描述
status		Number	返回状态码
data	checkNo	String	检测编号
msg		String	返回信息

代码示例
请求正文：



返回示例：
{
"status": 1,
    "data": {
           "checkNo":" 2022121615350067110268377 "
},
    "msg": "OK"
}


3.22应用--代码仓库添加
描述：添加代码仓库检测记录
POST  /sca/v1/applications/repository
Content-Type: application/json

请求参数
参数	类型	长度/大小	是否必填	描述
appName	String	80	是	应用名称
projectUuid	String	80	是	项目唯一标识
type	String	1~10	是	应用来源：
1.git
2. svn
5.TFS
6.Gerrit
7.Mercurial
8.Coding
9.Artifactory
10.Firefly
gitType	String	20	否	git类型
type是git的时候该字段必输
值为
github/gitlab/gitee
gitlabApiVersion	String	20	否	gitlab版本
当gitType为gitlab必填

枚举值有   V3、V4

说明：在GitLab 9.0及更高版本中，请选择 API V4版本
gitLabHead	String	500	否	GitLab访问配置
当gitType为gitlab必填
Protocol	String	20	否	type是1、5、6的时候该字段必填。
SSH:以SSH方式拉取代码
HTTPS:以HTTP方式拉取代码
gitLabPort	String	5	否	gitlab指定ssh端口号 默认22，protocol是SSH的时候该字段必填
privateKey	String	4000	否	密钥
当type为1、5、6，且protocol是SSH的时候该字段必填（请注意转义）
pullWay	Number	1、2	否	当type为1、5、6，且protocol是HTTPS的时候该字段必填。
1: 用户名密码方式拉取代码
2: token方式拉取代码
Username	String	200	否	Git或svn的用户名,
当type为1、5、6，且protocol是HTTPS、pullWay=1时，该字段必填

或

当type为2、8、9、10时，该字段必填
Password	String	200	否	Git或svn的密码，
当type为1、5、6，且protocol是HTTPS、pullWay=1时，改字段必填

或

当type为2、8、9、10时，该字段必填
accessToken	String	200	否	Git的Token
当type为1、5、6，且protocol是HTTPS、pullWay=2时候该字段必填
url	String	500	否	Git或svn的路径

当type不为10时，该字段必填
serverAddress	String	200	否	服务器地址,

当type为10时，该字段必填
warehouseName	String	200	否	仓库名称
当type为10时，该字段必填
branch	String	200	否	git分支。和detectionTags二选一（但两者都可为空）
scanLevel	Number	-1~10	否	扫描层级默认为3，取值范围-1（-1代表智能识别）~10
filterName	String	500	否	仓库文件名称（用来过滤仓库文件）
detectionTags
	String	200	否	git标签。和branch二选一（但两者都可为空）


响应参数
名称	名称1	类型	业务描述
status		String	返回状态码
data	appUuid	Number	应用唯一标识
msg		String	返回信息

代码示例
请求正文：
{
    "appName":"test22.5.8-4",
    "projectUuid":"99e215cf80e442408ce92e31c5e90a0a",
    "type":"1",
    "gitType":"gitlab",
    "gitlabApiVersion":"V4",
    "gitLabHead":"https://gitlab.com/",
    "protocol":"HTTPS",
    "gitLabPort":"22",
    "privateKey":null,
    "pullWay":2,
    "username":null,
    "password":null,
    "accessToken":"**************************",
    "url":"https://gitlab.com/stucksback/bolt.git",
    "serverAddress":null,
    "warehouseName":null,
    "branch":null,
    " scanLevel ":3,
"appLicenseId":null,
"filterName":null
}

返回示例：
{
"status": 1,
"data":{
"appUuid":"a009cd12e2ed422090f38f8952ed58db"
},
    "msg": "OK"
}


3.23应用--仓库应用检测
描述：根据应用唯一标识对应用进行检测
GET  /sca/v1/applications/repositorys/detection/{appUuid}

请求参数
参数	类型	长度/大小	是否必填	描述
appUuid	String	80	是	应用唯一标识
codeTraceabilityAnalysis	boolean	1	否	源码溯源,true开启(溯源必须许可文件授权以后才有此功能)
limitNumber	number	0~1000	否	文件限制行数 推荐值 10
源码溯源开启必填
threshold	String	0~1.0	否	阈值 推荐值0.8
源码溯源开启必填
fileFormatFilter	String	200	否	源码溯源文件格式过滤
默认为空，文件后缀用分号隔开。例：zip;txt
fileDirectoryFilter	String	200	否	源码溯源文件目录过滤
默认为空，路径从项目根目录开始，目录间用分号隔开。例：/sc/assets/text;/sc
fileFilter	String	200	否	源码溯源指定文件过滤
默认为空，路径从项目根目录开始，文件间用分号隔开。例：/sc/a.zip;/sc/b.txt

响应参数
名称	名称1	类型	业务描述
status		Number	返回状态码
data	checkNo	String	检测编号
msg		String	返回信息

返回示例：
{
"status": 1,
"data": {
	"checkNo": "2022121615350067110268377"
},
"msg": "OK"
}


3.23.1应用--仓库应用检测
描述：应用  git/svn 项目检测,当检测失败的时候，会调用callBackUrl对应的接口来通知调用者检测失败
POST  /sca/v1/warehouse/applications/detection
Content-Type: application/x-www-form-urlencoded

请求参数
参数	类型	长度/大小	是否必填	描述
appUuid	String	80	是	应用唯一标识
callBackUrl	String	1024	是	回调接口地址(post接口，json参数体)

响应参数
名称	名称1	类型	业务描述
status		Number	返回状态码
data	checkNo	String	检测编号
msg		String	返回信息

代码示例
请求正文：
KEY	VALUE
appUuid	a009cd12e2ed422090f38f8952ed58db
callBackUrl	https://gitlab.com/stucksback/bolt.git

返回示例：
{
"status": 1,
"data": {
	"checkNo": "2022121615350067110268377"
},
"msg": "OK"
}


3.24应用--应用列表
描述：获取当前人员所有应用列表信息，也可根据项目ID和应用名称获取指定范围的应用列表信息
GET  /sca/v1/applications?pageIndex=xx&pageSize=yy

请求参数:
参数	类型	长度/大小	是否必填	描述
pageIndex	number	大于0	是	当前页面下标
pageSize	number	1 ~ 500	是	每页展示行数
projectUuid	String	80	否	项目唯一标识
appName	String	80	否	应用名称


响应参数：
名称	名称1	名称2	类型	业务描述
status			Number	返回状态码
data	total		Number	总数
	dataList	appUuid	String	应用唯一标识id
		appType	String	应用类型
		gradeDesc	String	风险描述
		grade	String	风险级别字典值 字典编码:APPRISK(详情见接口13.1) 
		name	String	应用名称
		status	String	检测状态字典值 字典编码:SOURCECHECKSTATUS(详情见接口13.1) 
		checkNo	String	检测编号
		uploadTime	Number	时间
		detectionBranch	String	分支名称
		startTime	Number	开始检测时间
		endTime	Number	检测结束时间
	pageIndex		Number	当前页面下标
	pageSize		Number	每页展示行数
msg			String	返回信息

代码示例
返回示例：
{
    "status": 1,
    "data": {
        "total": 1,
        "dataList": [
            {
                "appUuid": "6a56c76759b042359383fc44b3f99dee",
                "appType": "个人应用",
                "gradeDesc": "存在高危风险的组件【1】个\n",
                "grade": "HIGH",
                "name": "jiang-python",
                "status": "2",
                "errorMessage": "系统异常，请稍后重试",
                "checkNo": "2022121211142568510236411",
                "uploadTime": 1671416697000,
                "detectionBranch": null,
                "startTime": 1670814865000,
                "endTime": null
            }
        ],
        "pageIndex": 1,
        "pageSize": 10
    },
    "msg": "OK"
}


3.25应用—应用删除
描述：根据应用唯一标识删除应用
DELETE  /sca/v1/applications/{appUuid}

请求参数
参数	类型	长度/大小	是否必填	描述
appUuid	String	64	是	项目唯一标识

响应参数
名称	类型	业务描述
status	Number	返回状态码
msg	String	返回信息

代码示例
返回示例：
{
"status": 1,
"msg": "OK"
}


3.26应用--应用详情
描述：根据应用唯一标识获取应用的详细信息
GET  /sca/v1/applications/{appUuid}

请求参数
参数	类型	长度/大小	是否必填	描述
appUuid	String	80	是	项目唯一标识

响应参数
名称	名称1	类型	业务描述
status		Number	返回状态码
data	appUuid	String	应用唯一标识id
	appType	String	应用类型
	gradeDesc	String	风险描述
	grade	String	风险级别字典值 字典编码:APPRISK(详情见接口13.1) 
	name	String	应用名称
	status	String	检测状态字典值 字典编码:SOURCECHECKSTATUS(详情见接口13.1) 
	uploadTime	Number	时间
	auth	Number	
	copyRights	List<String>	版权信息
msg		String	返回信息

返回示例：
{
"status": 1,
"data": {
        "appUuid": "67cf2c7f50ac42228ccc0fb8fbc206a3",
        "appType": "个人应用",
        "gradeDesc": "无风险--超超超",
        "grade": "NONE",
        "name": "ServerScan_Pro_v1.0.2_windows_x86.zip",
        "state": 2,
        "uploadTime": 1671383136000,
        "auth": 1,
        "copyRights": [
            "Copyright (c) 2015 David Schury, Gabi Melman",
            "Copyright (c) 2016 David Schury, Gabi Melman",
            "Copyright (c) 2017 David Schury, Gabi Melman",
            "Copyright (c) 2018 David Schury, Gabi Melman",
            "Copyright (c) 2019 David Schury, Gabi Melman",
            "Copyright (c) 2020 David Schury, Gabi Melman"
        ]
},
"msg": "OK"
}


3.27应用--应用修改
描述：根据应用唯一标识修改应用信息
PUT  /sca/v1/applications
Content-Type: application/json

请求参数
参数	类型	长度/大小	是否必填	描述
appUuid	String	80	是	应用唯一标识
appName	String	80	否	应用名称
username	String	200	否	用户名
password	String	200	否	密码
Branch	String	200	否	git分支
url	String	500	否	Git或svn的路径
Protocol	String	20	否	SSH:以SSH方式拉取代码
HTTPS:以HTTP方式拉取代码
pullWay	Number		否	
1: 用户名密码方式拉取代码
2: token方式拉取代码
accessToken	String	200	否	Token
gitLabPort	String	5	否	gitlab指定ssh端口号 默认22，
privateKey

	String	4000	否	密钥
serverAddress	String	200	否	服务器地址
warehouseName	String	200	否	仓库名称
scanLevel	Number	0~10	否	扫描层级默认为1，取值范围0~10
gitType	String	20	否	git类型
gitlabApiVersion	String	20	否	gitlab版本

gitLabHead	String	500	否	GitLab访问配置

响应参数
名称	名称1	类型	业务描述
status		Number	返回状态码
data	appUuid	Number	项目版本号id
	projectUuid	String	项目唯一标识
msg		String	返回信息

代码示例
请求正文：
{ 
	"appUuid": "9c58d420b16f4c518b0bde594627823a",
"appName": "应用名称"
}

返回示例：
{
"status": 1,
"data":{
	"appUuid": "9c58d420b16f4c518b0bde594627823a "
}
"msg": "OK"
}


3.28应用--应用分享
描述：把应用分享给指定的用户
POST  /sca/v1/applications/user
Content-Type: application/json

请求参数
参数	类型	长度/大小	是否必填	描述
appUuid	String	64	是	应用唯一标识
userUuid	String	64	是	用户唯一标识（3.18接口获取,传userUuid值）
auth	String		是	权限（3.17接口获取,传code值）


响应参数
名称	类型	业务描述
status	String	返回状态码
msg	String	返回信息

代码示例
请求正文：
{
    "appUuid":"99e215cf80e442408ce92e31c5e90a0a123",
    "userUuid":"99e215cf80e442408ce92e31c5e90a0a",
    "auth":"1"
}

返回示例：
{
"status": 1,
    "msg": "OK"
}


3.29应用--应用检测进度
描述：根据检测编号获取应用的检测进度
GET  /sca/v1/applications/progress/{checkNo}

请求参数:
参数	类型	长度/大小	是否必填	描述
checkNo	String	64	是	检测编号


响应参数：
名称	名称2	类型	业务描述
status		Number	返回状态码


data
	progress	String	进度
	state	Number	应用检测状态字典值 字典编码:SOURCECHECKSTATUS
	checkStartTime	String	检测开始时间
	checkEndTime	String	检测结束时间
	checkTime	Long	检测实际时长（单位：秒）
msg		String	返回信息

代码示例
返回示例：
{
    "status": 1,
    "data": {
        "progress": "100%",
"state": 1,
"checkTime": 153,
"checkEndTime": "2023-05-26 01:22:45",
"checkStartTime": "2023-05-26 01:20:12"
    },
    "msg": "OK"
}


3.30应用--应用相关统计
描述：根据应用唯一标识统计应用的相关数量
GET  /sca/v1/statistics/applications/{appUuid}

请求参数
参数	类型	长度/大小	是否必填	描述
appUuid	String	64	是	应用唯一标识
grade	String		否	风险等级（多个用,拼接）
字典编码PROJECTRISK(详情见接口3.54)

响应参数
名称	名称1	类型	业务描述
status		Number	返回状态码
data	componentNum	Number	组件数量
	vulNum	Number	漏洞数量
	licenseNum	Number	许可数量
msg		String	返回信息

返回示例：
{
"status": 1,
"data": {
	" componentNum ": 3,
	" vulNum ": 1,
	" licenseNum ": 9
},
"msg": "OK"
}


3.31应用--应用报告生成
描述：根据应用唯一标识异步生成相关的PDF文档，完成后请到相关页面的报告处下载
GET  /sca/v1/applications/file/create/{appUuid}?type=pdf

请求参数
参数	类型	长度/大小	是否必填	描述
appUuid	String	64	是	项目唯一标识
type	String	10	否	暂时只支持PDF（参数传pdf）

响应参数
名称	类型	业务描述
status	Number	返回状态码
data	String	报告唯一标识，下载用
msg	String	返回信息

代码示例
返回示例：
{
"status": 1,
"data": "wsYISp+L6hd6y5rwNlOIfQ==",
"msg": "OK"
}


3.32应用--应用阻断列表
描述：查询应用阻断信息列表
POST 	/sca/v1/applications/blocks/query
Content-Type: application/json

请求参数
参数	类型	长度/大小	是否必填	描述
appUuid	String	64	是	应用唯一标识
pageIndex	number	大于0	是	当前页面下标
pageSize	number	1~ 500	是	每页展示行数


响应参数：
名称	名称1	名称2	类型	业务描述
status			Number	返回状态码
data	total		Number	总数
	dataList	blockReason	String	阻断原因
		blockStatus	Number	阻断状态
	pageIndex		Number	当前页面下标
	pageSize		Number	每页展示行数
msg			String	返回信息
代码示例
请求正文：
{
    "appUuid":"99e215cf80e442408ce92e31c5e90a0a123",
    "pageIndex":1,
    "pageSize":1
}

{
    "status": 1,
    "data": {
        "total": 1,
        "dataList": [
            {
                "blockReason": "阻断原因"
                "blockStatus": 1
            }
        ],
        "pageIndex": 1,
        "pageSize": 10
    },
    "msg": "OK"
}


3.33应用--项目/应用下的组件列表
描述：获取当前人员所有应用列表信息，也可根据项目ID和应用名称获取指定范围的应用列表信息
POST  /sca/v1/applications/components
Content-Type: form-data

请求参数:
参数	类型	长度/大小	是否必填	描述
pageIndex	number	大于0	是	当前页面下标
pageSize	number	1 ~ 500	是	每页展示行数
appUuid	String	64	是	应用唯一标识
projectUuid	String	80	否	项目唯一标识
componentUuid	String	80	否	组件uuid（如果填入会只查出一个组件记录）
language	String	10	否	语言字典值 字典编码：COMPUTERLANGUAGE(详情见接口3.54)
grade	String	10	否	风险级别字典值 字典编码:JARRISK(详情见接口3.54)
license	String	20	否	许可
controlStatus	number	1	否	黑白名单字典值 字典编码:CONTROLSTATUS(详情见接口3.54)
componentType	number	1	否	组件类型字典值 字典编码:PRIVATEPUBLICSTATUS(详情见接口3.54)
dependRank	String	10	否	依赖等级（直接依赖/间接依赖）
IntroductionType	String	10	否	引用类型字典值 字典编码:INTRODUCTIONTYPE(详情见接口3.54)
depScopeList	String	10	否	作用域字典值 字典编码:SCOPE(详情见接口3.54)
scanLevel	number	2	否	扫描层级
(1~10)
remarkTypes		String		否	组件标记
remarkTypes 值只有在appUuid存在时才有效，且只能是 
[0,  1, 2]
0 - 无标签
1 - 误报
2 - 忽略


响应参数：
名称	名称1	名称2	名称3	类型	业务描述
status				Number	返回状态码
data	total			Number	总数
	dataList	componentId		Number	组件id
		groupId		String	组织
		artifactId		String	组件名称
		componentUuid		String	组件唯一标识
		controlStatus		Number	黑白名单状态字典值  字典编码:LICENSECONTROLSTATUS(详情见接口3.54)
		controlStatusName		String	黑白名单字典值中文名
		depRank		String	依赖类型
		depScope		String	作用域
		reference		String	引用类型字典值 字典编码:INTRODUCTIONTYPE(详情见接口3.54)
		grade		String	风险级别字典值 字典编码:JARRISK(详情见接口3.54)
		jarInfoAddFrom		String	语言字典值 字典编码COMPUTERLANGUAGE
		version		String	版本
		recommendVersion		String	推荐版本
		appVersionId		Number	应用版本id
		firstCheckTime		Number	第一次检测时间
		lastCheckTime		Number	最后检测时间
		sourceInfoList	depth	String	扫描层级
		sourceInfoList
origin	depRank	String	依赖方式
			path	String	解析路径
				String	来源
		licenseIds		array	许可名称列表
		gradeDesc		String	风险描述
		homePage		String	主页
		sourceCode		String	源码
		releaseTime		Number	发布时间
		latestVersion		String	最新版本
		privatePublicStatus		Number	组件状态编码，0 开源，1自研，2开源（未收录），3   非开源，4  开源（已修改），5  自研（废弃）
		privatePublicStatusName		String	组件状态中文
		classifier		String	classifier字符
		projectWhiteControlStatus		Number	黑名单是否豁免。1：豁免；0不豁免
		country		String	所属国家
		countryChineseName		String	所属国家的中文名称
		virusFlag		Number	投毒组件标识 1-是 
0-否
	pageIndex			Number	当前页面下标
	pageSize			Number	每页展示行数
msg				String	返回信息

代码示例
请求正文：


返回示例：
{
    "status": 1,
    "data": {
        "total": 1,
        "dataList": [
            {
                "componentId": 2609005,
                "groupId": "",
                "artifactId": "datascript",
                "componentUuid": "3121304dcc5a0d12ad72f6b6c74da0e2b683577d158270bed94561f135a6b4d7",
                "controlStatus": 0,
                "controlStatusName": "未配置",
                "depRank": "直接依赖",
                "depScope": "compile",
                "reference": "file",
                "grade": "NONE",
                "jarInfoAddFrom": "npm",
                "licenseId": null,
                "version": "1.0.1",
                "recommendVersion": "1.0.1",
                "appVersionId": 26200,
                "firstCheckTime": 1669000414000,
                "lastCheckTime": 1669000414000,
                "sourceInfoList": [
                    {
                        "depth": "1",
                        "depRank": "直接依赖",
                        "path": "\\datascript-master\\release-js\\package.json"
                    }
                ],
                "origin": "直接依赖【1】处",
                "licenseIds": null,
                "gradeDesc": "",
                "homePage": "https://github.com/tonsky/datascript",
                "sourceCode": "https://github.com/tonsky/datascript",
                "releaseTime": 1598641841000,
                "latestVersion": "1.3.15",
                "privatePublicStatus": 0,
                "privatePublicStatusName": "开源",
                "classifier": null,
                "projectWhiteControlStatus": 0,
                "country": null,
                "countryChineseName": null,
                "virusFlag": 1
}
        ],
        "pageIndex": 1,
        "pageSize": 1
    },
    "msg": "OK"
}

3.34应用--应用下的漏洞列表
详见章节 3.42
3.35应用--应用下的许可列表
详见章节 3.40


3.36应用--获取资产组件列表
描述：获取当前人员所属机构下的组件列表，可以根据项目唯一标识、应用唯一标识、组件名称来进一步过滤查询结果
GET  /sca/v1/components?pageIndex=xx&pageSize=yy

请求参数:
参数	类型	长度/大小	是否必填	描述
pageIndex	number	大于0	是	当前页面下标
pageSize	number	1 ~ 500	是	每页展示行数
projectUuid	String	80	否	项目唯一标识
appUuid	String	64	否	应用唯一标识
componentName	String	64	否	组件名称(artifactId)


响应参数：
名称	名称1	名称2	类型	业务描述
status			Number	返回状态码
data	total		Number	总数
	dataList	groupId	String	组织
		artifactId	String	组件名称
		componentUuid	String	组件唯一标识
		controlStatus	Number	黑白名单状态字典值  字典编码:LICENSECONTROLSTATUS(详情见接口3.54)
		grade	String	风险级别字典值 字典编码:JARRISK(详情见接口3.54)
		gradeName	String	风险级别描述
		jarInfoAddFrom	String	语言字典值 字典编码COMPUTERLANGUAGE
		language	String	语言
		licenseIds	array	许可名称列表
		version	String	版本
		recommendVersion	String	推荐版本
		latestVersion	String	最新版本
		country	String	所属国家
		countryChineseName	String	所属国家的中文名称
	pageIndex		Number	当前页面下标
	pageSize		Number	每页展示行数
msg			String	返回信息

代码示例
返回示例：
{
    "status": 1,
    "data": {
        "total": 70724,
        "dataList": [
            {
                "groupId": "",
                "artifactId": "datascript",
                "componentUuid": "94f1ec4647f5a38b878bfc1070593fd44732e6ac79f92fc6da57a12647c45bb0",
                "controlStatus": 0,
                "grade": "HIGH",
                "gradeName": "高危", 
                "jarInfoAddFrom": "npm",
"language": "Java", 
"licenseIds": [
   "Apache-2.0"
],
                "version": "1.0.1",
                "recommendVersion": "1.0.1", 
                "latestVersion": "1.3.15",
                "country": null,
                "countryChineseName": null
            }
        ],
        "pageIndex": 1,
        "pageSize": 1
    },
    "msg": "OK"
}


3.37应用--组件详情
描述：根据组件唯一标识获取组件详情
GET  /sca/v1/component/{componentUuid}
请求参数:
参数	类型	长度/大小	是否必填	描述
componentUuid	String	80	是	组件唯一标识
appUuid	String	80	否	应用唯一标识


响应参数：
名称	名称1	名称2	类型	业务描述
status			Number	返回状态码
data	groupId		String	组织
	artifactId		String	组件名称
	componentUuid		String	组件唯一标识
	controlStatus		Number	黑白名单状态字典值  字典编码:LICENSECONTROLSTATUS(详情见接口3.54)
	grade		String	风险级别字典值 字典编码:JARRISK(详情见接口3.54)
	gradeDesc		String	风险级别描述
	jarInfoAddFrom		String	语言字典值 字典编码COMPUTERLANGUAGE
	language		String	语言
	licenseIds		array	许可名称列表
	version		String	版本
	recommendVersion		String	推荐版本
	releaseTime		Number	发布时间
	homePage		String	主页
	sourceCode		String	源码
	latestVersion		String	最新版本
	versionEntities	releaseDate	Number	发布时间
		version	String	版本
		grade	String	风险级别字典值 字典编码:JARRISK(详情见接口3.54)
		gradeName	String	风险级别描述
	privatePublicStatus		Number	组件状态编码，0 开源，1自研，2开源（未收录），3   非开源，4  开源（已修改），5  自研（废弃）
	privatePublicStatusName		String	组件状态中文
	country		String	所属国家
	countryChineseName		String	所属国家的中文名称
	componentIntegrity		String	组件完整性(传appUuid才有值)
	licenseIntegrity		String	许可完整性(传appUuid才有值)
	organization		String	所属组织
	ownSoftware		String	所属软件
	author		String	开发者
	description		String	组件描述
	virusFlag		Number	投毒组件标识 1-是 0-否
msg			String	返回信息

代码示例
返回示例：
{
    "status": 1,
    "data": {
        "groupId": "net.sourceforge.htmlunit",
        "artifactId": "htmlunit",
        "componentUuid": "94f1ec4647f5a38b878bfc1070593fd44732e6ac79f92fc6da57a12647c45bb0",
        "controlStatus": 0,
        "grade": "HIGH",
        "gradeDesc": "存在高危--超超超风险的漏洞:CVE-2020-5529;;;;",
        "jarInfoAddFrom": "maven",
        "language": "Java",
        "licenseIds": [
            "Apache-2.0"
        ],
        "version": "2.1.5",
        "recommendVersion": "2.37.0",
        "releaseTime": 1483625971000,
        "homePage": "http://www.GargoyleSoftware.com/",
        "sourceCode": "https://github.com/htmlunit/htmlunit",
        "latestVersion": "2.67.0",
        "versionEntities": [
            {
                "releaseDate": 1668949170000,
                "version": "2.67.0",
                "grade": "NONE",
                "gradeName": "无风险"
            }
        ],
        "privatePublicStatus": 0,
        "privatePublicStatusName": "开源",
        "country": null,
        "countryChineseName": null ,
" componentIntegrity ":"完整",
" licenseIntegrity ":"未篡改",
"description": "xxxx",
    "organization": null,
    "ownSoftware": null,
    "author": null,
    "virusFlag": 1
},
    "msg": "OK"
}
3.38组件--获取组件的所有版本
描述：根据组件唯一标识获取组件的版本信息
POST 	/sca/v1/components/versions/query
Content-Type: application/json

请求参数
参数	类型	长度/大小	是否必填	描述
componentUuid	String	80	是	组件唯一标识
pageIndex	number	大于0	是	当前页面下标
pageSize	number	1~ 500	是	每页展示行数
version	String	20	否	组件版本
startTime	String	20	否	开始时间 
(yyyy-MM-dd)
endTime	String	20	否	结束时间
(yyyy-MM-dd)
(startTime,endTime同时有值才生效)


响应参数：
名称	名称1	名称2	名称3	类型	业务描述
status				Number	返回状态码
data	total			Number	总数
	dataList	version		String	版本
		vulNum		Number	漏洞数量
		grade		String	风险级别字典值 字典编码:JARRISK(详情见接口3.54)
		releaseDate		Number	发布时间
		licenses	licenseId	String	许可简称
			licenseName	String	许可全称
	pageIndex			Number	当前页面下标
	pageSize			Number	每页展示行数
msg				String	返回信息

代码示例
请求正文：
{
    "componentUuid":"99e215cf80e442408ce92e31c5e90a0a123",
    "pageIndex":1,
    "pageSize":1
}

{
    "status": 1,
    "data": {
        "total": 80,
        "dataList": [
            {
                "version": "2.67.0",
                "vulNum": null,
                "grade": null,
                "releaseDate": 1668949170000,
                "licenses": [
                    {
                        "licenseId": "Apache-2.0",
                        "licenseName": "Apache License 2.0"
                    }
                ]
            }
        ],
        "pageIndex": 1,
        "pageSize": 1
    },
    "msg": "OK"
}


3.39组件--获取组件的所有漏洞
描述：根据组件唯一标识获取组件的所有漏洞
POST 	/sca/v1/components/vuls/query
Content-Type: application/json

请求参数
参数	类型	长度/大小	是否必填	描述
componentUuids	Array		是	组件唯一标识
pageIndex	number	大于0	是	当前页面下标
pageSize	number	1~ 500	是	每页展示行数


响应参数：
名称	名称1	名称2	类型	业务描述
status			Number	返回状态码
data	total		Number	总数
	dataList	componentUuid	String	组件唯一标识
		customSzNo	String	sz编号
		customCveNo	String	cve编号
		customCnnvdNo	String	cnnvd编号
		customCnvdNo	String	cnvd编号
		grade	String	风险级别字典值 字典编码:JARRISK(详情见接口3.54)
	pageIndex		Number	当前页面下标
	pageSize		Number	每页展示行数
msg			String	返回信息

代码示例
请求正文：
{
    "componentUuids": ["3351408df9f8d75fa5b2193c38b621a3ebe0e807ed778cd6c8ce3ecd453c1516","60905f441e7f1bf858ddbc8ec2b5c6c634c3952b32a8731c5114da959902e9b6"],
    "pageIndex":1,
    "pageSize":1
}

{
    "status": 1,
    "data": {
        "total": 2,
        "dataList": [
            {
                "componentUuid": "3351408df9f8d75fa5b2193c38b621a3ebe0e807ed778cd6c8ce3ecd453c1516",
                "customSzNo": "SZ-2022-14393",
                "customCveNo": "CVE-2022-42889",
                "customCnnvdNo": "CNNVD-202210-790",
                "customCnvdNo": "CNVD-2022-73686",
                "grade": "CRITICAL"
            }
        ],
        "pageIndex": 1,
        "pageSize": 1
    },
    "msg": "OK"
}


3.40许可--许可列表
描述：获取用户下的许可列表
GET  /sca/v1/licenses?pageIndex=xx&pageSize=yy

请求参数:
参数	类型	长度/大小	是否必填	描述
pageIndex	number	大于0	是	当前页面下标
pageSize	number	1~ 500	是	每页展示行数
projectUuid	String	80	否	项目唯一标识
appUuid	String	64	否	应用唯一标识
componentUuid	String	64	否	组件唯一标识
licenseId	String	20	否	许可简称


响应参数：
名称	名称1	名称2	类型	业务描述
status			Number	返回状态码
data	total		Number	总数
	dataList	controlStatus	Number	黑白名单状态字典值  字典编码:LICENSECONTROLSTATUS(详情见接口3.54) 
		grade	String	风险级别字典值 字典编码:LICENSERISK(详情见接口3.54)
		licenseId	String	许可简称
		licenseName	String	许可全称
	pageIndex		Number	当前页面下标
	pageSize		Number	每页展示行数
msg			String	返回信息

代码示例
返回示例：
{
    "status": 1,
    "data": {
        "total": 116,
        "dataList": [
            {
                "controlStatus": 0,
                "grade": "HIGH",
                "licenseId": "GPL",
                "licenseName": "GNU General Public License"
            }
        ],
        "pageIndex": 1,
        "pageSize": 10
    },
    "msg": "OK"
}


3.41许可--许可详情
描述：根据许可简称获取许可详情
POST 	/sca/v1/license
Content-Type: application/json

请求参数
参数	类型	长度/大小	是否必填	描述
licenseId	String	80	是	许可简称


响应参数：
名称	名称1	名称2	类型	业务描述
status			Number	返回状态码
data	detailsUrl		String	详细地址
	grade		String	 风险等级字典值 字典编码:LICENSERISK(详情见接口3.54) 
	reference		String	参考地址
	licenseName		String	许可全称
	licenseId		String	许可简称
	osi		String	OSI Approved 字典值 字典编码:COMFIRM
	fsf		String	FSF Free/Libre 字典值 字典编码:COMFIRM
	description		String	描述信息
	allow	permissions	String	许可权限（允许）
		description	String	许可权限（允许描述）
	attention	permissions	String	许可权限（注意）
		description	String	许可权限（注意描述）
	must	permissions	String	许可权限（必须）
		description	String	许可权限（必须描述）
	expire		String	是否过期 字典值 字典编码:ISDEPRECATEDLICENSEID
msg			String	返回信息

代码示例
请求正文：
{
    "licenseId": "Apache-2.0"
}

返回示例：
{
    "status": 1,
    "data": {
        "detailsUrl": "http://spdx.org/licenses/Apache-2.0.json",
        "grade": "NONE",
        "reference": "http://spdx.org/licenses/Apache-2.0.html",
        "licenseName": "Apache License 2.0",
        "licenseId": "Apache-2.0",
        "osi": "1",
        "fsf": "1",
        "description":"Apache License\n\nVersion 2.0, January 2004\n\nhttp://www.apache.org/licenses/ TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION\n\n   1. Definitions.\n\n      \n\n      \"License\" shall mean the terms and conditions for use, reproduction, and distribution as defined by Sections 1 through 9 of this document. See the License for the specific language governing permissions and\n\nlimitations under the License.",
        "allow": [
            {
                "permissions": "商业用途",
                "description": "描述使用该软件做商业用途的能力"
            }
        ],
        "attention": [
            {
                "permissions": "承担责任",
                "description": "（对质量保证进行描述和是否软件/许可的持有者会因为损坏而承担费用）描述保修以及是否可以向软件/许可证所有者收取损害赔偿。"
            }
        ],
        "must": [
            {
                "permissions": "包含版权",
                "description": "必须明显地包含原始版权以及免责声明。"
            }
        ],
        "expire": "0"
    },
    "msg": "OK"
}


3.42漏洞--漏洞列表
描述：获取用户下的漏洞列表
GET  /sca/v1/vulnerabilities?pageIndex=xx&pageSize=yy

请求参数:
参数	类型	长度/大小	是否必填	描述
pageIndex	number	大于0	是	当前页面下标
pageSize	number	1~ 500	是	每页展示行数
projectUuid	String	80	否	唯一标识
appUuid	String	64	否	应用唯一标识
componentUuid	String	64	否	组件唯一标识
number	String	30	否	customSzNo/customCveNo/customCnnvdNo过滤条件


响应参数：
名称	名称1	名称2	类型	业务描述
status			Number	返回状态码
data	total		Number	总数
	dataList	customSzNo	String	sz编号
		customCveNo	String	Cve编号
		customCnnvdNo	String	Cnnvd编号
		affectComponentCount	Number	影响组件数
		customCnvdNo	String	Cnvd编号
		grade	String	风险等级字典 字典编码:CVERISK(详情见接口3.54) 
		cwe	String	弱点类型编号
		vulnerabilityName	String	漏洞名称
		cweName	String	弱点类型名称
		description	String	漏洞描述
		releaseDate	Number	发布时间
	pageIndex		Number	当前页面下标
	pageSize		Number	每页展示行数
msg			String	返回信息

代码示例
返回示例：
{
    "status": 1,
    "data": {
        "total": 116,
        "dataList": [
            {
                "customSzNo": "SZ-2022-18767",
                "customCveNo": "CVE-2022-42252",
                "customCnnvdNo": "CNNVD-202210-2602",
                "customCnvdNo": "CNVD-2022-74082",
                "grade": "HIGH",
                "cwe": "CWE-20",
                "vulnerabilityName": "Apache Tomcat 环境问题漏洞",
                "cweName": "Improper Input Validation",
                "description": "If Apache Tomcat 8.5.0 to 8.5.82, 9.0.0-M1 to 9.0.67, 10.0.0-M1 to 10.0.26 or 10.1.0-M1 to 10.1.0 was configured to ignore invalid HTTP headers via setting rejectIllegalHeader to false (the default for 8.5.x only), Tomcat did not reject a request containing an invalid Content-Length header making a request smuggling attack possible if Tomcat was located behind a reverse proxy that also failed to reject the request with the invalid header.",
                "releaseDate": 1667265300000
            }
        ],
        "pageIndex": 1,
        "pageSize": 10
    },
    "msg": "OK"
}


3.43漏洞--漏洞详情
描述：根据sz编号获取漏洞详情
GET 	/sca/v1/vulnerability/{customNo}

请求参数
参数	类型	长度/大小	是否必填	描述
customNo	String	30	是	sz编号


响应参数：
名称	名称1	名称2	类型	业务描述
status			Number	返回状态码
data	customSzNo		String	sz编号
	customCveNo		String	Cve编号
	customCnnvdNo		String	Cnnvd编号
	customCnvdNo		String	Cnvd编号
	grade		String	风险等级字典  字典编码:CVERISK(详情见接口3.54)
	cwe		String	Cwe
	description		String	漏洞描述
	vulnerabilityName		String	漏洞名称
	cweName		String	弱点类型名称
	releaseDate		Number	发布时间
	updateDate		Number	更新时间
	announcement		String	漏洞建议
	referenceVoList	source	String	来源
		link	String	链接
	cvss3	baseScore	String	cvss3基础分数
		impact	String	cvss3影响
		developAble	String	cvss3可开发性
		attackWay	String	cvss3攻击途径
		attackComplexity	String	cvss3攻击复杂度
		userInteraction	String	cvss3用户交互
		usabilityImpact	String	cvss3可用性影响
		integrityEffect	String	cvss3完整性影响
		confidentialityImpact	String	cvss3机密性影响
		scope	String	cvss3作用域
		permission	String	cvss3权限要求
	cvss2	baseScore	String	cvss2基础分数
		impact	String	cvss2影响
		developAble	String	cvss2可开发性
		attackWay	String	cvss2攻击途径
		attackComplexity	String	cvss2攻击复杂度
		usabilityImpact	String	cvss2可用性影响
		integrityEffect	String	cvss2完整性影响
		confidentialityImpact	String	cvss2机密性影响
		authentication	String	Cvss2认证
	cpe		Arrar	cpe
	isRepair		String	是否修复
	utilization		String	EXP成熟度
	cnnvdPatchPo	patchId	String	补丁编号
		title	String	补丁名称
		link	String	补丁链接地址
		publishDate	String	补丁发布时间
		szId	String	该补丁所修复的漏洞编号
		referenceUrl	String	参考网址
	vulUseDifficulty		String	漏洞利用难度
	pocusesTime		String	poc利用时间
msg			String	返回信息

代码示例
返回示例：
{
    "status": 1,
    "data": {
        "customSzNo": "SZ-2022-18767",
        "customCveNo": "CVE-2022-42252",
        "customCnnvdNo": "CNNVD-202210-2602",
        "customCnvdNo": "CNVD-2022-74082",
        "grade": "HIGH",
        "cwe": "CWE-20",
        "description": "If Apache Tomcat 8.5.0 to 8.5.82, 9.0.0-M1 to 9.0.67, 10.0.0-M1 to 10.0.26 or 10.1.0-M1 to 10.1.0 was configured to ignore invalid HTTP headers via setting rejectIllegalHeader to false (the default for 8.5.x only), Tomcat did not reject a request containing an invalid Content-Length header making a request smuggling attack possible if Tomcat was located behind a reverse proxy that also failed to reject the request with the invalid header.",
        "vulnerabilityName": "Apache Tomcat 环境问题漏洞",
        "cweName": "Improper Input Validation",
        "releaseDate": 1667265300000,
        "updateDate": 1668744900000,
        "announcement": "目前厂商已发布升级补丁以修复漏洞，详情请关注厂商主页： \nhttps://tomcat.apache.org/security-8.html",
        "referenceVoList": [
            {
                "source": "https://lists.apache.org/thread/zzcxzvqfdqn515zfs3dxb7n8gty589sq",
                "link": "https://lists.apache.org/thread/zzcxzvqfdqn515zfs3dxb7n8gty589sq"
            }
        ],
        "cvss3": {
            "baseScore": "7.5",
            "impact": "3.6",
            "developAble": "3.9",
            "attackWay": "NETWORK",
            "attackComplexity": "LOW",
            "userInteraction": null,
            "usabilityImpact": null,
            "integrityEffect": "HIGH",
            "confidentialityImpact": null,
            "scope": "UNCHANGED",
            "permission": null
        },
        "cvss2": {
            "baseScore": null,
            "impact": null,
            "developAble": null,
            "attackWay": null,
            "attackComplexity": null,
            "usabilityImpact": null,
            "integrityEffect": null,
            "confidentialityImpact": null,
            "authentication": null
        },
        "cpe": [
            "cpe:2.3:a:apache:tomcat:*:*:*:*:*:*:*:*"
        ],
        "isRepair": "否",
        "utilization": null,
        "cnnvdPatchPo": [],
        "vulUseDifficulty": "中",
        "pocusesTime": ""
    },
    "msg": "OK"
} 


3.44检测任务--检测任务列表
描述：企业管理员用户获取所有的检测任务列表，非企业管理员获取用户所属机构下的检测任务列表
GET  /sca/v1/tasks?pageIndex=xx&pageSize=yy&state=yy

请求参数:
参数	类型	长度/大小	是否必填	描述
pageIndex	number	大于0	是	当前页面下标
pageSize	number	1~ 500	是	每页展示行数
state	number	10	是	状态 1 检测中，2 已检测， 3 待检测
checkNo	String	80	否	任务编号


响应参数：
名称	名称1	名称2	类型	业务描述
status			Number	返回状态码
data	total		Number	总数
	dataList	checkNo	String	任务编号
		type	String	所属类型 字典编码:CHECKPLATFORM(详情见接口3.54)
		projectName	String	所属项目
		projectUuid	String	项目唯一标识
		appName	String	应用名称/私服
		appUuid	String	应用唯一标识
		state	String	状态 字典编码:SOURCECHECKSTATUS(详情见接口3.54)
		submitDate	String	提交时间
		sourceType	String	来源，字典编码:APPMAINSOURCE(详情见接口3.54)
	pageIndex		Number	当前页面下标
	pageSize		Number	每页展示行数
msg			String	返回信息

代码示例
返回示例：
{
    "status": 1,
    "data": {
        "total": 6459,
        "dataList": [
            {
                "checkNo": "2022122801000268210022936",
                "type": "PLATFORM",
                "projectName": "jiang-dev-protect-001",
                "projectUuid": "f7c6068be11a4396aea9410d5120b3fa",
                "appName": "dddddd",
                "appUuid": "a66bdcda2e8e4970918dcbd8dcd1560f",
                "state": "2",
                "submitDate": "2022-12-28 01:00:24",
"sourceType": " localupload "
            }
        ],
        "pageIndex": 1,
        "pageSize": 10
    },
    "msg": "OK"
}


3.45报告--报告列表查询
描述：获取用户下的报告列表
GET  /sca/v1/reports?pageIndex=xx&pageSize=yy

请求参数:
参数	类型	长度/大小	是否必填	描述
pageIndex	number	大于0	是	当前页面下标
pageSize	number	1~ 500	是	每页展示行数
docName	String	80	否	文档名称


响应参数：
名称	名称1	名称2	类型	业务描述
status			Number	返回状态码
data	total		Number	总数
	dataList	reportType	Number	报告类型 字典编码:REPORTTYPE(详情见接口3.54)
		reportModel	Number	报告格式 字典编码:REPORTMODEL(详情见接口3.54)
		reportStatus	Number	报告生成状态 字典编码:REPORTSTATUS(详情见接口3.54)
		reportName	String	报告名称
		createUser	String	创建人
		createTime	Number	创建时间
		id	Number	报告记录主键值
		reportUuid	String	报告唯一标识，下载用
	pageIndex		Number	当前页面下标
	pageSize		Number	每页展示行数
msg			String	返回信息

代码示例
返回示例：
{
    "status": 1,
    "data": {
        "total": 1291,
        "dataList": [
            {
                "reportType": 6,
                "reportModel": 1,
                "reportStatus": 1,
                "reportName": "12345678",
                "createUser": "<EMAIL>",
                "createTime": 1671093503000,
                "id": 2934,
                "reportUuid": "X0dOsUC/ERYUXVBwwcyO1A=="
            }
        ],
        "pageIndex": 1,
        "pageSize": 10
    },
    "msg": "OK"
}


3.46报告--报告删除（已过期）
描述：根据报告唯一标识删除报告
DELETE  	/sca/v1/reports/download/{reportUuid}

请求参数
参数	类型	长度/大小	是否必填	描述
reportUuid	编号	64	是	报告唯一标识
代码示例
响应参数
名称	类型	业务描述
status	Number	返回状态码
msg	String	返回信息

代码示例
返回示例：
{
"status": 1,
"msg": "OK"
}
接口变更说明

/reports/download/{reportUuid} 接口有转义问题，url转义后易触发安全策略（1. Spring框架不允许， 2. 企业防火墙不允许）（触发“url注入攻击”的防御机制）
修改成/reports/download DELETE json后 httpClient不支持，不利于对方开发对接
修改成/reports/download POST json与 “3.47报告--报告下载” 冲突
故改成
/reports/delete POST json



3.46报告--报告删除
描述：根据报告唯一标识删除报告
POST  	/sca/v1/reports/delete

请求参数
参数	类型	长度/大小	是否必填	描述
reportUuid	编号	64	是	报告唯一标识
代码示例
请求正文：
{
    "reportUuid":" OB3r/zv1uVf7D6WhYiUSig=="
}

响应参数
名称	类型	业务描述
status	Number	返回状态码
msg	String	返回信息

代码示例
返回示例：
{
"status": 1,
"msg": "OK"
}


3.47报告--报告下载
描述：根据报告唯一标识下载报告
POST  	/sca/v1/reports/download
Content-Type: application/json

请求参数
参数	类型	长度/大小	是否必填	描述
reportUuid	编号	64	是	报告唯一标识

代码示例
请求正文：
{
    "reportUuid":" OB3r/zv1uVf7D6WhYiUSig=="
}


3.48定时任务--定时任务列表
描述：根据项目唯一标识或者应用唯一标识获取其对应的定时任务列表
GET  /sca/v1/schedules?projectUuid=&appUuid=

请求参数
参数	类型	长度/大小	是否必填	描述
projectUuid	String	80	否(与appUuid二选一)	项目唯一标识
appUuid	String	80	否(与projectUuid二选一)	应用唯一标识

响应参数
名称	名称1	类型	业务描述
status		Number	返回状态码
data	name	String	定时任务类型名
	scheduleType	String	定时任务所属类型
	implement	Number	定时任务状态（0、未启动，1、启动）
	regularTime	String	定时任务指定日期类型，指定的具体的时间
	scheduleTaskType	String	定时任务周期类型的维度（1、周，2、日，3、月）
	periodTime	String	周期定时任务执行的具体时间
	dayofMonth	String	定时任务周期类型以月为维度，执行的日期
	dayofWeek	String	定时任务周期类型以周为单位，执行的日期
msg		String	返回信息

返回示例：
{
"status": 1,
"data":[
        {
            "name": "定时",
            "scheduleType": "项目设置",
            "implement": 1,
            "regularTime": "2022-08-26 15:05:20",
            "scheduleTaskType": null,
            "periodTime": null,
            "dayofMonth": null,
            "dayofWeek": null
        },
        {
            "name": "周期",
            "scheduleType": "项目设置",
            "implement": 1,
            "regularTime": null,
            "scheduleTaskType": "2",
            "periodTime": "1",
            "dayofMonth": null,
            "dayofWeek": null
        }
],
"msg": "OK"
}


3.49定时任务--定时任务设置
描述：新增定时任务设置
POST  /sca/v1/schedules
Content-Type: application/json

请求参数
参数	类型	长度/大小	是否必填	描述
timeType	Number		是	定时任务类型（1、定时，2、周期）
projectUuid	String	64	否(与appUuid二选一)	项目唯一标示符
appUuid	String	64	否(与projectUuid二选一)	应用唯一标示符
periodTime	String	10	否	定时任务周期维度的检测时间（）具体执行的时刻---小时，如果是定时任务，此参数不传）
scheduleTaskType	Number	10	否	定时检测周期维度类型（1、周，2、日，3、月）
dayofMonth	String	10	否	定时检测周期维度以月为单位执行的日期
dayofWeek	Array	10	否	定时检测周期维度以周为单位执行的日期（输入1-7，1为周日，2为周一……以此类推）
regularTime	Long	1 - 2^63 -1	否	定时检测指定日期维度，指定的具体日期（时间戳）

响应参数
名称	类型	业务描述
status	Number	返回状态码
msg	String	返回信息

代码示例
请求正文：
周期检测类型
{
"timeType":2,
"appUuid":"deeee25bd4534e2e9a7fafc908cb7f9f",
    "scheduleTaskType":2,
    "periodTime":"15"
}
定时检测类型
{
"timeType":1,
"appUuid":"deeee25bd4534e2e9a7fafc908cb7f9f",
    "regularTime":2
}

返回示例：
{
"status": 1,
"msg": "OK"
}


3.50定时任务--定时任务开启/关闭
描述：设置定时任务为 开启/关闭
PUT  /sca/v1/schedules 
Content-Type: application/json

请求参数
参数	类型	长度/大小	是否必填	描述
timeType	Number		是	定时任务类型（1、定时，2、周期）
type	Number		是	开启/取消定时任务检测
1:开启  2:关闭
projectUuid	String	64	否(与appUuid二选一)	项目唯一标示符
appUuid	String	64	否(与projectUuid二选一)	应用唯一标示符

响应参数
名称	类型	业务描述
status	Number	返回状态码
msg	String	返回信息

代码示例
请求正文：
周期检测类型
{
"timeType":2,
"type":2,
"appUuid":"deeee25bd4534e2e9a7fafc908cb7f9f"
}

返回示例：
{
"status": 1,
"msg": "OK"
}


3.51预警--漏洞预警查询
描述：获取用户下的漏洞预警列表
GET  /sca/v1/warnings/vulnerabilities/components?pageIndex=1&pageSize=10

请求参数:
参数	类型	长度/大小	是否必填	描述
pageIndex	number	大于0	是	当前页面下标
pageSize	number	1~ 500	是	每页展示行数
day	String	64	否	更新时间
（支持格式：2022-01-01）


响应参数：
名称	名称1	名称2	类型	业务描述
status			Number	返回状态码
data	total		Number	总数
	dataList	customSzNo	String	sz编号
		customCveNo	String	Cve编号
		customCnnvdNo	String	Cnnvd编号
		customCnvdNo	String	Cnvd编号
		cwe	String	弱点类型编号
		desc	String	漏洞描述
		componentUuid	String	组件唯一标识
		groupId	String	groupId
		artifactId	String	artifactId
		version	String	版本
		language	String	语言
		type	String	VUL_ADD 漏洞新增 /  VUL_UPDATE 漏洞更新
		createDate	String	创建时间
		zeroDay	String	0：48小时内的0day漏洞
1：24小时内的0day漏洞
2：非0day漏洞
		customSzNo	String	sz编号
	pageIndex		Number	当前页面下标
	pageSize		Number	每页展示行数
msg			String	返回信息

代码示例
返回示例：
{
    "status": 1,
    "data": {
        "total": 6761,
        "dataList": [
            {
                "customSzNo": "SZ-2021-27611",
                "customCveNo": "",
                "customCnnvdNo": "",
                "customCnvdNo": "",
                "cwe": "CWE-Unknown",
                "desc": "Javascript implementation of zip for nodejs with support for electron original-fs. Allows user to create or extract zip files both in memory or to/from disk",
                "componentUuid": "f31ae2f6ad35034834e82f771bab10a3b25e0dcb83c14eb00ded698c12be0920",
                "groupId": "",
                "artifactId": "adm-zip",
                "version": "0.4.16",
                "language": "JavaScript",
                "jarInfoAddFrom": "npm",
                "type": "VUL_UPDATE",
                "createDate": "2022-12-16 10:34:09",
                "zeroDay": "2"
            }
        ],
        "pageIndex": 1,
        "pageSize": 10
    },
    "msg": "OK"
}


3.52预警--站内信查询
描述：站内信查询
GET  /sca/v1/warnings/statistics?day=

请求参数:
参数	类型	长度/大小	是否必填	描述
day	String	64	是	更新时间
（支持格式：2022-01-01）


响应参数：
名称	名称1	类型	业务描述
status		Number	返回状态码
data	vulCount	Number	漏洞数量
	vulComponent	Number	组件漏洞数量
	syncDay	String	同步时间
	type	Number	类型
msg		String	返回信息

代码示例
返回示例：
{
    "status": 1,
    "data": {
        "vulCount": 0,
        "vulComponent": 0,
        "syncDay": "0",
        "type": 1
    },
    "msg": "OK"
}


3.53版本--产品版本信息
描述：获取产品版本信息
GET  /sca/v1/versions

响应参数：
名称	名称1	类型	业务描述
status		Number	返回状态码
data	dataVersion	String	数据版本
	engineVersion	String	引擎版本
	platformVersion	String	平台版本
msg		String	返回信息

代码示例
返回示例：
{
    "status": 1,
    "data": {
        "dataVersion": "",
        "engineVersion": "",
        "platformVersion": ""
    },
    "msg": "OK"
}


3.54字典--通过字典编码获取字典
描述：通过字典编码获取字典信息
GET  /sca/v1/dictionaries/details?dicType=

请求参数:
参数	类型	长度/大小	是否必填	描述
dicType	String	20	是	字典编码


响应参数：
名称	名称1	类型	业务描述
status		Number	返回状态码
data	dicCode	String	字典值
	dicName	String	字典名称
	order	Number	排序
msg		String	返回信息

代码示例
返回示例：
{
    "status": 1,
    "data": [
        {
            "dicCode": "0",
            "dicName": "轻危",
            "order": 1
        },
        {
            "dicCode": "1",
            "dicName": "中危",
            "order": 2
        }
    ],
    "msg": "OK"
}


3.55调用数据中心获取组件信息
描述：调用数据中心获取组件信息
POST  /sca/v1/component/detail
Content-Type: application/json

请求参数
参数	类型	是否必填	描述
name	String	否(与componentId二选一)	软件名称
软件名称:组件版本拼接
如：FILEUPLOAD:1.3.2
componentId	String	否(与name二选一)	g:a:v:l拼接
如commons-fileupload:commons-fileupload:1.3.2:Java
（语言使用字典COMPUTERLANGUAGE 的字典名称）

响应参数
名称	名称1	名称2	类型	业务描述
status			Number	返回状态码
data	groupId		String	g 
	artifactId		String	a
	version		String	v
	language		String	l
	hash		String	hash
	grade		String	风险等级
	gradeName		String	风险等级
	gradeDesc		String	风险描述
	projectDeveloper		String	开发者
	projectScmUrl		String	源码链接
	projectUrl		String	主页链接
	description		String	描述
	softWareName		String	软件名称
	releaseDate		String	最早发布时间
	latestReleaseDate		String	最新发布时间
	uuid		String	组件uuid
	newestVersion		String	最新版本
	latelyVersion		String	最近版本
	licenses		Array	许可
	vulnerables	customCveNo	String	Cve编号
		customCnnvdNo	String	Cnnvd编号
		customCnvdNo	String	Cnvd编号
		customSzNo	String	Sz编号
		componentUuid	String	组件uuid
		severity	String	风险等级
		description	String	描述
		publishedDate	String	发布时间
		score	String	基础分数
		scoreV2	String	Cvss2分数
		scoreV3	String	Cvss3分数
		g	String	g
		a	String	a
		v	String	v
		hash	String	Hash
		language	String	l
		cwe	String	Cwe信息
		exploitabilityScoreV2	String	
		exploitabilityScoreV3	String	
		nameCN	String	漏洞名称中文
		nameUS	String	漏洞名称英文
		availableDifficulty	String	漏洞利用难度

代码示例
请求正文：
{
"name": " FILEUPLOAD:1.3.2"
}

返回示例：
{
    "status": 1,
    "data": {
        "groupId": "commons-fileupload",
        "artifactId": "commons-fileupload",
        "version": "1.3.2",
        "language": "Java",
        "hash": "5d7491ed6ebd02b6a8d2305f8e6b7fe5dbd95f72",
        "grade": "CRITICAL",
        "gradeName": "超危",
        "gradeDesc": "存在超危风险的漏洞:CVE-2016-1000031,CNNVD-201610-475,SZ-2016-01021;",
        "projectDeveloper": null,
        "projectScmUrl": "https://git-wip-us.apache.org/repos/asf?p=commons-fileupload.git",
        "projectUrl": "http://commons.apache.org/proper/commons-fileupload/",
        "description": "The Apache Commons FileUpload component provides a simple yet flexible means of adding support for multipart\n    file upload functionality to servlets and web applications.",
        "softWareName": "FILEUPLOAD",
        "releaseDate": "2016-05-23 22:48:09",
        "latestReleaseDate": "2018-12-24 15:07:08",
        "uuid": "8b89dd52ba5dd636c5224fe3f29e5b6ada86e5ed236f002cda57b2cf32587358",
        "newestVersion": "1.4",
        "latelyVersion": "1.3.3",
        "licenses": [
            "Apache-2.0"
        ],
        "vulnerables": [
            {
                "customCveNo": "CVE-2016-1000031",
                "customCnnvdNo": "CNNVD-201610-475",
                "customCnvdNo": "CNVD-2016-09997",
                "customSzNo": "SZ-2016-01021",
                "componentUuid": "8b89dd52ba5dd636c5224fe3f29e5b6ada86e5ed236f002cda57b2cf32587358",
                "severity": "CRITICAL",
                "description": "Apache Commons FileUpload是美国阿帕奇（Apache）基金会的一个可将文件上传到Servlet和Web应用程序的软件包。 \nApache Commons FileUpload 1.3.3之前版本中存在访问控制错误漏洞。该漏洞源于网络系统或产品未正确限制来自未授权角色的资源访问。",
                "publishedDate": "2016-10-25",
                "score": "9.8",
                "scoreV2": "7.5",
                "scoreV3": "9.8",
                "g": "commons-fileupload",
                "a": "commons-fileupload",
                "v": "1.3.2",
                "hash": "5d7491ed6ebd02b6a8d2305f8e6b7fe5dbd95f72",
                "language": "MAVEN",
                "cwe": "CWE-284",
                "exploitabilityScoreV2": "10.0",
                "exploitabilityScoreV3": "3.9",
                "nameCN": "Apache Commons FileUpload 访问控制错误漏洞",
                "nameUS": "Arbitrary Code Execution",
                "availableDifficulty": "MEDIUM"
            }
        ]
    }
}


3.56获取ca许可的详细信息
描述：获取ca许可的详细信息
GET  /sca/v1/license/detail

响应参数：
名称	名称1	类型	业务描述
status		Number	返回状态码
data	checkedCount	Number	已使用检测次数
	maxInputUserNumber	String	最大用户数
	maxInputDetectionNumber	String	最大检测次数
	appName	String	应用名称
	errorReason	String	错误原因
	existAppCount	Number	已使用应用数
	maxInputApplicationNumber	String	最大应用数
	expireDate	Number	过期时间
	orgUserNum	Number	已使用用户数
	startDate	Number	开始时间
	status	Number	状态 1 正常 0 异常

代码示例
返回示例：
{
    "status": 1,
    "data": {
        "checkedCount": 472,
        "maxInputUserNumber": "无限制",
        "maxInputDetectionNumber": "无限制",
        "appName": "SourceCheck",
        "errorReason": "",
        "existAppCount": 55,
        "maxInputApplicationNumber": "无限制",
        "expireDate": 1672217472525,
        "orgUserNum": 65,
        "startDate": 1672217472525,
        "status": 1
    }
}


3.57容器--创建容器检测
描述：根据容器名称和上传的附件创建容器并对附件进行检测，要求：容器名称不能重复
POST  /sca/v1/docker
Content-Type: multipart/form-data

请求参数
参数	类型	长度/大小	是否必填	描述
appName	String	80	是	容器名称
file	MultipartFile	2G	是	附件


响应参数
名称	名称1	类型	业务描述
status		Number	返回状态码

代码示例
请求正文：



返回示例：
{
"status": 1
}


3.58容器--容器列表
描述：获取用户下的容器列表
GET  /sca/v1/dockers?pageIndex=xx&pageSize=yy

请求参数:
参数	类型	长度/大小	是否必填	描述
pageIndex	number	大于0	是	当前页面下标
pageSize	number	1~ 500	是	每页展示行数
name	String	64	否	容器名称


响应参数：
名称	名称1	名称2	类型	业务描述
status			Number	返回状态码
data	pageIndex		Number	当前页面下标
	pageSize		Number	每页展示行数
	total		Number	总数
	data	name	String	容器名称
		grade	String	风险等级
		gradeName	String	风险等级
		appId	Number	
		gradeDesc	String	风险等级描述
		appUuid	String	唯一标识
		uploadAppStatus	String	状态
		uploadAppStatusDesc	String	状态
		errorMessage	String	错误原因
		description	String	描述
		uploadTime	Long	检测时间

代码示例
返回示例：
{
    "status": 1,
"data": {
		"pageIndex": 1,
        "pageSize": 10,
        "total": 52,
        "data": [
            {
                "name": "3",
                "grade": "LOW",
                "gradeName": "低危--超超超",
                "appId": 3,
                "gradeDesc": "存在低危风险的组件【38】个",
                "appUuid": "46bff691d60442f59d5d3524d3c12793",
                "uploadAppStatus": "2",
                "uploadAppStatusDesc": "已检测",
                "errorMessage": null,
                "description": "3423",
                "uploadTime": 1627281238000
            }
        ]
    }
}


3.59容器--容器删除
描述：根据容器唯一标识删除容器
DELETE  /sca/v1/docker/{appUuid}

请求参数
参数	类型	长度/大小	是否必填	描述
appUuid	String	64	是	容器唯一标识

响应参数
名称	类型	业务描述
status	Number	返回状态码
msg	String	返回信息

代码示例
返回示例：
{
"status": 1,
"msg": "OK"
}


3.60容器--获取容器的详细信息
描述：根据容器唯一标识获取容器详情
GET 	/sca/v1/docker/{appUuid}

请求参数
参数	类型	长度/大小	是否必填	描述
appUuid	String	64	是	容器唯一标识


响应参数：
名称	名称1	类型	业务描述
status		Number	返回状态码
data	name	String	容器名称
	grade	String	风险等级
	gradeName	String	风险等级
	appId	Number	
	gradeDesc	String	风险等级描述
	appUuid	String	唯一标识
	uploadAppStatus	String	状态
	uploadAppStatusDesc	String	状态
	errorMessage	String	错误原因
	description	String	描述
	uploadTime	Long	检测时间
msg		String	返回信息

代码示例
返回示例：
{
    "status": 1,
    "data": {
        "name": "3",
        "grade": "LOW",
        "gradeName": "低危--超超超",
        "appId": 3,
        "gradeDesc": "存在低危风险的组件【38】个",
        "appUuid": "46bff691d60442f59d5d3524d3c12793",
        "uploadAppStatus": "2",
        "uploadAppStatusDesc": "已检测",
        "errorMessage": null,
        "description": "3423",
        "uploadTime": 1627281238000
    },
    "msg": "OK"
}


3.61获取应用下的检测历史
描述：根据应用唯一标识获取应用下的检测历史
GET  /sca/v1/application/history?pageIndex=xx&pageSize=yy&appUuid=zz

请求参数:
参数	类型	长度/大小	是否必填	描述
pageIndex	number	大于0	是	当前页面下标
pageSize	number	1~ 500	是	每页展示行数
appUuid	String	64	是	应用唯一标识
checkNo	String	80	否	检测编号


响应参数：
名称	名称1	名称2	类型	业务描述
status			Number	返回状态码
data	pageIndex		Number	当前页面下标
	pageSize		Number	每页展示行数
	total		Number	总数
	data	vulCount	Number	漏洞数量
		compCount	Number	影响组件数量
		checkNo	String	扫描编号
		changeDesc	String	扫描结果
		licCount	Number	许可数量
		email	String	邮箱

代码示例
返回示例：
{
    "status": 1,
"data": {
		"pageIndex": 1,
        "pageSize": 10,
        "total": 3,
        "data": [
            {
                "vulCount": 0,
                "compCount": 121,
                "checkNo": "2022122910284510510039832",
                "changeDesc": "新增1个组件，2个组件有变更，删除9个组件",
                "licCount": 10,
                "email": "<EMAIL>"
            }
        ]
    }
}


3.62应用--应用下组件修复建议
描述：根据应用唯一标识获取应用下组件修复建议
POST  /sca/v1/application/component/opinionList
Content-Type: multipart/form-data

请求参数:
参数	类型	长度/大小	是否必填	描述
pageIndex	number	大于0	是	当前页面下标
pageSize	number	1~ 500	是	每页展示行数
appUuid	String	64	是	应用唯一标识
componentUuid	String	80	否	组件uuid


响应参数：
名称	名称1	名称2	名称3	类型	业务描述
status				Number	返回状态码
data	pageIndex			Number	当前页面下标
	pageSize			Number	每页展示行数
	total			Number	总数
	data	children	classify	Number	漏洞类型标识
			count	Number	漏洞数量
			cveGrade	String	漏洞风险类型
			cveGradeName	String	漏洞风险类型中文名
			countDesc	String	漏洞统计描述
		jarName		String	组件名称
		version		String	组件版本
		uuid		String	组件Uuid
		compomengVersionDetailVoList	depRank	String	依赖等级
			depSource	String	引用类型标识
			depSourceName	String	引用类型名称
			depScope	String	作用域
			desc	String	修复建议描述

代码示例
返回示例：
{
    "status": 1,
"data": {
		"pageIndex": 1,
        "pageSize": 10,
        "total": 3,
        "data": [
            {
                "children": [
                    {
                        "classify": 1,
                        "count": 2,
                        "cveGrade": "CRITICAL",
                        "cveGradeName": "超危--超超超",
                        "countDesc": "超危--超超超漏洞2条"
                    }
                ],
                "jarName": "fastjson",
                "version": "1.1.18",
                "uuid": "3a48e0df2421ddbe44587cedf9837625e4f088c20f96ef149450aec52eec71d3",
                "compomengVersionDetailVoList": [
                    {
                        "depRank": "直接依赖",
                        "depSource": null,
                        "depSourceName": "配置文件引入",
                        "depScope": "provided",
                        "desc": "在/META-INF/maven/com.alibaba/dubbo/pom.xml:80中将com.alibaba:fastjson组件升级为1.2.83版本"
                    }
                ]
            }
        ]
    }
}


3.63项目--获取项目成员列表
描述：根据项目唯一标识获取项目成员列表
GET  /sca/v1/project/shareduser/list?projectUuid=&pageIndex=xx&pageSize=yy

请求参数
参数	类型	长度/大小	是否必填	描述
projectUuid	String	String	是	项目唯一标识
pageIndex	Number	大于0	是	当前页面下标
pageSize	Number	1 ~ 500	是	每页展示行数

响应参数
名称	名称1	名称2	类型	业务描述
status			Number	返回状态码
data	total		Number	总行数
	dataList	auth	String	当前用户对组的权限
		authName	String	当前用户对组的权限的名称
		userUuid	String	用户唯一标识
		userName	String	用户名称
		isOrg	Number	是否超管 0-否 1-是
		email	String	用户邮箱
	pageIndex		Number	当前页面下标
	pageSize		Number	每页展示行数
msg			String	返回信息

返回示例：
{
"status": 1,
"data": {
"total ":1,
	"dataList ": [{
       "auth": "1",
       "authName": "owner1",
       "userUuid": "15a17679c34f477fa451665ebae512e3",
       "userName": "AdminOrg",
       "isOrg": 1,
       "email": "<EMAIL>"
}],
"pageIndex ":1,
"pageSize ":10
},
"msg": "OK"
}


3.64私服--获取私服列表
描述：获取当前登陆人所属机构的私服列表
GET  /sca/v1/nexus/list?pageIndex=xx&pageSize=yy&filter=

请求参数
参数	类型	长度/大小	是否必填	描述
pageIndex	Number	大于0	是	当前页面下标
pageSize	Number	1 ~ 500	是	每页展示行数
filter	String		否	私服地址

响应参数
名称	名称1	名称2	名称3	类型	业务描述
status				Number	返回状态码
data	pageIndex			Number	当前页面下标
	pageSize			Number	每页展示行数
	total			Number	总行数
	data	id		Number	私服主键id
		version		Number	私服类型  
字典值 字典编码  DATABASE_DETECT_TYPE(详情见接口3.54)
		grade		String	项目风险等级
字典值 字典编码PROJECTRISK(详情见接口3.54)
		gradeDesc		String	不同风险组件的数量介绍
		gradeName		String	风险等级
		children	classify	Number	漏洞类型标识
			count	Number	漏洞数量
			cveGrade	String	漏洞风险类型
字典值 字典编码CVERISK (详情见接口3.54)
			cveGradeName	String	漏洞风险类型中文名
			countDesc	String	漏洞类型标识
		address		String	私服地址
		desc		String	私服描述
		number		Number	组件数量
		auth		Number	是否有简单授权的base64
1-有，2-无
		status		Number	检测状态
字典值 字典编码NEXUS_CHECK_STATUS (详情见接口3.54)
		statusName		String	检测状态
		createTime		Number	创建时间
		updateTime		Number	修改时间
		versionName		String	私服类型
		timingFlag		boolean	是否设置有定时任务
true-有，false-无

返回示例：
{
    "status": 1,
"data": {
		"pageIndex": 1,
        "pageSize": 10,
        "total": 23,
        "data": [
            {
                "id": 83,
                "version": 2,
                "grade": "HIGH",
                "gradeDesc": "存在高危组件253个;存在中危组件149个;存在低危组件45",
                "gradeName": "高危--超超超",
                "children": [
                    {
                        "classify": 2,
                        "count": 2,
                        "cveGrade": "HIGH",
                        "cveGradeName": "高危--超超超",
                        "countDesc": "高危--超超超漏洞2条"
                    }
                ],
                "address": "https://maven.java.net/content/repositories/staging/",
                "desc": null,
                "number": 46742,
                "progress": null,
                "auth": 0,
                "status": 3,
                "statusName": "检测完成",
                "createTime": 1653300467000,
                "updateTime": 1653355669000,
                "versionName": "Nexus2",
                "timingFlag": false
            }
        ]
    }
}


3.65私服--获取私服组件列表
描述：根据私服主键id获取对应的组件列表
GET  /sca/v1/nexus/component/list?pageIndex=xx&pageSize=yy&nexusId=

请求参数
参数	类型	长度/大小	是否必填	描述
pageIndex	Number	大于0	是	当前页面下标
pageSize	Number	1 ~ 500	是	每页展示行数
nexusId	Number		是	私服主键id
computerLanguage	String		否	所属语言
字典值 字典编码COMPUTERLANGUAGE (详情见接口3.54)
license	String		否	许可ID 即许可简称
severity	String		否	风险等级
字典值 字典编码JARRISK (详情见接口3.54)

响应参数
名称	名称1	名称2	名称3	类型	业务描述
status				Number	返回状态码
data	pageIndex			Number	当前页面下标
	pageSize			Number	每页展示行数
	total			Number	总行数
	data	id		Number	私服对应的组件的主键id
		nexusId		Number	私服主键id
		format		String	依赖包类型
		name		String	组件名称
		componentName		String	关联组件名称
		componentRouter		String	关联组件路由
		nexusAddress		String	仓库路径
		groupName		String	g  组件组织
		artifactName		String	a  组件名称
		version		String	v  组件版本
		language		String	l  所属语言
		extension		String	文件扩展名
		createTime		Number	创建时间
		updateTime		Number	修改时间
		grade		String	项目风险等级
字典值 字典编码PROJECTRISK(详情见接口3.54)
		gradeName		String	风险等级
		gradeDesc		String	风险漏洞的简述
		licenseId		String	许可简称 许可ID
		children	classify	Number	漏洞类型标识
			count	Number	漏洞数量
			cveGrade	String	漏洞风险类型
字典值 字典编码CVERISK (详情见接口3.54)
			cveGradeName	String	漏洞风险类型中文名
			countDesc	String	漏洞类型标识
		componentId		String	组件唯一标识   gavl的组合
		controlStatus		String	黑白名单字典值
字典编码CONTROLSTATUS(详情见接口3.54)
		controlStatusName		String	黑白名单

返回示例：
{
    "status": 1,
"data": {
		"pageIndex": 1,
        "pageSize": 10,
        "total": 16,
        "data": [
            {
                "id": 503002,
                "nexusId": 96,
                "format": "MAVEN",
                "name": "jackson-databind-2.9.8.jar",
                "componentName": "com.fasterxml.jackson.core:jackson-databind@2.9.8",
                "componentRouter": "com/fasterxml/jackson/core/jackson-databind/2.9.8/jackson-databind-2.9.8.jar",
                "nexusAddress": "http://**********:8081/repository/maven-releases/com/fasterxml/jackson/core/jackson-databind/2.9.8/jackson-databind-2.9.8.jar",
                "groupName": "com.fasterxml.jackson.core",
                "artifactName": "jackson-databind",
                "version": "2.9.8",
                "language": "MAVEN",
                "extension": "jar",
                "createTime": 1672304407000,
                "updateTime": 1672304407000,
                "grade": "CRITICAL",
                "gradeName": "超危",
                "gradeDesc": "存在超危风险的漏洞:CVE-2020-9548,CNNVD-202003-040,SZ-2020-29985,CVE-2019-14893,CNNVD-202001-906,SZ-2019-06685,CVE-2019-16943,CNNVD-201910-022,SZ-2019-08940,CVE-2019-17531,CNNVD-201910-774,SZ-2019-09587,CVE-2019-17267,CNNVD-201910-227,SZ-2019-09296,CVE-2019-16942,CNNVD-201910-023,SZ-2019-08939,CVE-2019-20330,CNNVD-202001-054,SZ-2019-12666,CVE-2020-8840,CNNVD-202002-354,SZ-2020-29277,CVE-2019-16335,CNNVD-201909-715,SZ-2019-08271,CVE-2019-14540,CNNVD-201909-716,SZ-2019-06297;存在高危风险的漏洞:SZ-2022-19936,SZ-2022-20293,CVE-2020-36518,CNNVD-202203-1165,SZ-2020-70884,CVE-2020-36184,CNNVD-202101-344,SZ-2020-23900,CVE-2020-36189,CNNVD-202101-329,SZ-2020-23905,CVE-2020-36188,CNNVD-202101-355,SZ-2020-23904,CVE-2020-24616,CNNVD-202008-1195,SZ-2020-17078,CVE-2020-14060,CNNVD-202006-997,SZ-2020-05467,CVE-2020-25649,CNNVD-202010-622,SZ-2020-18214,CVE-2020-36185,CNNVD-202101-337,SZ-2020-23901,CVE-2020-11113,CNNVD-202003-1735,SZ-2020-02225,CVE-2020-14061,CNNVD-202006-995,SZ-2020-05468,CVE-2020-11111,CNNVD-202003-1737,SZ-2020-02223,CVE-2020-24750,CNNVD-202009-1066,SZ-2020-17226,CVE-2021-20190,CNNVD-202101-1474,SZ-2021-02083,CVE-2020-35490,CNNVD-202012-1285,SZ-2020-23389,CVE-2020-10673,CNNVD-202003-1151,SZ-2020-01741,CVE-2020-35728,CNNVD-202012-1602,SZ-2020-23565,CVE-2020-11112,CNNVD-202003-1736,SZ-2020-02224,CVE-2020-11620,CNNVD-202004-385,SZ-2020-02783,CVE-2020-36187,CNNVD-202101-331,SZ-2020-23903,CVE-2020-10672,CNNVD-202003-1150,SZ-2020-01740,CVE-2020-35491,CNNVD-202012-1270,SZ-2020-23390,CVE-2020-14062,CNNVD-202006-996,SZ-2020-05469,CVE-2020-36186,CNNVD-202101-333,SZ-2020-23902,CVE-2020-11619,CNNVD-202004-387,SZ-2020-02781,CVE-2020-14195,CNNVD-202006-1070,SZ-2020-05615,CVE-2020-36180,CNNVD-202101-326,SZ-2020-23896,CVE-2020-36181,CNNVD-202101-330,SZ-2020-23897,CVE-2020-36183,CNNVD-202101-371,SZ-2020-23899,CVE-2020-36179,CNNVD-202101-327,SZ-2020-23894,CVE-2020-36182,CNNVD-202101-325,SZ-2020-23898,CVE-2020-10969,CNNVD-202003-1627,SZ-2020-02066,CVE-2020-10968,CNNVD-202003-1625,SZ-2020-02065,CVE-2019-12086,CNNVD-201905-776,SZ-2019-03597,SZ-2021-29893,CVE-2019-14439,CNNVD-201907-1500,SZ-2019-06185,CVE-2022-42004,CNNVD-202210-006,SZ-2022-17752,CVE-2022-42003,CNNVD-202210-007,SZ-2022-14349;存在中危风险的漏洞:CVE-2020-10650,CNNVD-202007-1028,SZ-2020-01716,CVE-2019-12384,CNNVD-201906-867,SZ-2019-03925,CVE-2019-12814,CNNVD-201906-751,SZ-2019-04398;",
                "licenseId": "Apache-2.0",
                "children": [
                    {
                        "classify": 1,
                        "count": 10,
                        "cveGrade": "CRITICAL",
                        "cveGradeName": "超危--超超超",
                        "countDesc": "超危--超超超漏洞10条"
                    }
                ],
                "componentId": "com.fasterxml.jackson.core:jackson-databind:2.9.8:MAVEN"
                "controlStatus": 0,
        "controlStatusName": "未配置"

}
        ]
    }
}


3.66私服--获取私服漏洞列表
描述：根据私服主键id获取对应的漏洞列表
GET  /sca/v1/nexus/vulList?pageIndex=xx&pageSize=yy&nexusId=

请求参数
参数	类型	长度/大小	是否必填	描述
pageIndex	Number	大于0	是	当前页面下标
pageSize	Number	1 ~ 500	是	每页展示行数
nexusId	Number		是	私服主键id
severity	String		否	风险等级
字典值 字典编码JARRISK (详情见接口3.54)

响应参数
名称	名称1	名称2	类型	业务描述
status			Number	返回状态码
data	pageIndex		Number	当前页面下标
	pageSize		Number	每页展示行数
	total		Number	总行数
	data	vulName	String	漏洞名称
		severity	String	风险等级
字典值 字典编码JARRISK (详情见接口3.54)
		severityName	String	风险等级
		published	String	发表时间
		description	String	漏洞介绍
		customCveNo	String	cve编号
		customCnnvdNo	String	cnnvd编号
		customCnvdNo	String	cnvd编号
		customSzNo	String	sz编号
		cwe	String	弱点类型
		vulUseDifficulty	String	漏洞利用难度

返回示例：
{
    "status": 1,
"data": {
		"pageIndex": 1,
        "pageSize": 10,
        "total": 66,
        "data": [
            {
                "vulName": "MAVEN",
                "severity": "CRITICAL",
                "severityName": "超危--超超超",
                "published": "2019-09-15",
                "description": "FasterXML jackson-databind是一个基于JAVA可以将XML和JSON等数据格式与JAVA对象进行转换的库。Jackson可以轻松的将Java对象转换成json对象和xml文档，同样也可以将json、xml转换成Java对象。 \nFasterXML jackson-databind 2.9.10之前版本中存在代码问题漏洞。攻击者可利用该漏洞执行代码。",
                "customCveNo": "CVE-2019-14540",
                "customCnnvdNo": "CNNVD-201909-716",
                "customCnvdNo": "CNVD-2019-37174",
                "customSzNo": "SZ-2019-06297",
                "cwe": "CWE-502",
                "vulUseDifficulty": "中"
            }
        ]
    }
}


3.67私服--获取私服许可列表
描述：根据私服主键id获取对应的漏洞列表
GET  /sca/v1/nexus/licenseList?pageIndex=xx&pageSize=yy&nexusId=

请求参数
参数	类型	长度/大小	是否必填	描述
pageIndex	Number	大于0	是	当前页面下标
pageSize	Number	1 ~ 500	是	每页展示行数
nexusId	Number		是	私服主键id
severity	String		否	风险等级
字典值 字典编码JARRISK (详情见接口3.54)
controlStatus	String		否	黑白名单
字典值  字典编码:LICENSECONTROLSTATUS(详情见接口3.54)

响应参数
名称	名称1	名称2	名称3	类型	业务描述
status				Number	返回状态码
data	pageIndex			Number	当前页面下标
	pageSize			Number	每页展示行数
	total			Number	总行数
	data	name		String	许可全称
		licenseStr		String	许可ID
		controlStatus		Number	黑白名单
字典值  字典编码:LICENSECONTROLSTATUS(详情见接口3.54)
		controlStatusName		String	黑白名单
		licenseId		String	许可主键id
		severity		String	风险等级
字典值 字典编码JARRISK (详情见接口3.54)
		severityName		String	风险等级
		parentId		String	父级许可ID
		childens	name	String	子许可 - 许可全称
			licenseStr	String	子许可 - 许可ID
		compatibleLicense		String	兼容GPL
		parent		boolean	是否有子许可

返回示例：
{
    "status": 1,
"data": {
		"pageIndex": 1,
        "pageSize": 10,
        "total": 66,
        "data": [
            {
                "name": "BSD License",
                "licenseStr": "BSD",
                "controlStatus": 0,
                "controlStatusName": "未配置",
                "licenseId": 50902,
                "severity": "NONE",
                "severityName": "无风险",
                "parentId": "",
                "childens": [
                    {
                        "name": "BSD 3-Clause No Nuclear License",
                        "licenseStr": "BSD-3-Clause-No-Nuclear-License"
                    }
                ],
                "compatibleLicense": "未知",
                "parent": true
			}
        ]
    }
}





3.68Ca许可--获取机器码
描述：获取机器码
GET  /sca/v1/permission/machine/{seriesNum}

请求参数
参数	类型	长度/大小	是否必填	描述
seriesNum	String r	大于0	是	渠道号，默认传sca

响应参数
名称	名称1	类型	业务描述
status		Number	返回状态码
data	machineNumber	String	机器码
	snNumber	String	序列号

返回示例：
{
    "status": 1,
    "data": {
        "machineNumber": "C7pzY1XZq9vUIHXKag2bM8pyUD94PULpoPmG/qOJv+TOBYesiw+/tlohS+OJ9smkwFifi9O4KbmX+zw5e2oNLIlMN7FL6ugTDItBIYkkkqpV2tH+wIWMVap2vJtdwvqjU/8f10QbWfyFmLN+K6TPXAtym+AX2hGeIxL5dvfUs9pH7wDlaVw72YF1Ap2ZB0KSjrDg8hq0H75Mzkw==",
        "snNumber": "XXXXF7509-XXXXF-4C65-9AED-92E43XXX"
    }
}

3.69Ca许可--Ca文件上传
描述：Ca文件上传
POST  /sca/v1/permission/file/upload
请求参数
参数	类型	长度/大小	是否必填	描述
licenseFile	MultipartFile	大于0	是	Ca文件

响应参数
名称	类型	业务描述
status	Number	返回状态码
msg	String	返回信息

代码示例
返回示例：
{
"status": 1,
"msg": "OK"
}

3.70Ca许可--查询Ca许可状态
描述：查询Ca许可状态
GET  /sca/v1/permission/vertification

请求参数
参数	类型	长度/大小	是否必填	描述
				

响应参数
名称	类型	业务描述
status	Number	返回状态码
data	Number	Ca许可状态,
0：未激活
1：正常
2：无效的应用
3：无效的机器码
4：许可到期
5：许可不存在
6：应用错误
7：用户数超限
8：应用数超限
9：检测成功数超限

代码示例
返回示例：
{
"status": 1,
"data": 1
}

3.71应用--组件使用建议
描述：根据组件uuid获取使用建议
GET /sca/v1/component/advise

请求参数
参数	类型	长度/大小	是否必填	描述
uuid	String	36	是	组件uuid

响应参数
名称	名称1	名称2	类型	业务描述
status			Number	返回状态码
data	recommendComponent	uuid	String	推荐组件uuid
		version	String	推荐组件版本
		releaseTime	String	推荐组件发布时间
		grade	Number	推荐组件风险等级
		criticalNum	Number	推荐组件超危漏洞数目
		highNum	Number	推荐组件高危漏洞数目
		mediumNum	Number	推荐组件中危漏洞数目
		lowNum	Number	推荐组件低危漏洞数目
	latestComponent	uuid	String	最新组件uuid
		version	String	最新组件版本
		releaseTime	String	最新组件发布时间
		grade	Number	最新组件风险等级
		criticalNum	Number	最新组件超危漏洞数目
		highNum	Number	最新组件高危漏洞数目
		mediumNum	Number	最新组件中危漏洞数目
		lowNum	Number	最新组件低危漏洞数目

返回示例：
{
    "status": 1,
    "data": {
        "recommendComponent": {
            " uuid ": "26a86acf418cae2d5b9349b48995876f921816a667a722ef2ce160e23854ddfc",
            "version": "4.1.68.Final",
            " releaseTime ": "2021-09-09 21:46:36",
            "grade": "NONE",
            "criticalNum": 0,
            "highNum": 0,
            "mediumNum": null,
            "lowNum": 0       
 },
        "latestComponent": {
            "uuid": "afb91fc2c948f5becaed2c14c6e755ea82c2813b53cd58b86f39039ac1619685",
            "version": "4.1.87.Final",
            "releaseTime": "2023-01-13 00:12:20",
            "grade": "NONE",
            "criticalNum": 0,
            "highNum": 0,
            "mediumNum": 0,
            "lowNum": 0
                }
    }
}
   



3.72应用--组件依赖关系列表查询
描述：查询应用下所有的组件的依赖关系或单独某一组件的依赖关系
GET /sca/v1/applications/componentDependency/{appUuid}?componentUuid=
请求参数: 
参数	类型	长度/大小	是否必填	描述
appUuid	String	80	是	应用uuid
componentUuid	String	80	否	组件uuid，
当这个有值的时候，表示获取这个组件的依赖关系
 
 
响应参数： 
名称	类型	业务描述
componentUuid	string	组件uuid
componentTitle	string	组件描述
grade	string	风险等级
gradeName	string	风险等级的中文说明
licenseId	array	许可简称
children	array	依赖的子组件
代码示例：
响应：
{
	"status": 1,
    "data": [
        {
            "componentUuid": "f4210e0f398d134f4c09d808e92c44a9cc72ee9ff2d12e67718ba0e993dee8dc",
            "componentTitle": "org.apache.httpcomponents:httpclient@4.5.12",
            "grade": "MEDIUM",
            "gradeName": "中危",
            "licenseId": "Apache-2.0",
            "children": [
                {
                    "componentUuid": "993c9714aaa902ff93b9450453b6cde7a2337fd63710ee067de6b7c2ed76d5a9",
                    "componentTitle": "org.apache.httpcomponents:httpcore@4.4.13",
                    "grade": "NONE",
                    "gradeName": "无风险",
                    "licenseId": "Apache-2.0",
                    "children": []
                },
                {
                    "componentUuid": "bbb5518562d22a483ea3ae2c52117dbf3d4342516de9bab91fd7793cae15e7aa",
                    "componentTitle": "commons-logging:commons-logging@1.2",
                    "grade": "NONE",
                    "gradeName": "无风险",
                    "licenseId": "Apache-2.0",
                    "children": []
                },
                {
                    "componentUuid": "e6ee414ec46e9f89c8dd38bfb639fc9c19e57cb209277df7be69d45ff6da959d",
                    "componentTitle": "commons-codec:commons-codec@1.11",
                    "grade": "NONE",
                    "gradeName": "无风险",
                    "licenseId": "Apache-2.0",
                    "children": []
                }
            ]
        }
    ]
}



3.73报告--报告生成状态查询
描述：报告生成状态查询
POST  /sca/v1/reports/status

请求参数
参数	类型	长度/大小	是否必填	描述
reportUuid	编号	64	是	报告唯一标识

响应参数
名称	名称1	类型	业务描述
status		Number	返回状态码
data	reportStatus	String	 报告生成状态 字典编码:REPORTSTATUS(详情见接口3.54)

代码示例
请求正文：
{
    "reportUuid":" OB3r/zv1uVf7D6WhYiUSig=="
}

返回示例：
{
    "status": 1,
    "data": {
        "reportStatus": 1
    }
}


3.74应用--生成应用-应用安全报告PDF格式 
描述：配置高级设置以生成PDF格式的应用安全报告。
POST  /sca/v1/reports/pdf/application

请求参数
参数	类型	长度/大小	是否必填	描述
dependRank	String[]		否，但templateId为空时必传	依赖过滤筛选
字典值：直接依赖、间接依赖
introductionType	String[]		否，但templateId为空时必传	引入过滤
字典值 字典编码INTRODUCTIONTYPE  (详情见接口3.54) ，如果templateId有值则不需要传
scanHierarchy	String[]		否，但templateId为空时必传	层级过滤
字典值 字典编码SCANTHEHIERARCHY (详情见接口3.54) ，如果templateId有值则不需要传
depScopeList	String[]		否，但templateId为空时必传	作用域 字典编码SCOPE (详情见接口3.54) ，如果templateId有值则不需要传
severity	String[]		否，但templateId为空时必传	组件风险等级筛选字典编码JARRISK (详情见接口3.54) ，如果templateId有值则不需要传
appUuid	String		否，但checkNo为空时必传	产品ID
templateId	Number		否	报告配置模板id（详情见接口3.87）
checkNo	String		否	检测编号，如果有checkNo入参，则按照checkNo查询应用版本

响应参数
名称	类型	业务描述
status	Number	返回状态码
data	String	报告唯一标识，下载用
msg	String	返回信息

代码示例
请求正文：
{
	"templateId": 19,
	"checkNo": null,
"dependRank": ["直接依赖,间接依赖"],// 全选传 -1
	"introductionType": ["-1"],// 全选传 -1
	"scanHierarchy": ["-1"],// 全选传 -1
	"depScopeList": ["-1"],// 全选传 -1
	"severity": ["-1"],// 全选传 -1
    "appUuid": "62a50e8546454161b772aa5277d80e34" 
}

返回示例：
{
    "status": 1,
    "data": "Kktp+6fZkLGl0k/8ehF4OQ==",
"msg": "OK"
}

3.75应用--生成应用-应用安全报告DOC格式 
描述：配置高级设置以生成DOC格式的应用安全报告。
POST  /sca/v1/reports/doc/application

请求参数
参数	类型	长度/大小	是否必填	描述
dependRank	String[]		否，但templateId为空时必传	依赖过滤筛选
字典值：直接依赖、间接依赖
introductionType	String[]		否，但templateId为空时必传	引入过滤
字典值 字典编码INTRODUCTIONTYPE  (详情见接口3.54) ，如果templateId有值则不需要传
scanHierarchy	String[]		否，但templateId为空时必传	层级过滤
字典值 字典编码SCANTHEHIERARCHY (详情见接口3.54) ，如果templateId有值则不需要传
depScopeList	String[]		否，但templateId为空时必传	作用域 字典编码SCOPE (详情见接口3.54) ，如果templateId有值则不需要传
severity	String[]		否，但templateId为空时必传	组件风险等级筛选字典编码JARRISK (详情见接口3.54) ，如果templateId有值则不需要传
appUuid	String		否，但checkNo为空时必传	产品ID
templateId	Number		否	报告配置模板id（详情见接口3.87）
checkNo	String		否	检测编号,如果有checkNo入参，则按照checkNo查询应用版本

响应参数
名称	类型	业务描述
status	Number	返回状态码
data	String	报告唯一标识，下载用
msg	String	返回信息

代码示例
请求正文：
{
	"templateId": null,
	"checkNo": null,
"dependRank": ["-1"],// 全选传 -1
	"introductionType": ["-1"],// 全选传 -1
	"scanHierarchy": ["-1"],// 全选传 -1
	"depScopeList": ["-1"],// 全选传 -1
	"severity": ["-1"],// 全选传 -1
    "appUuid": "62a50e8546454161b772aa5277d80e34" 
}

返回示例：
{
    "status": 1,
    "data": "Kktp+6fZkLGl0k/8ehF4OQ==",
"msg": "OK"
}


3.76应用—生成应用-组件清单报告
描述：生成组件清单报告
POST  /sca/v1/reports/excel/component

请求参数
参数	类型	长度/大小	是否必填	描述
componentSelfFileds	String[]		否，但templateId为空时必传	组件自定义字段
字典编码PROAPPCOMSELFFIELD(详情见接口3.54) ，如果templateId有值则不需要传
dependRank	String[]		否，但templateId为空时必传	依赖过滤筛选
字典值：直接依赖、间接依赖，如果templateId有值则不需要传
introductionType	String[]		否，但templateId为空时必传	引入过滤
字典值 字典编码INTRODUCTIONTYPE  (详情见接口3.54) ，如果templateId有值则不需要传
scanHierarchy	String[]		否，但templateId为空时必传	层级过滤
字典值 字典编码SCANTHEHIERARCHY (详情见接口3.54) ，如果templateId有值则不需要传
depScopeList	String[]		否，但templateId为空时必传	作用域 字典编码SCOPE (详情见接口3.54) ，如果templateId有值则不需要传
severity	String[]		否，但templateId为空时必传	组件风险等级筛选字典编码JARRISK (详情见接口3.54) ，如果templateId有值则不需要传
controlStatusFlags	String[]		否，但templateId为空时必传	黑白名单
字典编码CONTROLSTATUS (详情见接口3.54) ，如果templateId有值则不需要传
appUuid	String		否，但checkNo为空时必传	产品ID
templateId	Number		否	报告配置模板id（详情见接口3.87）
checkNo	String		否	检测编号,如果有checkNo入参，则按照checkNo查询应用版本

响应参数
名称	类型	业务描述
status	Number	返回状态码
data	String	报告唯一标识，下载用
msg	String	返回信息

代码示例
请求正文：
{
"templateId": null,
"checkNo": null,
"componentSelfFileds": [
        "4",
        "6",
        "7",
        "10"
    ], // 全选传 -1
    "dependRank": [
        "-1"
    ], // 全选传 -1
    "introductionType": [
        "-1"
    ], // 全选传 -1
    "scanHierarchy": [
        "-1"
    ], // 全选传 -1
    "depScopeList": [
        "-1"
    ], // 全选传 -1
    "severity": [
        "-1"
    ], // 全选传 -1
    "controlStatusFlags": [
        "-1"
    ], // 全选传 -1
    "appUuid": "62a50e8546454161b772aa5277d80e34" 
}

返回示例：
{
    "status": 1,
    "data": "Kktp+6fZkLGl0k/8ehF4OQ==",
"msg": "OK"
}

3.77应用—生成应用-漏洞清单报告
描述：生成漏洞清单报告
POST  /sca/v1/reports/excel/vul

请求参数
参数	类型	长度/大小	是否必填	描述
vulSeverity	String[]		否，但templateId为空时必传	漏洞风险等级筛选字典编码CVERISK (详情见接口3.54) ，如果templateId有值则不需要传
vulUseDifficulty	String[]		否，但templateId为空时必传	漏洞利用难度等级
字典编码VULNERABILITYUSEDIFFICULTYTYPE (详情见接口3.54) ，如果templateId有值则不需要传
appUuid	String		否，但checkNo为空时必传	产品ID
templateId	Number		否	报告配置模板id（详情见接口3.87）
checkNo	String		否	检测编号,如果有checkNo入参，则按照checkNo查询应用版本

响应参数
名称	类型	业务描述
status	Number	返回状态码
data	String	报告唯一标识，下载用
msg	String	返回信息

代码示例
请求正文：
{
"templateId": null,
"checkNo": null,
"vulSeverity": [
        "-1"
    ], // 全选传 -1
    "vulUseDifficulty": [
        "-1"
    ], // 全选传 -1
    "appUuid": "62a50e8546454161b772aa5277d80e34" 
}

返回示例：
{
    "status": 1,
    "data": "Kktp+6fZkLGl0k/8ehF4OQ==",
"msg": "OK"
}

3.78应用—生成应用-许可清单报告 
描述：生成许可清单报告
POST  /sca/v1/reports/excel/license

请求参数
参数	类型	长度/大小	是否必填	描述
lisenceSeverity	String[]		否，但templateId为空时必传	许可风险等级筛选
字典编码LICENSERISK  (详情见接口3.54) ，如果templateId有值则不需要传
controlStatusFlags	String[]		否，但templateId为空时必传	黑白名单
字典编码CONTROLSTATUS (详情见接口3.54) ，如果templateId有值则不需要传
appUuid	String		否，但checkNo为空时必传	产品ID
templateId	Number		否	报告配置模板id（详情见接口3.87）
checkNo	String		否	检测编号,如果有checkNo入参，则按照checkNo查询应用版本

响应参数
名称	类型	业务描述
status	Number	返回状态码
data	String	报告唯一标识，下载用
msg	String	返回信息

代码示例
请求正文：
{
"templateId": null,
"checkNo": null,
"lisenceSeverity": [
        "-1"
    ], // 全选传 -1
    "controlStatusFlags": [
        "-1"
    ], // 全选传 -1
    "appUuid": "62a50e8546454161b772aa5277d80e34" 
}

返回示例：
{
    "status": 1,
    "data": "Kktp+6fZkLGl0k/8ehF4OQ==",
"msg": "OK"
}

3.79应用—生成应用-软件物料清单SBOM报告 
描述：生成软件物料清单报告
POST  /sca/v1/reports/dependencyCheck/sbom

请求参数
参数	类型	长度/大小	是否必填	描述
spdxFileFormat	Sting		是	报告格式 
字典值 1 json  2 xml
sbomExportType	String		是	Sbom规范
字典值 0:spdx  1:cycloneDX 2:SWID
appUuid	String		否，但checkNo为空时必传	产品ID
checkNo	String		否	检测编号,如果有checkNo入参，则按照checkNo查询应用版本
spdxVersion	String		否	SPDX版本
目前仅支持2.2和2.3
注意: sbomExportType的值是0的时候，该字段生效

响应参数
名称	类型	业务描述
status	Number	返回状态码
data	String	报告唯一标识，下载用
msg	String	返回信息

代码示例
请求正文：
{
    "checkNo": null,
"spdxFileFormat": "1", 
    "sbomExportType": "1",
    "appUuid": "62a50e8546454161b772aa5277d80e34" 
}

返回示例：
{
    "status": 1,
    "data": "Kktp+6fZkLGl0k/8ehF4OQ==",
"msg": "OK"
}
3.80应用—源码溯源获取相关统计信息 
GET /sca/v1/source/statistics
请求参数
参数	类型	长度/大小	是否必填	描述
appUuid	Sting		是	应用唯一标识

响应参数
名称	类型	业务描述
status	Number	返回状态码
data.codeLine	Number	代码行数
data.fileSize	String	文件大小
selfDevelop	String	自研率


返回示例：
{
    "status": 1,
  	 "data": {
        "codeLine": 315,
        "fileSize": "4.9KB",
        "selfDevelop": "0.0%"
    }
}

3.81应用—源码溯源文件分布 
GET /sca/v1/source/file/statistics


请求参数
参数	类型	长度/大小	是否必填	描述
appUuid	Sting		是	应用唯一标识

响应参数
名称	类型	业务描述
status	Number	返回状态码
data.lang	String	语言
data.size	Number	文件数目


返回示例：
{
    "status": 1,
  	 "data": [
    		   {
          	  "lang": "java",
           	 "size": 5
       	 }
    ]
}


3.82应用—源码溯源组件文件列表信息
GET /sca/v1/source/components
请求参数
参数	类型	长度/大小	是否必填	描述
appUuid	Sting		是	应用唯一标识
pageIndex	number		是	页数
pageSize	number		是	数量

响应参数
名称	类型	业务描述
status	Number	返回状态码
data.fileName	String	文件名称
data.componentName	String	组件名称
data.version	String	版本
data.proportion	String	匹配度
data.selfStatusName	String	自研率
data.author	String	作者
data.filePath	String	文件路径


返回示例：
{
    "status": 1,
  	 "data": [
       {
                "fileName": "BtClientBuilder.java",
                "componentName": "bt",
                "version": "bt-parent-1.8",
                "proportion": "100%",
                "selfStatus": "0",
                "author": "atomashpolskiy",
                "filePath": "test/BtClientBuilder.java",
                "selfStatusName": "启用"
            }
]}

3.83容器—容器下组件列表
GET /sca/v1/docker/components?pageIndex=1&pageSize=10
请求参数
参数	类型	长度/大小	是否必填	描述
appUuid	Sting	64	是	容器唯一标识
pageIndex	number	大于0	是	页数
pageSize	number	1~500	是	数量
computerLanguage	String	80	否	所属语言
字典值 字典编码COMPUTERLANGUAGE (详情见接口3.54)
license	String	20	否	许可ID
字典值 字典编码licenseAll (详情见接口3.54)
severity	String	80	否	风险等级
字典值 字典编码JARRISK (详情见接口3.54)
controlStatus	number	1	否	黑白名单字典值 字典编码:CONTROLSTATUS(详情见接口3.54)
componentType	number	1	否	组件类型字典值 字典编码:PRIVATEPUBLICSTATUS(详情见接口3.54)

响应参数
名称	名称1	名称3	类型	业务描述
status				
data	data	jarName	String	组件名称
		componentUuid	String	组件唯一标识符
		controlStatus	number	黑白名单状态字典值  字典编码:LICENSECONTROLSTATUS(详情见接口3.54)
		controlStatusName	String	黑白名单字典值中文名
		depRank	String	依赖类型
		grade	String	风险级别字典值 字典编码:JARRISK(详情见接口3.54)
		jarInfoAddFrom	String	语言字典值 字典编码COMPUTERLANGUAGE
		licenseId	String	许可名称
		version	String	版本
		path	String	所属路径
	pageIndex		number	当前页面下标
	pageSize		number	每页展示行数
msg			String	返回信息




返回示例：
{
    "status": 1,
    "data": {
        "pageIndex": 1,
        "pageSize": 1,
        "total": 236,
        "data": [
            {
                "jarName": "ant-launcher",
                			"path": "/xyb/java/sca-sc-engine-2.0.2-116.jar/BOOT-INF/lib/ant-launcher-1.10.6.jar",
                			"grade": "NONE",
                			"licenseId": "Apache-2.0",
                			"gradeName": "无风险",
                			"controlStatus": 0
                			"controlStatusName": "未配置",
                			"jarInfoAddFrom": "maven",
                			"version": "1.10.6",
       			"depRank": "直接依赖" 
            }
        ], 
       	 "pages": 236,
        "size": 1,
        "strOrDate": false,
        "checkSize": null
    }
}

3.84容器—容器下漏洞列表
GET /sca/v1/docker/vulnerabilities?pageIndex=1&pageSize=10
请求参数
参数	类型	长度/大小	是否必填	描述
appUuid	Sting		是	应用唯一标识
pageIndex	number		是	页数
pageSize	number		是	数量
Severity	String		否	风险等级
字典值 字典编码CVERISK (详情见接口3.54)
vulUseDifficulty	String		否	漏洞利用难度
字典值 字典编码VULNERABILITYUSEDIFFICULTYTYPE (详情见接口3.54)

响应参数
名称	名称1	名称2	类型	业务描述
status			Number	返回状态码
data	pageIndex		Number	当前页面下标
	pageSize		Number	每页展示行数
	total		Number	总行数
	data	vulName	String	漏洞名称
		severity	String	风险等级
字典值 字典编码JARRISK (详情见接口3.54)
		severityName	String	风险等级
		publishTime	String	发布时间
		customCveNo	String	cve编号
		customCnnvdNo	String	cnnvd编号
		customCnvdNo	String	cnvd编号
		customSzNo	String	sz编号
		cwe	String	弱点类型
		vulUseDifficulty	String	漏洞利用难度


返回示例：
{
    "status": 1,
    "data": {
        "pageIndex": 1,
        "pageSize": 1,
        "total": 187
        "data": [
            {
                "customCveNo": "CVE-2022-42889",
                "customCnnvdNo": "CNNVD-202210-790",
                "customCnvdNo": "CNVD-2022-73686",
                "customSzNo": "SZ-2022-14393",
                "subordinateComponentNum": 1,
                "subordinateComponentValue": "1115978",
                "published": "2022-10-13",
                "severity": "CRITICAL"
               			 "cwe": "CWE-94",
                "vulName": "Apache Commons Text 代码注入漏洞",
               			 "vulUseDifficulty": "中"
           	 	}
        ]
    }
}

3.85容器—容器下许可列表
GET /sca/v1/docker/licenses?pageIndex=1&pageSize=10
请求参数
参数	类型	长度/大小	是否必填	描述
appUuid	Sting		是	应用唯一标识
pageIndex	number		是	页数
pageSize	number		是	数量
severity	String		否	风险等级
字典值 字典编码LICENSERISK (详情见接口3.54)

响应参数
名称	名称1	名称2	名称3	类型	业务描述
status				Number	返回状态码
data	pageIndex			Number	当前页面下标
	pageSize			Number	每页展示行数
	total			Number	总行数
	data	name		String	许可全称
		licenseStr		String	许可ID
		controlStatus		Number	黑白名单
字典值  字典编码:LICENSECONTROLSTATUS(详情见接口3.54)
		controlStatusName		String	黑白名单
		severity		String	风险等级
字典值 字典编码JARRISK (详情见接口3.54)
		severityName		String	风险等级
		compatibleLicense		String	兼容GPL


返回示例：
{
    "status": 1,
    "data": {
        "pageIndex": 1,
        "pageSize": 1,
        "data": [
            {
 			"licenseStr": "GPL-2.0",
 			"controlStatus": 0,
 			"controlStatusName": "未配置",
 			"severity": "High",
 			"severityName": "高危",
         	        "name": "GNU General Public License v2.0 only",
 			"compatibleLicense": "兼容"
          		  }
        ]
    }
}

3.86应用—批量删除应用
描述：批量删除应用
POST  /sca/v1/applications/batchDelete

请求参数
参数	类型	长度/大小	是否必填	描述
appUuidList	List<String>		是	应用的uuid

响应参数
名称	类型	业务描述
Status	Number	返回状态码
Data	String	报告唯一标识，下载用
Msg	String	出错时返回信息
Code	String	出错时编码

代码示例
请求正文：
{
    "appUuidList" : [
        "408f45a9c781489299cd27496d5f224a","86f4387293104731a89f416d9eca5b8b"
    ]
}

返回示例：
{
    "status": 1,
    "data": "OK"
}

{"msg":"应用不存在或无权限","status":0,"code":"50307"}

3.87报告--获取报告导出配置列表
描述：根据报告模板类型获取报告模板下拉选（包含详情回显）
GET  	/sca/v1/exports/template/select/{exportType}

请求参数
参数	类型	长度/大小	是否必填	描述
exportType	String	64	是	报告模板类型：
projectSecurity-项目安全报告，
projectComponent-项目组件清单，
projectVulnerable-项目漏洞清单，
projectLicense-项目许可清单，
applicationSecurity-应用安全报告，
applicationComponent-应用组件清单，
applicationVulnerable-应用漏洞清单，
applicationLicense-应用许可清单

响应参数：
名称	名称2	类型	业务描述
status		Number	返回状态码
data	Id	Number	报告模板id
	depScopeList	String[]	作用域 字典编码: SCOPE (详情见接口3.54)
	introductionType	String[]	引入类型 字典编码: INTRODUCTIONTYPE (详情见接口3.54)
	scanHierarchy	String[]	层级过滤  字典编码: SCANTHEHIERARCHY (详情见接口3.54)
	severity	String[]	导出组件风险等级筛选  字典编码: JARRISK (详情见接口3.54)
	vulSeverity	String[]	导出漏洞风险等级筛选  字典编码: CVERISK (详情见接口3.54)
	licenseSeverity	String[]	导出漏洞风险等级筛选  字典编码: LICENSERISK (详情见接口3.54)
	dependRank	String[]	依赖过滤  -1代表全部，直接依赖和间接依赖
	componentSelfFileds	String[]	组件自定义字段   字典编码: PROAPPCOMSELFFIELD (详情见接口3.54)
	controlStatusFlags	String[]	黑白名单标识  未配置 0;白名单 1;黑名单 2
	vulUseDifficulty	String[]	漏洞利用难度等级
字典编码VULNERABILITYUSEDIFFICULTYTYPE
	orgId	Number	企业id
	defaultTemplate	Boolean	是否默认模板，0-否，1-是
	name	String	模板名称
	exportType	String	报告模板类型，
projectSecurity-项目安全报告，
projectComponent-项目组件清单，
projectVulnerable-项目漏洞清单，
projectLicense-项目许可清单，
applicationSecurity-应用安全报告，
applicationComponent-应用组件清单，
applicationVulnerable-应用漏洞清单，
applicationLicense-应用许可清单
	exportTypeName	String	报告模板类型名称
msg		String	返回信息

代码示例
返回示例：
{
    "status": 1,
    "data": [
        {
            "id": 21,
            "depScopeList": [
                "provided",
                "runtime",
                "test"
            ],
            "introductionType": [
                "binary",
                "file"
            ],
            "reportType": "componentType",
            "scanHierarchy": [
                "4",
                "3",
                "5",
                "7"
            ],
            "severity": [
                "HIGH",
                "MEDIUM"
            ],
            "vulSeverity": null,
            "licenseSeverity": null,
            "dependRank": [
                "直接依赖"
            ],
            "componentSelfFileds": null,
            "controlStatusFlags": null,
            "vulUseDifficulty": null,
            "defaultTemplate": false,
            "name": "applicationSecurity19",
            "exportType": "applicationSecurity",
            "exportTypeName": "应用安全报告"
        },
        ...
    ]
}


3.88知识库--漏洞详情
描述：获取用户下的漏洞列表
GET  /sca/v1/vulnerability/knowledge/{customNo}

请求参数
参数	类型	长度/大小	是否必填	描述
customNo	String	30	是	漏洞编号


响应参数：
名称	名称1	名称2	类型	业务描述
status			Number	返回状态码
data	customSzNo		String	sz编号
	customCveNo		String	Cve编号
	customCnnvdNo		String	Cnnvd编号
	customCnvdNo		String	Cnvd编号
	Grade		String	风险等级字典  字典编码:CVERISK(详情见接口3.54)
	Cwe		String	Cwe
	description		String	漏洞描述
	vulnerabilityName		String	漏洞名称
	cweName		String	弱点类型名称
	releaseDate		Number	发布时间
	updateDate		Number	更新时间
	announcement		String	漏洞建议
	referenceVoList	source	String	来源
		link	String	链接
	cvss3	baseScore	String	cvss3基础分数
		impact	String	cvss3影响
		developAble	String	cvss3可开发性
		attackWay	String	cvss3攻击途径
		attackComplexity	String	cvss3攻击复杂度
		userInteraction	String	cvss3用户交互
		usabilityImpact	String	cvss3可用性影响
		integrityEffect	String	cvss3完整性影响
		confidentialityImpact	String	cvss3机密性影响
		scope	String	cvss3作用域
		permission	String	cvss3权限要求
	cvss2	baseScore	String	cvss2基础分数
		impact	String	cvss2影响
		developAble	String	cvss2可开发性
		attackWay	String	cvss2攻击途径
		attackComplexity	String	cvss2攻击复杂度
		usabilityImpact	String	cvss2可用性影响
		integrityEffect	String	cvss2完整性影响
		confidentialityImpact	String	cvss2机密性影响
		authentication	String	Cvss2认证
	Cpe		Arrar	cpe
	isRepair		String	是否修复
	utilization		String	EXP成熟度
	cnnvdPatchPo	patchId	String	补丁编号
		title	String	补丁名称
		link	String	补丁链接地址
		publishDate	String	补丁发布时间
		szId	String	该补丁所修复的漏洞编号
		referenceUrl	String	参考网址
	vulUseDifficulty		String	漏洞利用难度
	pocusesTime		String	poc利用时间
msg			String	返回信息

代码示例
返回示例：
{
    "status": 1,
    "data": {
        "customSzNo": "SZ-2022-18767",
        "customCveNo": "CVE-2022-42252",
        "customCnnvdNo": "CNNVD-202210-2602",
        "customCnvdNo": "CNVD-2022-74082",
        "grade": "HIGH",
        "cwe": "CWE-20",
        "description": "If Apache Tomcat 8.5.0 to 8.5.82, 9.0.0-M1 to 9.0.67, 10.0.0-M1 to 10.0.26 or 10.1.0-M1 to 10.1.0 was configured to ignore invalid HTTP headers via setting rejectIllegalHeader to false (the default for 8.5.x only), Tomcat did not reject a request containing an invalid Content-Length header making a request smuggling attack possible if Tomcat was located behind a reverse proxy that also failed to reject the request with the invalid header.",
        "vulnerabilityName": "Apache Tomcat 环境问题漏洞",
        "cweName": "Improper Input Validation",
        "releaseDate": 1667265300000,
        "updateDate": 1668744900000,
        "announcement": "目前厂商已发布升级补丁以修复漏洞，详情请关注厂商主页： \nhttps://tomcat.apache.org/security-8.html",
        "referenceVoList": [
            {
                "source": "https://lists.apache.org/thread/zzcxzvqfdqn515zfs3dxb7n8gty589sq",
                "link": "https://lists.apache.org/thread/zzcxzvqfdqn515zfs3dxb7n8gty589sq"
            }
        ],
        "cvss3": {
            "baseScore": "7.5",
            "impact": "3.6",
            "developAble": "3.9",
            "attackWay": "NETWORK",
            "attackComplexity": "LOW",
            "userInteraction": null,
            "usabilityImpact": null,
            "integrityEffect": "HIGH",
            "confidentialityImpact": null,
            "scope": "UNCHANGED",
            "permission": null
        },
        "cvss2": {
            "baseScore": null,
            "impact": null,
            "developAble": null,
            "attackWay": null,
            "attackComplexity": null,
            "usabilityImpact": null,
            "integrityEffect": null,
            "confidentialityImpact": null,
            "authentication": null
        },
        "cpe": [
            "cpe:2.3:a:apache:tomcat:*:*:*:*:*:*:*:*"
        ],
        "isRepair": "否",
        "utilization": null,
        "cnnvdPatchPo": [],
        "vulUseDifficulty": "中",
        "pocusesTime": ""
    },
    "msg": "OK"
} 



3.89知识库--组件模糊查询
描述：组件模糊查询
GET  /sca/v1/component/knowledge/list
请求参数
参数	类型	是否必填	描述
groupId	String	否	组织 (java 和 clojure语言  groupId必填)
artifactId	String	是	组件名称
componentVersion	String	否	版本
language	String	是	语言
pageIndex	number	是	页数
pageSize	number	是	数量

响应参数：
响应参数
名称	名称1	名称3	类型	业务描述
status				
data	data	language	String	语言
		ecosystem	String	生态
		groupName	number	组织
		artifactName	String	组件名称
		version	String	版本
		purl	String	purl
		releaseDate	String	发布时间
		licenseId	Array	许可
		recommendVersion	String	推荐版本
		latestVersion	String	最新版本
		grade	String	风险等级
		uuid	String	组件uuid
	pageIndex		number	当前页面下标
	pageSize		number	每页展示行数
msg			String	返回信息


{
    "data": {
        "strOrDate": false,
        "data": [
            {
                "groupName": "org.codehaus.modello",
                "ecosystem": "MAVEN",
                "releaseDate": "2008-08-07",
                "latestVersion": "1.0-alpha-21",
                "artifactName": "modello-db-keywords",
                "grade": "NONE",
                "language": "java",
                "purl": "pkg:maven\/org.codehaus.modello\/modello-db-keywords@1.0-alpha-21",
                "licenseId": [
                    "MIT"
                ],
                "version": "1.0-alpha-21",
                "recommendVersion": "1.0-ALPHA-21"
            }
}



3.90知识库--组件详情查询
描述：组件模糊查询
GET  /sca/v1/component/knowledge/detail
请求参数
参数	类型	是否必填	描述
groupId	String	否	组织 (java 和 clojure语言  groupId必填)
artifactId	String	是	组件名称
componentVersion	String	是	版本
language	String	是	语言

响应参数：
名称	名称1	名称2	类型	业务描述
status			Number	返回状态码
data	groupId		String	groupId
	artifactId		String	组件名称
	version		String	版本
	hash		String	hash
	recommendVersion		String	推荐版本
	latestVersion		String	最新版本
	grade		String	风险级别字典值 字典编码:JARRISK(详情见接口3.54)
	language		String	语言
	desc		String	组件描述
	uuid		String	组件uuid
	vulNum		Number	漏洞数
	licenseIdNum		Number	许可数目
	licenseId		array	许可名称列表
	releaseDate		Number	发布时间
	homePage		String	主页
	componentVulKnowledgeDetailVos	szNo	String	Sz编号
		severity	String	漏洞风险等级
		cnnvdList	array	Cnnvd编号
		cnvdList	array	Cnvd编号
		cveNo	String	漏洞风险等级
	organization		String	所属组织
	ownSoftware		String	所属软件
	author		String	作者
	scmConnection		String	源码链接
	country		String	所属国家
	countryChineseName		String	所属国家的中文名称
msg			String	返回信息

    


{
    "status": 1,
    "data": {
        "groupId": "com.fasterxml.jackson.core",
        "artifactId": "jackson-databind",
        "version": "2.13.4",
        "hash": "98b0edfa8e4084078f10b7b356c300ded4a71491",
        "recommendVersion": "2.13.4.2",
        "latestVersion": "2.15.2",
        "grade": "HIGH",
        "gradeName": "高危",
        "gradeDesc": null,
        "language": "Java",
        "desc": "General data-binding functionality for Jackson: works on core streaming API",
        "releaseDate": 1662220800000,
        "jarInfoAddFrom": "maven",
        "uuid": "8fca902e3ea5d0f61ad8542e3244dab02632d9779807bb9b4cc1aa3b06e7eafc",
        "licenseIdList": [
            "Apache-2.0"
        ],
        "componentVulKnowledgeDetailVos": [
            {
                "szNo": "SZ-2022-14349",
                "cveNo": "CVE-2022-42003",
                "severity": "HIGH",
                "cnnvdList": [
                    "CNNVD-202210-007"
                ],
                "cnvdList": null
            }
        ],
        "vulNum": 1,
        "licenseIdNum": 1,
        "ecosystem": null,
        "country": "America",
        "countryChineseName": "美国",
        "homePage": "https://github.com/FasterXML/jackson",
        "scmConnection": "https://github.com/fasterxml/jackson-databind",
        "organization": "FasterXML",
        "author": "Tatu Saloranta",
        "ownSoftware": "FasterXML/jackson-databind"
    }
}



3.91 应用--组件添加标记
描述：给应用中的组件添加标记
POST  /sca/v1/applications/components/audit/label
Content-Type: application/json

请求参数
参数	类型	是否必填	描述
appUuId	String	是	应用的UUID
componentUuid	String	是	组件UUID
remarkType	Integer	是	标签类型 (1 - 误报 | 2 - 忽略)
remarkDesc	String	否	添加对于标签描述

响应参数
名称	类型	业务描述
status	Number	返回状态码
data	String	报告唯一标识，下载用
msg	String	返回信息

代码示例
请求正文：
{
    "appUuid": "4ecc2858dd404e1bb606cdacc1d0d3c2",
    "componentUuid": "dd5f84beec10d8e9463959387bea2784e6d1b65233c0cc7a9fdf86c69bac15da",
    "remarkType": 1,
    "remarkDec": "123",
    "remark": null
}

返回示例：
{
    "status": 1
}
3.92 应用--组件取消标记
描述：给应用中的组件取消标记
DELETE  /sca/v1/applications/components/audit/label?appUuid={appUuid}&componentUuid={componentUuid}&remark={remark}

参数	类型	是否必填	描述
appUuid	String	是	应用的UUID
componentUuid	String	是	组件UUID
remark	String	是	标签名称，填“误报”或“忽略”

响应参数
名称	类型	业务描述
status	Number	返回状态码
data	String	报告唯一标识，下载用
msg	String	返回信息

返回示例：
{
    "status": 1
}
3.93 应用--沿用上次标签添加适配的检测结果
描述：沿用上次标签添加适配的检测结果
PUT  /sca/v1/applications/label/affect/result
Content-Type: application/json

参数	类型	是否必填	描述
appUuId	String	是	应用的UUID

响应参数
名称	类型	业务描述
status	Number	返回状态码
data	String	报告唯一标识，下载用
msg	String	返回信息

代码示例
请求正文：
{
    "appUuid": "4ecc2858dd404e1bb606cdacc1d0d3c2"
}

返回示例：
{
    "status": 1
}


3.94 应用—检测历史报告导出
描述：根据应用唯一标识异步生成相关的文档，完成后请到相关页面的报告处下载
注意：
- 当请求入参的"CheckNo"等于当前应用最近一次的检测号时，会生成最新检测的PDF报告
- 反之，会生成"CheckNo"对应的某次检测历史的Excel报告
GET  /sca/v1/application/file/export

请求参数
参数	类型	长度/大小	是否必填	描述
appUuid	String	64	是	应用唯一标识
checkNo	String	256	是	检测号

响应参数
名称	类型	业务描述
status	Number	返回状态码
data	String	报告唯一标识，下载用
msg	String	返回信息

返回示例：

{
"status": 1,
"data": "wsYISp+L6hd6y5rwNlOIfQ==",
"msg": "OK"
}


3.95 资产--组件影响的应用
GET /sca/v1/component/influence/app/list?pageIndex=xx&pageSize=xx& componentUuid=xxxx

请求参数
参数	类型	长度/大小	是否必填	描述
componentUuid	Sting	80	是	组件唯一标识
pageIndex	number	大于0	是	页数
pageSize	number	1~500	是	数量


响应参数：
名称	名称1	名称2	类型	业务描述
status			Number	返回状态码
data	total		Number	总数
	data	appName	String	应用名称
		projectName	String	项目名称
		importanceName	String	应用风险等级
		importance	String	应用风险级别字典值 字典编码:APPRISK(详情见接口13.1) 
		appLeader	String	添加人员
		path	String	引入位置
		uploadTime	String	检测时间(yyyy-MM-dd HH:mm:ss)
		critical	Number	超危漏洞数
		high	Number	高危漏洞数
		medium	Number	中危漏洞数
		low	Number	低危漏洞数
		unknown	Number	未知风险漏洞数
	pageIndex		Number	当前页面下标
	pageSize		Number	每页展示行数
msg			String	返回信息


返回示例：
{
    "status": 1,
    "data": {
        "pageIndex": 1,
        "pageSize": 1,
        "total": 3,
        "data": [
            {
                "appName": "w3",
                "path": "\\bt-master\\bt-cli\\pom.xml",
                "projectName": null,
                "appLeader": "AdminOrg",
                "critical": 6,
                "high": 8,
                "medium": 14,
                "low": 4,
				"unknown": 0,
                "importance": "CRITICAL",
                "importanceName": "超危",
                "uploadTime": "2024-07-03 10:04:49"
            }
        ]
      
    }
}



3.96 资产--组件详情导出报告
GET /sca/v1/component/create/report?type=xx&componentUuid=xxxx

请求参数
参数	类型	长度/大小	是否必填	描述
componentUuid	Sting	80	是	组件唯一标识
type	number	1	是	报告类型
1: 组件报告(pdf)
2: 许可列表
3: 漏洞列表
4: 应用列表


响应参数
名称	类型	业务描述
status	Number	返回状态码
data	String	报告唯一标识，下载用
msg	String	返回信息
返回示例：
{
"status": 1,
"data": "wsYISp+L6hd6y5rwNlOIfQ==",
"msg": "OK"
}

3.97 应用--应用下的敏感信息
GET /sca/v1/applications/sensitive/info/list?pageIndex=xx&pageSize=xx&type=xx&appUuid=xx

请求参数
参数	类型	长度/大小	是否必填	描述
pageIndex	number	大于0	是	当前页面下标
pageSize	number	1 ~ 500	是	每页展示行数
appUuid	String	64	是	应用唯一标识
type	String	30	是	敏感类型:
URL:敏感URL
IP_PORT:敏感IP
EMAIL:敏感邮箱
USER:用户名
PASSWORD:密码
ID_NUMBER:身份证号
BANK_CARD:银行卡号


响应参数：
名称	名称1	名称2	类型	业务描述
status			Number	返回状态码
data	total		Number	总数
	data	domainName	String	根域名 (敏感类型是URL或 EMAIL的独有字段)
		url	String	敏感URL(敏感类型是URL的独有字段)
		protocol	String	传输协议(敏感类型是URL的独有字段)
		urlPassword	String	敏感URL密码(敏感类型是URL的独有字段)
		ip	String	敏感IP(敏感类型是IP_PORT的独有字段)
		type	String	类型(敏感类型是IP_PORT的独有字段)
		email	String	敏感邮箱(敏感类型是EMAIL的独有字段)
		userName	String	用户名(敏感类型是USER的独有字段)
		password	String	密码(敏感类型是PASSWORD的独有字段)
		idCard	String	身份证号(敏感类型是ID_NUMBER的独有字段)
		bankCard	String	银行卡号(敏感类型是BANK_CARD的独有字段)
		path	String	检出路径
		source	String	敏感类型:
URL:敏感URL
IP_PORT:敏感IP
EMAIL:敏感邮箱
USER:用户名
PASSWORD:密码
ID_NUMBER:身份证号
BANK_CARD:银行卡号
	pageIndex		Number	当前页面下标
	pageSize		Number	每页展示行数
msg			String	返回信息


返回示例：
{
    "status": 1,
    "data": {    
        "pageIndex": 1,
        "pageSize": 5,
        "total": 104,
        "data": [
            {
               
                "domainName": "jitpack.io",
                "url": "https://jitpack.io/#the8472/mldht",
                "path": "bt-master\\bt-dht\\the8472\\mldht\\README.md",
                "protocol": "https",
                "userName": null,
                "type": null,
                "source": "URL",        
                "urlPassword": null,
                "ip": null,
                "email": null,
                "password": null,
                "idCard": null,
                "bankCard": null
            }
          
        ]
    }
}


Token鉴权方式具体参考SourceCheck_REST API接口文档

1组件--高危组件top10统计数据
描述：获取高危组件top10统计数据
POST 	/sca/v1/statistics/comp/grade/ten
Content-Type: application/json

请求参数
参数	类型	长度/大小	是否必填	描述
projectTagIds	List<String>		否	项目标签（多选）
depSources	List<String>		否	引入类型
startTime	String		否	开始时间
endTime	String		否	结束时间
type	String		否	项目分布、应用分布
projectFlag	String		是	项目关联筛选条件，ALL：所有数据，IN：项目内，OUT：项目外


响应参数：
名称	名称1	名称2	类型	业务描述
status			Number	返回状态码
data	componentName		String	组件名称
	componentId		String	组件ID
	grade		Number	危险等级
	gradeName		String	危险等级描述
	appVersionId		String	应用版本id
	vulNum		Number	漏洞数量
	controlStatus		String	黑白名单状态字典值  字典编码:LICENSECONTROLSTATUS(详情见接口3.54) 
	controlStatusName		String	黑白名单
	createTime		String	创建时间
msg			String	返回信息

代码示例
请求正文：
{
    "projectFlag": "ALL",
    "projectTagIds": [
    ],
    "depSources": [
    ]
}

返回示例：
{
    "status": 1,
    "data": [
        {
            "componentName": "electron-25.4.0",
            "componentId": "1828694567180828675",
            "grade": 5,
            "gradeName": "超危组",
            "appVersionId": null,
            "vulNum": 58,
            "controlStatus": null,
            "controlStatusName": null,
            "createTime": null
        },
        {
            "componentName": "jetty-server-8.2.0.v20160908",
            "componentId": "1815195804529328147",
            "grade": 5,
            "gradeName": "超危组",
            "appVersionId": null,
            "vulNum": 11,
            "controlStatus": null,
            "controlStatusName": null,
            "createTime": null
        },
        {
            "componentName": "postgresql-42.2.5",
            "componentId": "1828369961509191771",
            "grade": 5,
            "gradeName": "超危组",
            "appVersionId": null,
            "vulNum": 8,
            "controlStatus": null,
            "controlStatusName": null,
            "createTime": null
        },
        {
            "componentName": "snakeyaml-1.17",
            "componentId": "1815195804525133835",
            "grade": 5,
            "gradeName": "超危组",
            "appVersionId": null,
            "vulNum": 8,
            "controlStatus": null,
            "controlStatusName": null,
            "createTime": null
        },
        {
            "componentName": "tomcat-coyote-9.0.17",
            "componentId": "1818200092528607253",
            "grade": 5,
            "gradeName": "超危组",
            "appVersionId": null,
            "vulNum": 8,
            "controlStatus": null,
            "controlStatusName": null,
            "createTime": null
        },
        {
            "componentName": "snakeyaml-1.25",
            "componentId": "1826793929769484291",
            "grade": 5,
            "gradeName": "超危组",
            "appVersionId": null,
            "vulNum": 8,
            "controlStatus": null,
            "controlStatusName": null,
            "createTime": null
        },
        {
            "componentName": "snakeyaml-1.30",
            "componentId": "1821097884116844577",
            "grade": 5,
            "gradeName": "超危组",
            "appVersionId": null,
            "vulNum": 7,
            "controlStatus": null,
            "controlStatusName": null,
            "createTime": null
        },
        {
            "componentName": "snakeyaml-1.26",
            "componentId": "1814125055018270757",
            "grade": 5,
            "gradeName": "超危组",
            "appVersionId": null,
            "vulNum": 7,
            "controlStatus": null,
            "controlStatusName": null,
            "createTime": null
        },
        {
            "componentName": "hutool-core-5.4.0",
            "componentId": "1814125055018270776",
            "grade": 5,
            "gradeName": "超危组",
            "appVersionId": null,
            "vulNum": 7,
            "controlStatus": null,
            "controlStatusName": null,
            "createTime": null
        },
        {
            "componentName": "hutool-core-5.1.0",
            "componentId": "1826793929773678721",
            "grade": 5,
            "gradeName": "超危组",
            "appVersionId": null,
            "vulNum": 7,
            "controlStatus": null,
            "controlStatusName": null,
            "createTime": null
        }
    ]
}


2许可--被引用最多的组件top10统计数据
描述：获取被引用最多的组件top10统计数据
POST 	/sca/v1/statistics/comp/use/ten
Content-Type: application/json

请求参数
参数	类型	长度/大小	是否必填	描述
projectTagIds	List<String>		否	项目标签（多选）
depSources	List<String>		否	引入类型
startTime	String		否	开始时间
endTime	String		否	结束时间
type	String		否	项目分布、应用分布
projectFlag	String		是	项目关联筛选条件，ALL：所有数据，IN：项目内，OUT：项目外


响应参数：
名称	名称1	名称2	类型	业务描述
status			Number	返回状态码
data	value		String	组件数量
	name		String	组件名称
	componentId		String	组件ID
msg			String	返回信息

代码示例
请求正文：
{
    "projectFlag": "ALL",
    "projectTagIds": [
    ],
    "depSources": [
    ]
}

返回示例：
{
    "status": 1,
    "data": [
        {
            "value": "36",
            "name": "log4j-core-2.4.1",
            "componentId": "1815195804529328128"
        },
        {
            "value": "36",
            "name": "commons-logging-1.2",
            "componentId": "1813756871530512385"
        },
        {
            "value": "34",
            "name": "hamcrest-core-1.3",
            "componentId": "1813764694293348365"
        },
        {
            "value": "33",
            "name": "javax.inject-1",
            "componentId": "1815195804525133827"
        },
        {
            "value": "33",
            "name": "junit-4.12",
            "componentId": "1813764694293348352"
        },
        {
            "value": "32",
            "name": "jetty-servlet-8.2.0.v20160908",
            "componentId": "1815195804529328135"
        },
        {
            "value": "32",
            "name": "jetty-io-8.2.0.v20160908",
            "componentId": "1815195804529328134"
        },
        {
            "value": "32",
            "name": "libmldht-0.1.1",
            "componentId": "1815195804529328133"
        },
        {
            "value": "32",
            "name": "seamless-util-1.1.1",
            "componentId": "1815195804529328132"
        },
        {
            "value": "32",
            "name": "lanterna-3.0.0-beta3",
            "componentId": "1815195804529328131"
        }
    ]
}


3许可--获取许可统计数据
描述：获取许可统计数据
POST 	/sca/v1/statistics/license/count
Content-Type: application/json

请求参数
参数	类型	长度/大小	是否必填	描述
projectTagIds	List<String>		否	项目标签（多选）
depSources	List<String>		否	引入类型
startTime	String		否	开始时间
endTime	String		否	结束时间
type	String		否	项目分布、应用分布
projectFlag	String		是	项目关联筛选条件，ALL：所有数据，IN：项目内，OUT：项目外


响应参数：
名称	名称1	名称2	类型	业务描述
status			Number	返回状态码
data	label		String	危险描述
	value		Number	危险等级
	number		Number	许可数量
msg			String	返回信息

代码示例
请求正文：
{
    "projectFlag": "ALL",
    "projectTagIds": [
    ],
    "depSources": [
    ]
}

返回示例：
{
    "status": 1,
    "data": [
        {
            "label": "超危",
            "value": 5,
            "number": 1
        },
        {
            "label": "高危",
            "value": 4,
            "number": 2
        },
        {
            "label": "低危",
            "value": 2,
            "number": 5
        },
        {
            "label": "未知",
            "value": 1,
            "number": 4
        },
        {
            "label": "无风险",
            "value": 0,
            "number": 18
        }
    ]
}




