原型交互体验
暂时无法在飞书文档外展示此内容
需求背景
- 广东移动项目，需要整合开源网安CodeSec、SourceCheck、领航镜像安全三个产品，共同构成一个Devops安全左移检测系统
- 需要支持在一个统一面板上实现扫描任务的统一下发，并基于代码仓库关联三个产品（代码扫描、开源软件扫描、镜像扫描）的检测结果，便于扫描结果溯源
版本目标
- 设计一个 portal 系统，整合三个产品，能够实现统一的扫描任务下发
- 基于代码仓库关联三个工具的扫描结果，支持用户在一个整体项目中查看不同工具的扫描结果
- Portal 的用户管理能够连通3个工具，实现统一的账户管理
需求功能描述
代码扫描_接口文档：开源网安代码审核平台接口说明文档_v4.0.docx
开源软件扫描_接口文档：SourceCheck_REST API接口文档_v3.5.2_20240828.docx
系统登录
登录页输入邮箱、密码、验证码登录系统，「忘记密码」hover 展示提示：“如若忘记密码，请联系管理员”。不需要支持双语，默认中文。
[图片]
系统登录成功后，顶部导航栏展示主要功能模块，右上角展示登录用户名名，hover后展示「修改密码」和「退出登录」的操作按钮。
- 点击「退出登录」，注销登录信息，页面跳转到登录页
- 点击「修改密码」，弹出模态窗口，需填写「旧密码」、「新密码」、「确认密码」
修改密码的规则同领航
  - 「旧密码」：与当前用户密码进行校验比对，确保当前密码输入正确
  - 「新密码」：密码8-16位，至少包含字母/数字/特殊符号(~!@$%ヘ&*.) 2 种组合
  - 「确认密码」：校验与「新密码」是否一致
  - 「旧密码」、「新密码」和「确认密码」字段需要按照规则进行校验，可以在点击「提交」后校验
- 如果密码规则校验不通过，「密码」输入框线条标红，填框下下方文案提醒「密码8-16位，至少包含字母/数字/特殊符号(~!@$%^&*.)2种组合」
- 如果确认密码与密码填写框不一致，「确认密码」输入框标红，填框下方文案提醒「两次密码输入不一致」
- 如果旧密码输入错误，提醒文案「旧密码错误，请重新输入」
[图片]
首页
根据角色权限展示代码扫描、开源软件扫描、镜像扫描（CI）的统计图表，体现扫描概况。管理员查看数据
- 代码扫描（缺少现成接口）：代码缺陷数量统计、开发语言缺陷 Top5、超危&高危代码缺陷 Top5
- 开源组件（缺少现成接口）：许可分布、组件被引用次数 Top5、高危组件Top 5
- CI 镜像扫描：镜像概况趋势图（24h、7天、30天）、异常次数 Top5 的镜像，同领航的 CI 扫描统计
[图片]
代码安全&开源软件&镜像安全
在导航栏「代码安全」、「开源软件」、「镜像安全」菜单下，分别 iframe 嵌入对应的三款产品：开源网安codesec、开源网安 sourcecheck、探真领航，嵌入的要求如下：
- 登录统一看板后，嵌入的产品无需二次登录，可直接访问产品页面。Codesec 默认页为“首页”、sourcecheck 默认页“概览”、领航默认页“镜像安全-镜像生命周期-CI”。嵌入后产品需可正常独立使用
- 三个产品去logo，保留统一的导航栏产品名称即可
- 屏蔽登录用户的信息展示和用户管理入口
[图片]
[图片]
[图片]
项目管理
项目列表
- 列表展示项目的基本信息，包括：项目名称、风险级别、仓库地址、仓库类型、代码扫描、开源软件扫描、扫描状态、最近扫描时间，其中：
  - 「风险级别」：高、中、低、未知。三个组件的扫描结果达到高危及以上风险，则项目风险级别为高；扫描结果风险最高等级为中时，项目风险级别为中；扫描结果风险最高等级为低时，项目风险级别为低；未扫描的项目或未扫描到任何结果的项目，风险级别为未知。
  - 「代码扫描」：显示项目代码安全的扫描任务状态，状态值：未扫描、扫描中、扫描完成、扫描失败。
  - 「开源软件扫描」：显示项目开源软件的扫描任务状态，状态值：未扫描、扫描中、扫描完成、扫描失败。
  - 「扫描状态」：进度条分别展示代码扫描、开源软件扫描的任务状态。镜像扫描集成到流水线，构建时自动触发，无需通过页面下发，此处不展示镜像扫描的进度。
    - 鼠标 hover 到进度条前的 icon ，展示对应 icon 的提示文案“代码扫描”或“开源软件扫描”。
    - 进度条的状态有：未扫描、进行中(按百分比)、扫描完成、扫描失败。hover 到进度条上展示对应的状态文案。具体样式以 UI 稿为准
  - 「最近扫描时间」：显示项目最近一次扫描的时间，未扫描显示为空。表头右侧显示「排序」icon，点击可按该字段排序，第一次点击按时间逆序排，第二次点击按时间顺序，第三次点击恢复默认排序
  - 操作列：编辑、删除、发起扫描、导出报告。详见后文说明
  - 排序：默认按创建时间逆序
  - 分页：默认10条/页，采用规范组件
  - 筛选项：
    - 输入框模糊搜索：项目名称、仓库地址
    - 下拉多选搜索：风险级别、仓库类型
- 点击列表任意数据行，可进入项目详情页，见后文说明
[图片]
新增项目
项目列表左上角点击「新增」进入新增项目的页面，表单中填写信息，表单内容如下：
字段名
必填
唯一值
控件类型
取值范围
备注
项目名称
是
是
文本输入框
1~50个字符

仓库类型
是
否
下拉单择框
GitHub
GitLab V3（<9.0 版本）
GitLab V4(>=9.0)
Gitee

仓库地址
是
否
文本输入框

支持 http、https
Token
是
否
文本输入框


分支/标签

否

否
下拉单选
- 输入仓库地址、用户名、密码后，点击下拉拉取分支信息，选择分支或标签
- 不填默认master
- 分支的控件提示语：请选择分支，默认为master。
- 标签的控件提示语：请选择标签
- 控件下拉时根据仓库地址和 Token 拉取分支/标签信息，拉取失败则全局提示：
  - “分支拉取失败，请检查仓库认证信息”
  - “标签拉取失败，请检查仓库认证信息”
备注
否
否
文本输入框
1~100字符

- 必填项未填写时，前端控件标红，并在控件下方提示“xxx（字段名）不能为空”。
- 表单填写完成后点击右下角「新增」完成项目新增，创建同名的「代码安全项目」、「开源软件仓库应用」、三合一项目，自动生成「项目标识符」，完成后全局提示“新增成功”并返回项目列表，新增的项目展示在项目列表的第一条。
- 项目创建成功后，关联对应「代码安全项目」、「开源软件仓库应用」的项目ID，若用户在「代码安全」和「开源软件」的产品页面修改对应的项目信息，不影响三合一项目的关联情况。
[图片]
编辑项目
项目列表操作列点击「编辑」进入编辑页面，表单内容和填写规则同新增。根据项目的「扫描状态」，进入编辑页面前有以下场景：
- 「扫描状态」：代码扫描和开源软件扫描都不处于扫描中，直接进入编辑页
- 「扫描状态」：代码扫描或开源软件扫描处于扫描中，模态弹窗提示“项目扫描尚未完成。现在进入编辑模式将终止当前扫描任务，确定要继续该操作吗？。”确认后先终止进行中的扫描任务，扫描状态的进度条显示为「扫描失败」，再进入编辑模式；取消则关闭模态窗口，停留在列表页。
必填项未填写时，前端控件标红，并在控件下方提示“xxx（字段名）不能为空”。
表单填写完成后点击右下角「保存」完成编辑，全局提示“保存成功”并返回项目列表，项目在列表中的位置不变。
[图片]
[图片]
删除项目
项目列表操作列点击「删除」弹出模态窗口，提示“删除后，数据将不会被恢复，是否确定删除项目xxxxx？”。确认后删除项目，成功后全局提示“删除成功”
若被删的项目存在正在扫描的「代码扫描」、「开源软件扫描」任务，删除前需先终止扫描任务，再进行项目删除。
[图片]
发起扫描
项目列表操作列点击「发起扫描」，弹出模态窗口：
- 窗口中选择「任务类型」：必填，多选，默认都不选，可选范围：代码扫描、开源软件扫描。
- 确认后下发对应的扫描任务。根据项目的「扫描状态」，有以下场景：
  - 「代码扫描」的任务未处于扫描中 ：启动新的代码扫描任务，扫描方案为系统内置的全量策略
  - 「开源软件扫描」的任务未处于扫描中 ：启动新的开源软件扫描任务
  - 「代码扫描」的任务处于扫描中 ：弹窗中「代码扫描」的选项 disable 。终止旧任务，重新启动代码扫描任务，扫描方案为系统内置的全量策略
  - 「开源软件扫描」的任务处于扫描中 ：弹窗中「开源软件扫描」的选项 disable。 终止旧任务，重新启动开源软件扫描任务
[图片]
导出报告
系统右下角固定展示下载浮窗，点击展开导出列表，显示「文件名称」、「类型」、「操作」。交互形式同领航。
- 项目列表操作列点击「导出报告」，在下载浮窗中新增一个下载任务导出最新的扫描结果：
  - 文件名称：项目名称_report_YYYYMMDD-HHMMSS.zip，压缩包中分别包含 CodeScan.xlsx、SourcesoftScan.xlsx、ImageScan.xlsx 三个文件，分别包含代码扫描、开源软件扫描和镜像扫描的结果。报告的数据与界面字段一致即可，CI 的镜像扫描报告需导出镜像列表和相关的漏洞统计，暂不导漏洞详情。
  - 类型：项目报告
  - 操作：就绪前展示为loading，就绪后展示为「下载」按钮。点击下载可导出报告。
- 仅当项目的「代码扫描」、「开源软件扫描」的扫描状态至少存在一个「扫描完成」时，才展示「导出报告」的按钮，否则操作列不展示「导出报告」的按钮。
批量操作
项目列表左上角点击批量操作，交互逻辑参照规范组件。支持多选项目进行「发起扫描」、「删除」
- 「发起扫描」：多选项目后，点击「发起扫描」的批量按钮，模态弹窗中选择「任务类型」，批量下发扫描任务。下发逻辑同单个发起扫描的逻辑
- 「删除」：勾选后模态弹窗提示“删除后，数据将不会被恢复，是否确定删除所选项目？”，确认后删除所选项目。扫描中的项目删除前自动终结扫描任务
- 「导出报告」：
  - 若选中的项目中包含“扫描中”的项目，模态弹窗提示：“扫描中”的项目无法导出报告，是否继续下载其他项目的报告？”确认后将其他项目的报告打包进一个压缩包 report_YYYYMMDD-HHMMSS.zip；取消后不执行下载动作
  - 若选中的项目不包含“扫描中”的状态，直接将所选项目的报告打包进一个压缩包 report_YYYYMMDD-HHMMSS.zip；
[图片]
项目详情
- 项目列表点击任意行项目，可以进入项目详情，项目详情分为「基本信息」、「扫描结果」两个部分，其中扫描结果分别展示 代码扫描、开源软件扫描、镜像扫描的结果。
- 页面标题显示为「项目名称+项目风险级别」，右上角展示操作按钮「发起扫描」、「导出报告」、「编辑」、「删除」。若当前项目中的 代码扫描、开源软件扫描不存在已完成的扫描任务，则不显示「导出报告」的按钮。「发起扫描」和「导出报告」的交互，与项目列表对应的操作一致。
基本信息
展示项目的「仓库地址」、「仓库类型」、「分支/标签」、「项目标识符」、「最近扫描时间」、「更新人」、「最近更新时间」、「项目描述」。其中「最近更新时间」代表最近一次 代码扫描/ 开源软件/ 镜像扫描的时间。
代码扫描
开源网安CodeSec接口
单击选中「代码扫描」标签，展示仓库项目代码扫描的结果，主要包含「扫描信息」、「缺陷列表」。
- 「缺陷列表」：列表展示「缺陷名称」、「严重级别」、「文件名」、「文件行数」、「缺陷跟踪」。
- 「扫描信息」：展示代码扫描任务的「扫描状态」、「错误说明」、「语言」、「文件数」、「可执行代码行数」、「代码注释行数」、「空白行数」。
  - 「扫描状态」：包括未扫描、扫描中、扫描失败、扫描成功。始终显示最近一次扫描获取到的结果，本次扫描未完成则显示上一次扫描结果，没有扫描结果则显示为空。“扫描中”时页面扫描结果显示为空
  - 「错误说明」：扫描失败时展示具体失败原因，其他状态不显示该字段
  - 卡片左上角显示「查看详情」按钮，点击后跳转到「代码安全」内嵌产品对应的项目详情页。
url 示例：http://1.95.60.107:28081/#/project/viewer/static?a_id=4d552373-d00c-401f-825e-d46a8d9c0bca&p_from=p_detail&r_id=dc5545b4-4091-4130-8c9c-d8bedd2a7177&r_type=1&v_c_id=4
- 「漏洞列表」：列表展示「漏洞信息」（漏洞编号）、「严重级别」、「漏洞类型」、「文件」（含文件名和行数）、「漏洞描述」。
  - 点击任意行漏洞，直接跳转到「开源网安-代码安全」产品，此仓库项目该漏洞的详情页
url 示例：http://1.95.60.107:28081/#/project/viewer/static?a_id=4d552373-d00c-401f-825e-d46a8d9c0bca&p_from=p_detail&r_id=dc5545b4-4091-4130-8c9c-d8bedd2a7177&r_type=1&v_c_id=4
  - 分页：默认10条/页，规范组件即可
[图片]
开源软件扫描
开源网安 SourceCheck 接口
单击选中「开源软件扫描」标签，展示开源软件扫描的结果，包括「扫描结果统计」、「漏洞列表」、「许可列表」、「组件列表」
- 「扫描信息」：展示「扫描状态」、「错误说明」、「组件数」、「漏洞数」、「许可数」。
  - 「扫描状态」：包括未扫描、扫描中、扫描失败、扫描成功。始终显示最近一次扫描获取到的结果，本次扫描未完成则显示上一次扫描结果，没有扫描结果则显示为空。“扫描中”时页面扫描结果显示为空
  - 「错误说明」：扫描失败时展示具体失败原因，其他状态不显示该字段
  - 「错误说明」：扫描失败时展示具体失败原因，其他状态不显示该字段
  - 卡片左上角显示「查看详情」按钮，点击后跳转到「开源软件」内嵌产品对应的应用详情页。
url 示例：http://1.95.46.47:30000/#/assets/app/detail/detail-component?appVersionId=1900448737830895617&name=WebGoat-8.2.2_1741937260306&taskId
- 「漏洞列表」：展示字段「漏洞信息」（漏洞编号）、「严重级别」、「弱点类型」、「影响组件数」、「漏洞描述」。
  - 点击任意行漏洞，直接跳转到「开源网安-开源软件」产品，此仓库应用该漏洞的详情页
Url 示例：http://1.95.46.47:30000/#/assets/vul/detail?id=SZ-2024-31517
  - 分页：默认10条/页，规范组件即可。
- 「许可列表」：展示字段「许可简称」、「严重级别」、「许可全称」
  - 点击任意行数据，直接跳转到「开源网安-开源软件」产品，此仓库应用该许可的详情页
Url 示例：http://1.95.46.47:30000/#/assets/license/detail?licenseId=CDDL-1.0&appVersionId=1900448737830895617&useType=default
  - 分页：默认10条/页，规范组件即可。
- 「组件列表」：展示字段「组件名称」、「严重级别」、「语言」、「许可名称」、「当前版本」、「推荐版本」、「最新版本」、「国家」
  - 点击任意行数据，直接跳转到「开源网安-开源软件」产品，此仓库应用该组件的详情页
Url 示例：http://1.95.46.47:30000/#/assets/component/detail?componentId=1896834040514740380&appVersionId=1900448737830895617
  - 分页：默认10条/页，规范组件即可。
[图片]
镜像扫描
单击选中「镜像扫描」标签，展示仓库 CI阶段镜像扫描的结果，主要包含「镜像列表」，列表同领航「镜像安全 - 镜像生命周期 - CI」
- 「镜像列表」：列表展示「基本信息」（含镜像名称、流水线名称、白名单标识）、「安全问题」、「漏洞统计」、「状态」、「扫描时间」、「操作」，其中：
  - 「基本信息」：镜像名称 hover 显示复制icon，点击可复制镜像名称
  - 「安全问题」：漏洞和敏感文件icon，检测到对应安全问题时icon高亮，icon hover后显示类型名
  - 「漏洞统计」：统计不同严重等级的漏洞数，包含 严重、高、中、低、未知
  - 「状态」：含通过、告警、阻断、异常
  - 操作列：告警和阻断的状态，操作列展示「添加白名单」。点击后显示模态窗口，选择有效期将当前镜像加入到白名单，成功后关闭弹窗并全局提示“白名单新增成功”。
  - 排序：列表默认按扫描时间倒序
  - 分页：默认10条/页，规范组件即可
- 点击镜像列表任意数据，跳转到「镜像安全-镜像生命周期-CI-镜像详情」页查看镜像详情（领航页面）
Url 示例：https://console.tensorsecurity.cn/#/image-security/life-cycle/ci-images-detail?id=10508
[图片]
[图片]
用户管理
- 系统内置一个管理员用户名：<EMAIL> / Y5T!20uZK25a，且仅有一个管理员用户名，其余用户为普通用户。只有管理员用户名才有「用户管理」菜单的权限。
- 与其他三个工具系统 sso 对接时，映射的角色如下：
Portal 角色
代码扫描 - CodeSec
开源软件 - SourceCheck
镜像扫描 - 领航
管理员
企业管理员
企业管理员
超管
SeedAdmin，但用户名和密码需要按内置的要求调整下
普通用户
普通用户
普通用户
管理员
权限模块：平台、容器安全
用户列表
- 列表展示字段包括「用户名」、「角色」、「邮箱」、「手机号」、「操作」（重置密码、编辑、删除）
- 支持的筛选项包括「用户名」&「手机号」&「邮箱」(模糊搜索匹配)、「角色」(下拉多选)、「状态」(下拉多选)。列表分页展示，每页最多展示10条记录，不足10条记录不展示分页器。列表按照用户创建时间倒序
[图片]
新增用户
点击列表右上角「新增」按钮，需要填写的字段如下：
- 用户名：必填，唯一值，默认文案为「请输入用户名，仅支持输入英文、数字、"-"以及"_"，用户名为登录名」；点击新增后，若用户名重复提示“用户名已存在”。
- 角色：默认为普通用户，不支持修改
- 密码：密码8-16位，至少包含字母/数字/特殊符号的2种组合。默认密文显示，可以点击icon 切换为明文。
  - 输入框默认文案为「请输入密码，密码支持字母/数字/特殊符号(~!@$%^&*.)」。
  - 如果密码规则校验不通过，「密码」输入框线条标红，填框下下方文案提醒「密码8-16位，至少包含字母/数字/特殊符号(~!@$%^&*.)2种组合」
- 邮箱：必填，唯一值，登录账号。需要做邮箱格式校验。格式错误提示“邮箱格式不正确”；点击新增后，若邮箱重复提示“用户邮箱已存在”。
- 手机号：必填，需要做手机号格式校验。格式错误提示“手机号格式不正确”
- 填写完成后，点击「新增」，成功后，跳转至列表页，全局提示「新增成功」，失败时停在当前页面，全局提示「新增失败，请重试」。
- 当点击「取消」或顶部「返回」icon时，若在新增页面没有任何改动或输入，则直接返回，若有任何的输入，需二次弹窗提示，「取消后，本次填写的内容将不会被保留，是否确定取消?」，按钮「确定」和「取消」
[图片]
编辑用户
点击操作列「编辑」按钮，可对「用户名」、「角色」、「手机号」、「邮箱」、「备注」进行编辑。
点击「保存」时，提交编辑改动，保存成功后，返回到进入编辑前的页面，全局提示，「保存成功」，若失败，则保留当前编辑页内容不动，全局提示「保存失败，请重试」。
当点击「取消」或顶部「返回」icon时，若在编辑页面没有任何可改动或输入，则直接返回，若有任何的输入，需二次弹窗提示，「取消后，本次填写的内容将不会被保留，是否确定取消?」，按钮「确定」和「取消」，交互同二次确认规范要求，这里不再赘述。
[图片]
[图片]
删除用户
操作列点击「删除」按钮，二次提醒「删除后，数据将不会被恢复，是否确定删除用户xxx?」，按钮「删除」或「取消」，点击取消，关闭模态窗，点击「删除」，若删除失败，关闭模态，在当前页面全局提示「删除失败，请重试」，若删除成功，操作列发起的删除，则直接关闭模态，全局提示「删除成功」。
[图片]
重置密码
用户列表点击「重置密码」，弹出模态窗口，输入当前账户的登录密码后进行校验，校验成功生成随机密码，支持快捷复制
[图片]
[图片]
启用/停用（弃）
操作列点击「停用」，弹出二次确认框，文案为「停用用后，该用户不能再正常登录，是否确定停用?」，按钮「取消」和「停用」，点击取消直接关闭模态不改动，点击后「停用」将用户状态变为停用。停用的用户登录时，在登录页面全局提示「用户名已停用，请联系管理员」
操作列点击「启用」，弹出二次确认框，文案为「启用后，该用户可再次正常登录，是否确定启用?」，按钮「取消」和「启用」，点击「取消」直接关闭模态不改动，点击「启用」将恢复正常。
解除锁定（弃）
已锁定的用户，点击操作列的「解锁」按钮，弹出二二次确认框，「解锁后，该用户可再次登录系统，是否确定解锁?」按钮为「解锁」&「取消」解锁成功后，关闭模态框，全局提示「解锁成功」，解锁失败，关闭模态框，全局提示「解锁失败，请重试」，同时刷新列表用户状态信息
