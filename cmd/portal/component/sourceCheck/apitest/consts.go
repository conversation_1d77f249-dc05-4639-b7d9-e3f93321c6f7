package apitest

import (
	"os"
)

// getEnvOrDefault 获取环境变量，如果不存在则返回默认值
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// 测试配置常量 - 从环境变量读取，避免敏感信息暴露
var (
	BaseURL     = getEnvOrDefault("SOURCECHECK_BASE_URL", "")
	ProjectUuid = getEnvOrDefault("SOURCECHECK_PROJECT_UUID", "")
	APPId       = getEnvOrDefault("SOURCECHECK_APP_ID", "") // 有些地方也叫 appID
	Token       = getEnvOrDefault("SOURCECHECK_TOKEN", "")
)
