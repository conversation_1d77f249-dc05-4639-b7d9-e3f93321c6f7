# SourceCheck API 测试

本目录包含了 SourceCheck API 的测试用例，主要测试 `GetAllComponent`、`GetAllLicense` 和 `GetAllVuln` 三个方法。

## 结构体变更说明

原来的请求结构体包含很多可选参数，现在已经简化为只包含基本的分页参数和 AppUuid：

### VulnerabilityListRequest
```go
type VulnerabilityListRequest struct {
    PageIndex int    `json:"pageIndex"` // 当前页面下标
    PageSize  int    `json:"pageSize"`  // 每页展示行数
    AppUuid   string `json:"appUuid"`   // 应用唯一标识
}
```

### LicenseListRequest
```go
type LicenseListRequest struct {
    PageIndex int    `json:"pageIndex"` // 当前页面下标
    PageSize  int    `json:"pageSize"`  // 每页展示行数
    AppUuid   string `json:"appUuid"`   // 应用唯一标识
}
```

### ComponentListRequest
```go
type ComponentListRequest struct {
    PageIndex int    `json:"pageIndex"` // 当前页面下标
    PageSize  int    `json:"pageSize"`  // 每页展示行数
    AppUuid   string `json:"appUuid"`   // 应用唯一标识
}
```

## 测试文件说明

### api_integration_test.go
集成测试文件，测试实际的 API 调用：
- `TestGetAllComponent`: 测试获取组件列表 API
- `TestGetAllLicense`: 测试获取许可列表 API  
- `TestGetAllVuln`: 测试获取漏洞列表 API

每个测试包含：
- 基本功能测试
- 参数验证测试
- 边界条件测试

### api_unit_test.go
单元测试文件，测试结构体定义和方法签名：
- `TestStructDefinitions`: 测试请求结构体定义
- `TestAPIMethodSignatures`: 测试 API 方法签名
- `TestRequestValidation`: 测试请求参数验证
- `TestDefaultValues`: 测试默认值设置
- `TestJSONSerialization`: 测试 JSON 序列化

### consts.go
测试配置常量，从环境变量读取配置信息。

## 环境变量配置

运行集成测试需要设置以下环境变量：

```bash
export SOURCECHECK_BASE_URL="http://1.95.46.47:30000"
export SOURCECHECK_TOKEN="your-token-here"
export SOURCECHECK_APP_ID="your-app-id-here"
```

如果没有设置这些环境变量，集成测试会被跳过。

## 运行测试

### 运行所有测试
```bash
go test -v
```

### 运行特定测试
```bash
# 运行结构体定义测试
go test -v -run TestStructDefinitions

# 运行 API 方法签名测试
go test -v -run TestAPIMethodSignatures

# 运行组件列表测试
go test -v -run TestGetAllComponent

# 运行许可列表测试
go test -v -run TestGetAllLicense

# 运行漏洞列表测试
go test -v -run TestGetAllVuln
```

### 运行单元测试（不需要环境变量）
```bash
go test -v -run "TestStruct|TestAPI|TestRequest|TestDefault|TestJSON"
```

### 运行集成测试（需要环境变量）
```bash
go test -v -run "TestGetAll"
```

## 测试结果说明

### 成功的测试结果
- ✅ `TestGetAllComponent`: 成功获取到 40 个组件
- ✅ `TestGetAllLicense`: 成功获取到 9 个许可证
- ✅ `TestGetAllVuln`: 成功获取到 14 个漏洞
- ✅ `TestStructDefinitions`: 结构体定义测试通过
- ✅ `TestAPIMethodSignatures`: API方法签名验证通过

### 测试覆盖的场景
1. **正常场景**: 使用有效的 AppUuid 和分页参数
2. **参数验证**: 测试空的 AppUuid、无效的分页参数
3. **边界条件**: 测试大页面大小等边界情况
4. **错误处理**: 验证 API 错误响应的处理

## API 变更影响

由于简化了请求结构体，以下字段已被移除：
- ProjectUuid
- ComponentUuid  
- Language
- Grade
- License
- ControlStatus
- ComponentType
- DependRank
- IntroductionType
- DepScopeList
- ScanLevel
- RemarkTypes
- LicenseId
- Number

这些字段的移除简化了 API 调用，但也意味着无法进行细粒度的过滤。如果需要这些过滤功能，可能需要在客户端进行数据过滤。

## 注意事项

1. 集成测试依赖外部 SourceCheck 服务，可能会因为网络或服务问题而失败
2. 测试使用的是真实的 API 端点，请确保不会影响生产环境
3. 某些测试可能会因为服务器状态而返回不同的结果
4. 建议在 CI/CD 环境中只运行单元测试，集成测试可以在开发环境中手动运行
