package apitest

import (
	"context"
	"testing"

	"github.com/smartystreets/goconvey/convey"
	"gitlab.com/piccolo_su/vegeta/cmd/portal/component/sourceCheck"
)

// TestGetAllComponent 测试获取组件列表API
func TestGetAllComponent(t *testing.T) {
	convey.Convey("测试GetAllComponent API", t, func() {
		// 跳过测试如果没有配置环境变量
		if BaseURL == "" || Token == "" || APPId == "" {
			t.Skip("跳过测试：缺少必要的环境变量配置")
			return
		}

		// 创建API实例
		api := sourceCheck.NewApi(
			sourceCheck.WithBaseURL(BaseURL),
			sourceCheck.WithAuthorization(Token),
		)

		req := sourceCheck.ComponentListRequest{
			PageIndex: 1,
			PageSize:  10,
			AppUuid:   APPId,
		}

		components, err := api.GetAllComponent(context.Background(), req)

		convey.So(len(components), convey.ShouldBeGreaterThan, 0)
		convey.So(err, convey.ShouldBeNil)
	})
}

// TestGetAllLicense 测试获取许可列表API
func TestGetAllLicense(t *testing.T) {
	convey.Convey("测试GetAllLicense API", t, func() {
		// 跳过测试如果没有配置环境变量
		if BaseURL == "" || Token == "" || APPId == "" {
			t.Skip("跳过测试：缺少必要的环境变量配置 (SOURCECHECK_BASE_URL, SOURCECHECK_TOKEN, SOURCECHECK_APP_ID)")
			return
		}

		// 创建API实例
		api := sourceCheck.NewApi(
			sourceCheck.WithBaseURL(BaseURL),
			sourceCheck.WithAuthorization(Token),
		)

		convey.Convey("基本功能测试", func() {
			req := sourceCheck.LicenseListRequest{
				PageIndex: 1,
				PageSize:  10,
				AppUuid:   APPId,
			}

			licenses, err := api.GetAllLicense(context.Background(), req)
			convey.So(len(licenses), convey.ShouldBeGreaterThan, 0)
			convey.So(err, convey.ShouldBeNil)

		})
	})
}

// TestGetAllVuln 测试获取漏洞列表API
func TestGetAllVuln(t *testing.T) {
	convey.Convey("测试GetAllVuln API", t, func() {
		// 跳过测试如果没有配置环境变量
		if BaseURL == "" || Token == "" || APPId == "" {
			t.Skip("跳过测试：缺少必要的环境变量配置 (SOURCECHECK_BASE_URL, SOURCECHECK_TOKEN, SOURCECHECK_APP_ID)")
			return
		}

		// 创建API实例
		api := sourceCheck.NewApi(
			sourceCheck.WithBaseURL(BaseURL),
			sourceCheck.WithAuthorization(Token),
		)

		convey.Convey("基本功能测试", func() {
			req := sourceCheck.VulnerabilityListRequest{
				PageIndex: 1,
				PageSize:  10,
				AppUuid:   APPId,
			}

			vulns, err := api.GetAllVuln(context.Background(), req)
			convey.So(len(vulns), convey.ShouldBeGreaterThan, 0)
			convey.So(err, convey.ShouldBeNil)
		})
	})
}
