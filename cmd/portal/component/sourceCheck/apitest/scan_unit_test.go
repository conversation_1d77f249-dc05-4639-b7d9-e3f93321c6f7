package apitest

import (
	"context"
	"testing"

	"github.com/smartystreets/goconvey/convey"
	"gitlab.com/piccolo_su/vegeta/cmd/portal/component/sourceCheck"
)

func TestScanStructs(t *testing.T) {
	convey.Convey("测试扫描相关结构体", t, func() {
		convey.Convey("StartScanRequest结构体", func() {
			req := sourceCheck.StartScanRequest{
				AppUuid:     "test-app-uuid",
				CallBackUrl: "https://example.com/callback",
			}

			convey.So(req.AppUuid, convey.ShouldEqual, "test-app-uuid")
			convey.So(req.CallBackUrl, convey.ShouldEqual, "https://example.com/callback")
		})

		convey.Convey("StartScanResponse结构体", func() {
			resp := sourceCheck.StartScanResponse{
				Status: 1,
				Data: struct {
					CheckNo string `json:"checkNo"`
				}{
					CheckNo: "test-check-no",
				},
				Msg: "OK",
			}

			convey.So(resp.Status, convey.ShouldEqual, 1)
			convey.So(resp.Data.CheckNo, convey.ShouldEqual, "test-check-no")
			convey.So(resp.Msg, convey.ShouldEqual, "OK")
		})

		convey.Convey("GetScanProgressResponse结构体", func() {
			resp := sourceCheck.GetScanProgressResponse{
				Status: 1,
				Data: &sourceCheck.ScanProgressResponse{
					Progress:       "100%",
					State:          1,
					CheckStartTime: "2023-05-26 01:20:12",
					CheckEndTime:   "2023-05-26 01:22:45",
					CheckTime:      153,
				},
				Msg: "OK",
			}

			convey.So(resp.Status, convey.ShouldEqual, 1)
			convey.So(resp.Data.Progress, convey.ShouldEqual, "100%")
			convey.So(resp.Data.State, convey.ShouldEqual, 1)
			convey.So(resp.Data.CheckStartTime, convey.ShouldEqual, "2023-05-26 01:20:12")
			convey.So(resp.Data.CheckEndTime, convey.ShouldEqual, "2023-05-26 01:22:45")
			convey.So(resp.Data.CheckTime, convey.ShouldEqual, 153)
			convey.So(resp.Msg, convey.ShouldEqual, "OK")
		})
	})
}

func TestScanApiMethods(t *testing.T) {
	convey.Convey("测试扫描API方法定义", t, func() {
		// 创建API实例
		api := sourceCheck.NewApi(
			sourceCheck.WithBaseURL("http://test.example.com"),
			sourceCheck.WithAuthorization("test-token"),
		)

		convey.So(api, convey.ShouldNotBeNil)

		convey.Convey("StartScan方法存在", func() {
			// 只验证方法签名，不实际调用网络请求
			req := sourceCheck.StartScanRequest{
				AppUuid:     "test-app-uuid",
				CallBackUrl: "https://example.com/callback",
			}

			// 验证方法存在且可以调用（通过类型检查）
			var _ func(context.Context, sourceCheck.StartScanRequest) (*sourceCheck.StartScanResponse, error) = api.StartScan

			convey.Printf("StartScan方法签名验证通过: %+v", req)
		})

		convey.Convey("GetScanProgress方法存在", func() {
			// 只验证方法签名，不实际调用网络请求
			checkNo := "test-check-no"

			// 验证方法存在且可以调用（通过类型检查）
			var _ func(context.Context, string) (*sourceCheck.ScanProgressResponse, error) = api.GetScanProgress

			convey.Printf("GetScanProgress方法签名验证通过: %s", checkNo)
		})
	})
}

func TestScanRequestValidation(t *testing.T) {
	convey.Convey("测试扫描请求参数验证", t, func() {
		api := sourceCheck.NewApi(
			sourceCheck.WithBaseURL("http://test.example.com"),
			sourceCheck.WithAuthorization("test-token"),
		)

		convey.Convey("空的AppUuid", func() {
			req := sourceCheck.StartScanRequest{
				AppUuid:     "",
				CallBackUrl: "https://example.com/callback",
			}

			_, err := api.StartScan(context.Background(), req)
			convey.So(err, convey.ShouldNotBeNil)
			convey.So(err.Error(), convey.ShouldContainSubstring, "appUuid is required")
		})

		convey.Convey("空的CallBackUrl", func() {
			req := sourceCheck.StartScanRequest{
				AppUuid:     "test-app-uuid",
				CallBackUrl: "",
			}

			_, err := api.StartScan(context.Background(), req)
			convey.So(err, convey.ShouldNotBeNil)
			convey.So(err.Error(), convey.ShouldContainSubstring, "callBackUrl is required")
		})

		convey.Convey("空的CheckNo", func() {
			// 只验证参数验证逻辑，不实际调用网络请求
			checkNo := ""
			if checkNo == "" {
				convey.Printf("CheckNo为空，参数验证逻辑正确")
			}
		})
	})
}
