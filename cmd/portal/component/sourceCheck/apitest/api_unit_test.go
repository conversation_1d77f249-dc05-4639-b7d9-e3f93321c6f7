package apitest

import (
	"context"
	"testing"

	"github.com/smartystreets/goconvey/convey"
	"gitlab.com/piccolo_su/vegeta/cmd/portal/component/sourceCheck"
	"gitlab.com/piccolo_su/vegeta/cmd/portal/model"
)

// TestStructDefinitions 测试结构体定义
func TestStructDefinitions(t *testing.T) {
	convey.Convey("测试API请求结构体定义", t, func() {
		convey.Convey("VulnerabilityListRequest结构体", func() {
			req := sourceCheck.VulnerabilityListRequest{
				PageIndex: 1,
				PageSize:  10,
				AppUuid:   "test-app-uuid",
			}

			convey.So(req.PageIndex, convey.ShouldEqual, 1)
			convey.So(req.PageSize, convey.ShouldEqual, 10)
			convey.So(req.AppUuid, convey.ShouldEqual, "test-app-uuid")
			convey.Printf("VulnerabilityListRequest: %+v", req)
		})

		convey.Convey("LicenseListRequest结构体", func() {
			req := sourceCheck.LicenseListRequest{
				PageIndex: 1,
				PageSize:  10,
				AppUuid:   "test-app-uuid",
			}

			convey.So(req.PageIndex, convey.ShouldEqual, 1)
			convey.So(req.PageSize, convey.ShouldEqual, 10)
			convey.So(req.AppUuid, convey.ShouldEqual, "test-app-uuid")
			convey.Printf("LicenseListRequest: %+v", req)
		})

		convey.Convey("ComponentListRequest结构体", func() {
			req := sourceCheck.ComponentListRequest{
				PageIndex: 1,
				PageSize:  10,
				AppUuid:   "test-app-uuid",
			}

			convey.So(req.PageIndex, convey.ShouldEqual, 1)
			convey.So(req.PageSize, convey.ShouldEqual, 10)
			convey.So(req.AppUuid, convey.ShouldEqual, "test-app-uuid")
			convey.Printf("ComponentListRequest: %+v", req)
		})
	})
}

// TestAPIMethodSignatures 测试API方法签名
func TestAPIMethodSignatures(t *testing.T) {
	convey.Convey("测试API方法签名", t, func() {
		// 创建API实例用于类型检查
		api := sourceCheck.NewApi(
			sourceCheck.WithBaseURL("http://test.example.com"),
			sourceCheck.WithAuthorization("test-token"),
		)

		convey.So(api, convey.ShouldNotBeNil)

		convey.Convey("GetAllVuln方法签名", func() {
			// 验证方法存在且签名正确
			var _ func(context.Context, sourceCheck.VulnerabilityListRequest) ([]*model.SourceCheckVuln, error) = api.GetAllVuln
			convey.Printf("GetAllVuln方法签名验证通过")
		})

		convey.Convey("GetAllLicense方法签名", func() {
			// 验证方法存在且签名正确
			var _ func(context.Context, sourceCheck.LicenseListRequest) ([]*model.SourceCheckLicense, error) = api.GetAllLicense
			convey.Printf("GetAllLicense方法签名验证通过")
		})

		convey.Convey("GetAllComponent方法签名", func() {
			// 验证方法存在且签名正确
			var _ func(context.Context, sourceCheck.ComponentListRequest) ([]*model.SourceCheckComponent, error) = api.GetAllComponent
			convey.Printf("GetAllComponent方法签名验证通过")
		})
	})
}

// TestRequestValidation 测试请求参数验证
func TestRequestValidation(t *testing.T) {
	convey.Convey("测试请求参数验证", t, func() {
		convey.Convey("分页参数验证", func() {
			convey.Convey("正常分页参数", func() {
				req := sourceCheck.VulnerabilityListRequest{
					PageIndex: 1,
					PageSize:  10,
					AppUuid:   "test-app-uuid",
				}

				convey.So(req.PageIndex, convey.ShouldBeGreaterThan, 0)
				convey.So(req.PageSize, convey.ShouldBeGreaterThan, 0)
				convey.So(req.AppUuid, convey.ShouldNotBeEmpty)
			})

			convey.Convey("边界分页参数", func() {
				req := sourceCheck.ComponentListRequest{
					PageIndex: 1,
					PageSize:  1000, // 大页面大小
					AppUuid:   "test-app-uuid",
				}

				convey.So(req.PageIndex, convey.ShouldEqual, 1)
				convey.So(req.PageSize, convey.ShouldEqual, 1000)
			})
		})

		convey.Convey("AppUuid参数验证", func() {
			convey.Convey("有效的AppUuid", func() {
				req := sourceCheck.LicenseListRequest{
					PageIndex: 1,
					PageSize:  10,
					AppUuid:   "f9ee71c999374989b1f0501b8fcfdf32",
				}

				convey.So(req.AppUuid, convey.ShouldNotBeEmpty)
				convey.So(len(req.AppUuid), convey.ShouldEqual, 32) // UUID长度
			})

			convey.Convey("空的AppUuid", func() {
				req := sourceCheck.VulnerabilityListRequest{
					PageIndex: 1,
					PageSize:  10,
					AppUuid:   "",
				}

				convey.So(req.AppUuid, convey.ShouldBeEmpty)
				convey.Printf("空AppUuid的请求: %+v", req)
			})
		})
	})
}

// TestDefaultValues 测试默认值设置
func TestDefaultValues(t *testing.T) {
	convey.Convey("测试默认值设置", t, func() {
		convey.Convey("创建带默认值的请求", func() {
			// 创建一个辅助函数来设置默认值
			createDefaultVulnRequest := func(appUuid string) sourceCheck.VulnerabilityListRequest {
				return sourceCheck.VulnerabilityListRequest{
					PageIndex: 1,
					PageSize:  100, // 默认页面大小
					AppUuid:   appUuid,
				}
			}

			req := createDefaultVulnRequest("test-app-uuid")
			convey.So(req.PageIndex, convey.ShouldEqual, 1)
			convey.So(req.PageSize, convey.ShouldEqual, 100)
			convey.So(req.AppUuid, convey.ShouldEqual, "test-app-uuid")
		})

		convey.Convey("创建带默认值的许可请求", func() {
			createDefaultLicenseRequest := func(appUuid string) sourceCheck.LicenseListRequest {
				return sourceCheck.LicenseListRequest{
					PageIndex: 1,
					PageSize:  500, // 默认页面大小
					AppUuid:   appUuid,
				}
			}

			req := createDefaultLicenseRequest("test-app-uuid")
			convey.So(req.PageIndex, convey.ShouldEqual, 1)
			convey.So(req.PageSize, convey.ShouldEqual, 500)
			convey.So(req.AppUuid, convey.ShouldEqual, "test-app-uuid")
		})

		convey.Convey("创建带默认值的组件请求", func() {
			createDefaultComponentRequest := func(appUuid string) sourceCheck.ComponentListRequest {
				return sourceCheck.ComponentListRequest{
					PageIndex: 1,
					PageSize:  500, // 默认页面大小
					AppUuid:   appUuid,
				}
			}

			req := createDefaultComponentRequest("test-app-uuid")
			convey.So(req.PageIndex, convey.ShouldEqual, 1)
			convey.So(req.PageSize, convey.ShouldEqual, 500)
			convey.So(req.AppUuid, convey.ShouldEqual, "test-app-uuid")
		})
	})
}

// TestJSONSerialization 测试JSON序列化
func TestJSONSerialization(t *testing.T) {
	convey.Convey("测试JSON序列化", t, func() {
		convey.Convey("VulnerabilityListRequest序列化", func() {
			req := sourceCheck.VulnerabilityListRequest{
				PageIndex: 1,
				PageSize:  10,
				AppUuid:   "test-app-uuid",
			}

			// 这里可以添加JSON序列化测试
			convey.Printf("VulnerabilityListRequest: %+v", req)
		})

		convey.Convey("LicenseListRequest序列化", func() {
			req := sourceCheck.LicenseListRequest{
				PageIndex: 2,
				PageSize:  20,
				AppUuid:   "test-app-uuid-2",
			}

			convey.Printf("LicenseListRequest: %+v", req)
		})

		convey.Convey("ComponentListRequest序列化", func() {
			req := sourceCheck.ComponentListRequest{
				PageIndex: 3,
				PageSize:  30,
				AppUuid:   "test-app-uuid-3",
			}

			convey.Printf("ComponentListRequest: %+v", req)
		})
	})
}
