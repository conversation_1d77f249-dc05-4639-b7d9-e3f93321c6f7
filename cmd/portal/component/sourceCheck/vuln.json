{"status": 1, "data": {"total": 14, "dataList": [{"customSzNo": "SZ-2018-01218", "customCveNo": "CVE-2018-1000215", "customCnnvdNo": "CNNVD-201808-599", "customCnvdNo": "CNVD-2018-16490", "grade": "HIGH", "cwe": "CWE-399", "vulnerabilityName": "Dave Gamble cJSON拒绝服务漏洞", "cweName": null, "description": "Dave Gamble cJSON是一款轻量级的JSON格式解析器。 \nDave Gamble cJSON 1.7.6及之前版本中的cJSON库存在安全漏洞。攻击者可利用该漏洞造成拒绝服务（内存泄露）。", "releaseDate": 1534694400000, "affectComponentCount": 1}, {"customSzNo": "SZ-2019-01347", "customCveNo": "CVE-2019-1010239", "customCnnvdNo": "CNNVD-201907-1126", "customCnvdNo": "", "grade": "HIGH", "cwe": "CWE-754", "vulnerabilityName": "DaveGamble/cJSON cJSON 代码问题漏洞", "cweName": "Improper Check for Unusual or Exceptional Conditions", "description": "cJSON是一款轻量级的开源JSON解析器。 \nDaveGamble/cJSON cJSON 1.7.8版本中的‘cJSON_GetObjectItemCaseSensitive()’函数存在代码问题漏洞。该漏洞源于网络系统或产品的代码开发过程中存在设计或实现不当的问题。", "releaseDate": 1563465600000, "affectComponentCount": 1}, {"customSzNo": "SZ-2019-03320", "customCveNo": "CVE-2019-11834", "customCnnvdNo": "CNNVD-201905-218", "customCnvdNo": "", "grade": "CRITICAL", "cwe": "CWE-119", "vulnerabilityName": "cJSON 缓冲区错误漏洞", "cweName": "Improper Restriction of Operations within the Bounds of a Memory Buffer", "description": "cJSON是一款轻量级的开源JSON解析器。 \ncJSON 1.7.11之前版本中存在缓冲区错误漏洞。该漏洞源于网络系统或产品在内存上执行操作时，未正确验证数据边界，导致向关联的其他内存位置上执行了错误的读写操作。攻击者可利用该漏洞导致缓冲区溢出或堆溢出等。", "releaseDate": 1557331200000, "affectComponentCount": 1}, {"customSzNo": "SZ-2019-03321", "customCveNo": "CVE-2019-11835", "customCnnvdNo": "CNNVD-201905-219", "customCnvdNo": "", "grade": "CRITICAL", "cwe": "CWE-119", "vulnerabilityName": "cJSON 缓冲区错误漏洞", "cweName": "Improper Restriction of Operations within the Bounds of a Memory Buffer", "description": "cJSON是一款轻量级的开源JSON解析器。\r\ncJSON 1.7.11之前版本中存在缓冲区错误漏洞。该漏洞源于网络系统或产品在内存上执行操作时，未正确验证数据边界，导致向关联的其他内存位置上执行了错误的读写操作。攻击者可利用该漏洞导致缓冲区溢出或堆溢出等。", "releaseDate": 1557331200000, "affectComponentCount": 1}, {"customSzNo": "SZ-2022-07105", "customCveNo": "CVE-2022-29526", "customCnnvdNo": "CNNVD-202205-3035", "customCnvdNo": "", "grade": "MEDIUM", "cwe": "CWE-269", "vulnerabilityName": "Google Go 权限许可和访问控制问题漏洞", "cweName": "Improper Privilege Management", "description": "Google Go是美国谷歌（Google）公司的一种静态强类型、编译型、并发型，并具有垃圾回收功能的编程语言。\r\nGoogle go 存在权限许可和访问控制问题漏洞，该漏洞源于系统调用中的faccessat存在检查错误。", "releaseDate": 1655913600000, "affectComponentCount": 1}, {"customSzNo": "SZ-2023-27497", "customCveNo": "CVE-2023-44487", "customCnnvdNo": "CNNVD-202310-667", "customCnvdNo": "CNVD-2023-75597", "grade": "HIGH", "cwe": "CWE-Unknown", "vulnerabilityName": "Apache HTTP/2 安全漏洞", "cweName": null, "description": "HTTP/2是超文本传输协议的第二版，主要用于保证客户机与服务器之间的通信。\r\nApache HTTP/2存在安全漏洞。攻击者利用该漏洞导致系统拒绝服务。以下产品和版本受到影响：.NET 6.0,ASP.NET Core 6.0,.NET 7.0,Microsoft Visual Studio 2022 version 17.2,Microsoft Visual Studio 2022 version 17.4,Microsoft Visual Studio 2022 version 17.6,Microsoft Visual Studio 2022 version 17.7,Windows 10 Version 1809 for 32-bit Systems,Windows 10 Version 1809 for x64-based Systems,Windows 10 Version 1809 for ARM64-based Systems,Windows Server 2019,Windows Server 2019 (Server Core installation),Windows Server 2022,Windows Server 2022 (Server Core installation),Windows 11 version 21H2 for x64-based Systems,Windows 11 version 21H2 for ARM64-based Systems,Windows 10 Version 21H2 for 32-bit Systems,Windows 10 Version 21H2 for ARM64-based Systems,Windows 10 Version 21H2 for x64-based Systems,Windows 11 Version 22H2 for ARM64-based Systems,Windows 11 Version 22H2 for x64-based Systems,Windows 10 Version 22H2 for x64-based Systems,Windows 10 Version 22H2 for ARM64-based Systems,Windows 10 Version 22H2 for 32-bit Systems,Windows 10 Version 1607 for 32-bit Systems,Windows 10 Version 1607 for x64-based Systems,Windows Server 2016,Windows Server 2016 (Server Core installation),ASP.NET Core 7.0。", "releaseDate": 1696867200000, "affectComponentCount": 2}, {"customSzNo": "SZ-2023-34811", "customCveNo": "CVE-2023-50472", "customCnnvdNo": "CNNVD-202312-1362", "customCnvdNo": "", "grade": "HIGH", "cwe": "CWE-476", "vulnerabilityName": "cJSON 安全漏洞", "cweName": "NULL Pointer Dereference", "description": "cJSON是一款轻量级的开源JSON解析器。\r\ncJSON v1.7.16版本存在安全漏洞，该漏洞源于cJSON.c 中的函数 cJSON_SetValuestring包含分段违规。", "releaseDate": 1702483200000, "affectComponentCount": 1}, {"customSzNo": "SZ-2023-34813", "customCveNo": "CVE-2023-50471", "customCnnvdNo": "CNNVD-202312-1357", "customCnvdNo": "", "grade": "HIGH", "cwe": "CWE-Unknown", "vulnerabilityName": "cJSON 安全漏洞", "cweName": null, "description": "cJSON是一款轻量级的开源JSON解析器。\r\ncJSON v1.7.16版本存在安全漏洞，该漏洞源于cJSON.c 中的函数 cJSON_InsertItemInArray包含分段违规。", "releaseDate": 1702483200000, "affectComponentCount": 1}, {"customSzNo": "SZ-2023-35830", "customCveNo": "", "customCnnvdNo": "", "customCnvdNo": "", "grade": "MEDIUM", "cwe": "CWE-121", "vulnerabilityName": "基于堆栈的缓冲区溢出", "cweName": "Stack-based Buffer Overflow", "description": "当处理使用病态深度嵌套的输入时，此包的受影响版本容易受到基于堆栈的缓冲区溢出的攻击。", "releaseDate": 1703174400000, "affectComponentCount": 1}, {"customSzNo": "SZ-2024-03472", "customCveNo": "CVE-2024-22025", "customCnnvdNo": "CNNVD-202403-1801", "customCnvdNo": "", "grade": "MEDIUM", "cwe": "CWE-Unknown", "vulnerabilityName": "Node.js 安全漏洞", "cweName": null, "description": "Node.js是一个开源、跨平台的 JavaScript 运行时环境。\r\nNode.js 存在安全漏洞，该漏洞源于fetch()函数始终对 Brotli 进行解码，使资源耗尽导致拒绝服务。", "releaseDate": 1710777600000, "affectComponentCount": 1}, {"customSzNo": "SZ-2024-04233", "customCveNo": "CVE-2024-24786", "customCnnvdNo": "CNNVD-202403-452", "customCnvdNo": "", "grade": "HIGH", "cwe": "CWE-Unknown", "vulnerabilityName": "Google Go 安全漏洞", "cweName": null, "description": "Google Go是美国谷歌（Google）公司的一种静态强类型、编译型、并发型，并具有垃圾回收功能的编程语言。 \r\nGoogle Go 存在安全漏洞，该漏洞源于在解析某些无效的 JSON 时，protojson.Unmarshal 函数可能会进入无限循环。", "releaseDate": 1709568000000, "affectComponentCount": 1}, {"customSzNo": "SZ-2024-07951", "customCveNo": "CVE-2024-28182", "customCnnvdNo": "CNNVD-202404-586", "customCnvdNo": "", "grade": "MEDIUM", "cwe": "CWE-Unknown", "vulnerabilityName": "Nghttp2 安全漏洞", "cweName": null, "description": "Nghttp2是Nghttp2社区的一个用于实现HTTP/2的C库。\r\nNghttp2 1.61.0 之前版本存在安全漏洞，该漏洞源于读取无限数量的 HTTP/2 CONTINUATION 帧可能导致 CPU 使用率过高。", "releaseDate": 1712160000000, "affectComponentCount": 1}, {"customSzNo": "SZ-2024-10631", "customCveNo": "CVE-2024-31755", "customCnnvdNo": "CNNVD-202404-3303", "customCnvdNo": "", "grade": "HIGH", "cwe": "CWE-476", "vulnerabilityName": "cJSON 安全漏洞", "cweName": "NULL Pointer Dereference", "description": "cJSON是一款轻量级的开源JSON解析器。\r\ncJSON v1.7.17版本存在安全漏洞，该漏洞源于包含分段违规，可以通过 cJSON.c 中函数 cJSON_SetValuestring 的第二个参数触发。", "releaseDate": 1714060800000, "affectComponentCount": 1}, {"customSzNo": "SZ-2024-43246", "customCveNo": "CVE-2024-45339", "customCnnvdNo": "CNNVD-202501-3924", "customCnvdNo": "", "grade": "HIGH", "cwe": "CWE-Unknown", "vulnerabilityName": "glog 安全漏洞", "cweName": null, "description": "glog是Go开源的一个 Go 的分级执行日志。\r\nglog 1.2.4之前版本存在安全漏洞，该漏洞源于存在敏感文件覆盖漏洞，导致攻击者能预创建符号链接使程序退出修复。", "releaseDate": 1737993600000, "affectComponentCount": 2}], "pageIndex": 1, "pageSize": 100}, "msg": "OK"}