apiVersion: v1
data:
  default.conf: |
    server {
        listen       80;
        server_name  localhost;
        sendfile on;
        tcp_nopush on;
        client_max_body_size 0;
        #charset koi8-r;
        #access_log  /var/log/nginx/host.access.log  main;
        #
        proxy_set_header Host $http_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        #support for http1.1
        proxy_http_version 1.1;
        proxy_set_header Connection "";
        location / {
            root   /usr/share/nginx/html;
            index  index.html index.htm;
            try_files $uri $uri/ /index.html;
        }
        error_page  404              /404.html;
        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   /usr/share/nginx/html;
        }
        location /api/ {
            proxy_pass  http://portal-backend:10800;
        }
    }
kind: ConfigMap
metadata:
  labels:
    app.kubernetes.io/instance: navigator
  name: portal-nginx-conf
  namespace: tensorsec
