package main

import (
	"bytes"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"net/http"

	"gitlab.com/piccolo_su/vegeta/cmd/portal/utils"
)

type User struct {
	UserName       string `json:"userName"`
	UserEmail      string `json:"userEmail"`
	Password       string `json:"password"`
	Phone          string `json:"phone,omitempty"`
	KeyExpiredDate string `json:"keyExpiredDate,omitempty"`
}

// CreateUserParam 定义创建账号的请求结构体
type CreateUserParam struct {
	User         User
	AccessSecret string
	AccessKey    string
}

// CreateUserResponse 定义创建账号的响应结构体
type CreateUserResponse struct {
	Status  bool   `json:"status"`
	Message string `json:"message"`
	Data    struct {
		AccessKey    string `json:"accessKey"`
		AccessSecret string `json:"accessSecret"`
	} `json:"data"`
}

func CreateUser(baseUrl string, request CreateUserParam) (*CreateUserResponse, error) {
	// 将请求结构体转换为JSON
	apiURL := fmt.Sprintf("%s/%s", baseUrl, "cs/api/v4/user/createAccount")
	requestBody, err := json.Marshal(request.User)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %v", err)
	}
	data := map[string]interface{}{}
	_ = json.Unmarshal(requestBody, &data)
	header := utils.BuildV2Header(data, request.AccessKey, request.AccessSecret, nil)
	// 创建一个新的请求对象
	req, err := http.NewRequest(http.MethodPost, apiURL, bytes.NewBuffer(requestBody))
	if err != nil {
		return nil, err
	}
	// 设置自定义请求头
	for k, v := range header {
		req.Header.Set(k, v)
	}
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer func() { _ = resp.Body.Close() }()

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	// 解析响应体
	response := &CreateUserResponse{}
	if err := json.NewDecoder(resp.Body).Decode(response); err != nil {
		return nil, fmt.Errorf("failed to decode response: %v", err)
	}
	return response, nil
}

func main() {
	// 定义API URL
	// apiURL := "http://1.95.46.47:30000/cs/api/v4/user/createAccount"
	apiURL := "http://1.95.60.107:28081"

	// 创建请求体
	request := CreateUserParam{
		User: User{
			UserName:  "admin4",
			UserEmail: "<EMAIL>",
			Password:  "admin@123",
		},
		AccessKey:    "8cf51906-81de-4c2b-9eee-1a2abccfb7be",
		AccessSecret: "eyJhbGciOiJIUzUxMiJ9.eyJub25jZSI6ImI3MTRiYjJlLTVhODgtNDc0Yy1hYzVkLWZhZGJhZWQ5YTk0YiIsInN1YiI6IjhjZjUxOTA2LTgxZGUtNGMyYi05ZWVlLTFhMmFiY2NmYjdiZSJ9.E0hJXC_r5EeDMBGy7y7l4Js5gH9xM3vpaxbT0sGWVcuErrrPK9eTxLp8FNYicDwmGM1Xh7o1iOHAUAvgJxxxBQ",
		// AccessSecret: "eyJhbGciOiJIUzUxMiJ9.eyJub25jZSI6ImQ2MGEwMTY4LTU3YTUtNDc2Mi04Y2Y5LTZkOTQyODQyZDFhZSIsInN1YiI6ImZiOWQ2MDMyLWUzZmYtNDM1Ny04YzQyLTE4YTE3ZmQ4NTViZiJ9.x-7_um6KLeDZgFPJSNRQyxNflEQLHtp5AIj80VXZJ53ekZaMx4ClTBiuiV_FaceN_dxja8H7Sf6qNh6WKB4rYw",
	}

	hash := sha256.Sum256([]byte(request.User.Password))
	signature := hex.EncodeToString(hash[:])
	request.User.Password = signature

	// 调用创建账号的函数
	response, err := CreateUser(apiURL, request)
	if err != nil {
		fmt.Printf("Error creating account: %v\n", err)
		return
	}

	// 输出响应
	fmt.Printf("Status: %v\n", response.Status)
	fmt.Printf("Message: %s\n", response.Message)
	fmt.Printf("CodesecSk: %s\n", response.Data.AccessKey)
	fmt.Printf("CodesecAk: %s\n", response.Data.AccessSecret)
}

/*
map[accessKey:fb9d6032-e3ff-4357-8c42-18a17fd855bf x-cs-nonce:d590c71a-ddf7-42c0-a50e-bf65e53f536f x-cs-signature:4426f6f83eda146b10b3bc78657fcafb6036bdc68d00a2b85cda21f5e06747fb x-cs-timestamp:*************]
{"status":true,"message":"成功。","data":{"accessKey":"70b00b10-1fb5-4a71-a028-4df42d822ecf","accessSecret":"eyJhbGciOiJIUzUxMiJ9.eyJub25jZSI6ImY1YzRiZWUzLWM2MmEtNDJkNy1iOTBlLWFhMzI1YzRkNzYzZiIsInN1YiI6IjcwYjAwYjEwLTFmYjUtNGE3MS1hMDI4LTRkZjQyZDgyMmVjZiJ9.vegOFYh8vs6yo_EYocCOf7SmGxNBA4R7o8A02jggOWocT8jdKzp_NeGQJtcWFnaEC8iUanNWrxIxjvAUSNlcrw"}}
Status: true
Message: 成功。
CodesecSk: 70b00b10-1fb5-4a71-a028-4df42d822ecf
CodesecAk: eyJhbGciOiJIUzUxMiJ9.eyJub25jZSI6ImY1YzRiZWUzLWM2MmEtNDJkNy1iOTBlLWFhMzI1YzRkNzYzZiIsInN1YiI6IjcwYjAwYjEwLTFmYjUtNGE3MS1hMDI4LTRkZjQyZDgyMmVjZiJ9.vegOFYh8vs6yo_EYocCOf7SmGxNBA4R7o8A02jggOWocT8jdKzp_NeGQJtcWFnaEC8iUanNWrxIxjvAUSNlcrw
*/
