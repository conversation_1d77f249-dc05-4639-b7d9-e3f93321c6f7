# 开源网安代码审核平台接口示例V2.0

#### 使用说明

- 运行案例时先修改`Constants`类中的配置
- `CS_URL`api地址
- `accessKey`用户API访问KEY
- `openToken`签名密钥

#### V3部分接口示例列表
V3基于V2开发，因此仅部分接口不一致。

- 4.27 根据多个语言查询安全规则漏洞树 `TreeListByLanguageIdsExample`
- 4.32 获取编码规范类别列表 `ListCodingRulesTypeExample`
- 4.32 根据条件查询编码规范漏洞信息 `ListCodingRulesByPageExample`
- 4.33 根据多个语言查询编码规范漏洞树 `TreeListCodingByLanguageIdsExample`
- 4.34 创建扫描方案 `CreateRuleSetV3Example`
- 4.36 编辑扫描方案 `EditRuleSetV3Example`
- 4.55 获取缺陷数据统计分类 `GetCountVulCategoryExample`
- 4.56 获取缺陷数据统计环形图 `ListWeaknessInfoExample`
- 4.57 获取缺陷数据统计折线图 `ListWeaknessInfoTrendExample`
- 4.58 获取工具规则列表 `ListToolRulesExample`
- 4.59 查询sonar是否开启 `GetSonarEnableExample`
- 4.60 查询sonar查询请求参数 `GetSonarSelectParamsExample`
- 4.61 分页查询sonar漏洞列表 `ListSonarByPageExample`
- 4.62 查询项目缺陷汇总 `GetSummaryDataExample`

#### V2接口示例列表

- 4.1 创建账号 `CreateAccountExample`
- 4.2 删除账号 `DeleteAccountExample`
- 4.3 更新秘钥 `UpdateAccessSecreteExample`
- 4.4 加载开发语言列表 `CodeLanguageListExample`
- 4.5 获取仓库依赖信息 `GetDependInfoExample`
- 4.6 创建空项目 `CreateProjectByVoidExample`
- 4.7 上传源码 `UploadSourcesExample`
- 4.8 根据GIT信息创建项目 `CreateProjectByGitInfoExample`
- 4.9 根据SVN信息创建项目 `CreateProjectBySvnInfoExample`
- 4.10 编辑项目 `EditProjectExample`
- 4.11 编辑GIT项目 `EditGitProjectExample`
- 4.12 编辑SVN项目 `EditSvnProjectExample`
- 4.13 删除项目 `DeleteProjectExample`
- 4.14 创建扫描任务（已废弃）
- 4.15 发起扫描任务 `ScanSubProjectExample`
- 4.16 查询扫描进度 `GetScanProgessExample`
- 4.17 终止扫描 `StopScanSubProjectExample`
- 4.18 获取扫描结果 `GetScanResultExample`
- 4.19 分页查询漏洞列表（漏洞列表页面右侧表格） `GetListDetailByVulDataIdExample`
- 4.20 查询漏洞详情（漏洞列表页面点击右侧表格后）`VulDetailExample`
- 4.21 获取漏洞分类 `GetCategoryListExample`
- 4.22 获取漏洞分类列表详情（漏洞列表页面左侧树） `GetVulListExample`
- 4.23 标记漏洞状态 `EditVulTagExample`
- 4.24 获取当前版本 `GetVersionExample`
- 4.25 统计代码行数 `StatisticCodeExample`
- 4.26 获取License信息 `GetLicenseInfoExample`
- 4.27 根据语言查询漏洞树 `TreeListByLanguageExample`
- 4.28 据条件查询漏洞信息 `FindVulDataListByPageExample`
- 4.29 根据漏洞ID查询漏洞信息 `GetVulDataExample`
- 4.30 获取漏洞等级列表 `RiskListExample`
- 4.31 创建规则集信息 `CreateRuleSetExample`
- 4.32 查询规则集列表 `RuleListExample`
- 4.33 编辑规则信息 `EditRuleSetExample`
- 4.34 删除规则集信息 `DeleteRuleSetExample`
- 4.35 查询白名单语言列表 `FindLanguageListExample`
- 4.36 根据语言ID查询白名单对应支持漏洞信息 `FindVulListExample`
- 4.37 创建白名单 `WhiteCreateExample`
- 4.38 查询白名单列表 `WhiteListExample`
- 4.39 修改白名单 `WhiteEditExample`
- 4.40 根据Id删除白名单 `WhiteDeleteBatchExample`
- 4.41 查询同组下全部规则集列表 `RuleListAllExample`
- 4.42 获取扫描日志 `ScanLogExample`
- 4.43 新增告警条件配置信息 `AddAlarmConfigExample`
- 4.44 修改告警条件配置信息 `ModifyAlarmConfigExample`
- 4.45 查询告警条件配置信息 `ListAlarmConfigExample`
- 4.46 删除告警条件配置信息 `BatchDeleteAlarmConfigExample`
- 4.47 根据项目名称或项目ID查询项目相关信息 `GetProjectOverViewExample`
- 4.50 重复发起项目扫描 `ScanSubProjectRepeatExample`


#### V1接口示例列表
- 6.1 加载开发语言列表 `CodeLanguageListExample`
- 6.2 上传源码文件 `UploadSourcesExample`
- 6.3 创建扫描项目 `CreateExample`
- 6.4 创建扫描任务 `CreateSubProjectExample`
- 6.5 执行扫描任务 `ScanSubProjectExample`
- 6.6 终止扫描任务 `StopScanSubProjectExample`
- 6.7 查询扫描任务状态 `StatusExample`
- 6.8 加载项目列表（分页）`ListProjectExample`
- 6.9 加载项目漏洞（分页）`ListProjectVulExample`
- 6.10 统计项目漏洞数 `GetProjectVulAllCountExample`
- 6.11 生成报告 `GenerateReportExample`
- 6.12 查询报告生成情况接口 `GetGenerateReportStatusExample`
- 6.13 下载报告接口 `DownloadReportExample`
- 6.14 标记漏洞状态 `EditVulTagExample`
- 6.15 根据GIT信息创建项目 `CreateProjectByGitInfoExample`
- 6.16 根据SVN信息创建项目 `CreateProjectBySvnInfoExample`
- 6.17 编辑GIT项目 `EditGitProjectExample`
- 6.18 编辑SVN项目 `EditSvnProjectExample`
- 6.19 启动扫描任务 `StartScanByGitInfoExample`
- 6.20 删除项目 `DeleteProjectExample`
- 6.21 查询单个项目概览结果 `GetProjectScanOverViewExample`
- 6.22 查询单个项目详细结果 `GetProjectScanDetailExample`
- 6.23 查询单个项目详细结果（携带漏洞描述和修复建议）`GetProjectScanDetailV2Example`
- 6.25 项目详情跳转URL `RedirectDetailUrlExample`
- 6.26 项目扫描结果跳转URL `RedirectScanResultUrlExample`
- 6.27 获取依赖信息 `GetDependInfoExample`