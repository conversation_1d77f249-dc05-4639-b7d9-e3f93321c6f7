package com.seczone.ssp.example.v2.project;

import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

/**
 * <AUTHOR>
 * @description 4.13 删除项目
 * @since 2022/12/14 14:07
 **/
public class DeleteProjectExample {

    public static void main(String[] args) {

        // 项目uuid
        String projectUuid = "b80f2bb7-76e5-4507-a25b-1cb9bda38ee3";

        // 请求url
        String url = Constants.CS_URL + "/cs/api/v2/project/"+projectUuid+"/deleteProject";

        // 发起请求
        HttpUtils.delete(url);

    }

}