package com.seczone.ssp.utils;

import cn.hutool.http.ContentType;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import com.seczone.ssp.Constants;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class HttpUtils {

    public static void post(String url) {
        request(url, null, Constants.POST, null);
    }

    public static void post(String url, Map<String, Object> params) {
        request(url, params, Constants.POST, null);
    }

    public static void post(String url, Map<String, Object> params, ContentType contentType) {
        request(url, params, Constants.POST, contentType);
    }

    public static void put(String url, Map<String, Object> params) {
        request(url, params, Constants.PUT, null);
    }

    public static void put(String url, Map<String, Object> params, ContentType contentType) {
        request(url, params, Constants.PUT, contentType);
    }

    public static void delete(String url) {
        request(url, null, Constants.DELETE, null);
    }

    public static void delete(String url, Map<String, Object> params, ContentType contentType) {
        request(url, params, Constants.DELETE, contentType);
    }

    public static void get(String url) {
        request(url, null, Constants.GET, null);
    }

    public static void get(String url, Map<String, Object> params) {
        request(url, params, Constants.GET, null);
    }

    public static void get(String url, Map<String, Object> params, ContentType contentType) {
        request(url, params, Constants.GET, contentType);
    }

    public static void request(String url, Map<String, Object> params, String methodType, ContentType contentType) {

        // 获取HttpRequest
        HttpRequest request = getHttpRequest(url, methodType);

        System.out.println("请求地址: " + url);
        System.out.println("请求参数: \n" + (params != null ? JSONUtil.formatJsonStr(JSONUtil.toJsonStr(params)) : "无"));

        // 构建请求头
        // V2接口
        if(url.contains("cs/api/v2") || url.contains("cs/api/v3") || url.contains("cs/api/v4")) {
            Map<String, String> headers = Constants.buildV2Header(url, params);
            request.addHeaders(headers);
        }
        // V1接口
        else if(url.contains("openapi")) {
            Map<String, String> headers = Constants.buildV1Header(url, params);
            request.addHeaders(headers);
        }

        // 请求内容类型
        // 默认Content-Type为json
        request.contentType(contentType != null ? contentType.getValue() : ContentType.JSON.getValue());
        if(params != null) {
            if(contentType == ContentType.FORM_URLENCODED) {
                // 表单
                request.form(params);
            } else if(contentType == ContentType.MULTIPART) {
                // 文件
                for(String key : params.keySet()) {
                    request.form(key, params.get(key));
                }
            } else {
                // 默认json
                request.body(JSONUtil.toJsonStr(params));
            }
        }

        // 调用
        String body = request.execute().body();

        System.out.println("响应参数: \n" + JSONUtil.formatJsonStr(body));
    }

    public static HttpRequest getHttpRequest(String url, String methodType) {
        HttpRequest request = null;
        switch (methodType) {
            case Constants.GET:
                request = HttpRequest.get(url);
                break;
            case Constants.POST:
                request = HttpRequest.post(url);
                break;
            case Constants.PUT:
                request = HttpRequest.put(url);
                break;
            case Constants.DELETE:
                request = HttpRequest.delete(url);
                break;
            default:
                break;
        }
        return request;
    }
}
