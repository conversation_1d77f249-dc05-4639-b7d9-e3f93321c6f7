package com.seczone.ssp.example.v2.project;

import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

/**
 * <AUTHOR>
 * @description 4.24 获取当前版本
 * @since 2022/12/15 11:07
 **/
public class GetVersionExample {

    public static void main(String[] args) {

        // 请求url
        String url = Constants.CS_URL + "/cs/api/v2/project/getVersion";

        // 发起请求
        HttpUtils.get(url);

    }
}