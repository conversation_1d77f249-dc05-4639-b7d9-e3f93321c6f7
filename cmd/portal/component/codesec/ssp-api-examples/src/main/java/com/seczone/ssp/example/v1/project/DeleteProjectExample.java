package com.seczone.ssp.example.v1.project;

import cn.hutool.http.ContentType;
import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 6.20 删除项目
 * @since 2022/12/21 15:05
 **/
public class DeleteProjectExample {

    public static void main(String[] args) {
        // 请求url
        String url = Constants.CS_URL + "/openapi/project/deleteProject";

        // 请求参数
        Map<String, Object> params = new HashMap<>();
        // 必填 项目Uuid
        params.put("projectUuid", "4b5c0c3f-71f1-4efd-8d30-b46a57e3739f");

        // 发起请求
        HttpUtils.post(url, params);
    }

}