package com.seczone.ssp.example.v2;

import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

/**
 * <AUTHOR>
 * @description 4.2 删除账号
 * @since 2022/12/13 11:48
 */
public class DeleteAccountExample {

    public static void main(String[] args) {

        // 要删除的用户的API访问KEY（accessKey）
        String userId = "5976ed34-5bea-481c-afe4-4a08c8818595";

        // 请求url
        String url = Constants.CS_URL + "/cs/api/v2/user/"+userId+"/deleteAccount";

        // 发起请求
        HttpUtils.request(url, null, Constants.DELETE, null);
    }

}
