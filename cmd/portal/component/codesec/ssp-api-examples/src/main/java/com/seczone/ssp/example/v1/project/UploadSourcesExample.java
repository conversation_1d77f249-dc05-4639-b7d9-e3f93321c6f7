package com.seczone.ssp.example.v1.project;

import cn.hutool.core.io.FileUtil;
import cn.hutool.http.ContentType;
import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

/**
 * <AUTHOR>
 * @description 6.2 上传源码文件
 * @since 2022/12/13 14:57
 **/
public class UploadSourcesExample {

    public static void main(String[] args) throws Exception {


        // 请求url
        String url = Constants.CS_URL + "/openapi/project/uploadSources";

        // 构建请求参数
        Map<String, Object> params = new HashMap<>();
        params.put("file", FileUtil.file("/Users/<USER>/Desktop/UpdateAccessSecreteExample.java.zip"));

        // 发起请求
//        HttpUtils.post(url, params, ContentType.MULTIPART);

        System.out.println(uploadFile(url, "/Users/<USER>/Desktop/UpdateAccessSecreteExample.java.zip", null));

    }

    /**
     * 其他上传文件方式
     *
     * @param address
     * @param filePath
     * @param paramsMap
     * @return
     * @throws Exception
     */
    public static String uploadFile(String address, String filePath, Map paramsMap) throws Exception {
        File file = new File(filePath);
        if (!file.exists()) {
            throw new RuntimeException("文件不存在");
        }
        // 参数定义
        String boundary = "----" + UUID.randomUUID().toString();
        // 换行符
        final String newLine = "\r\n";
        // 服务器的上传地址
        URL url = new URL(address);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        // 设置为POST请求
        conn.setRequestMethod("POST");
        // 发送POST请求必须设置如下两行
        conn.setDoOutput(true);
        conn.setDoInput(true);
        conn.setUseCaches(false);
        // 设置请求头参数
        conn.setRequestProperty("connection", "Keep-Alive");
        conn.setRequestProperty("Charset", "UTF-8");
        conn.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + boundary);
        // 设置分块上传
        conn.setRequestProperty("Transfer-Encoding", "chunked");
        conn.setChunkedStreamingMode(1024);
        // 设置接收数据的格式
        conn.setRequestProperty("Accept", "application/json");

        Map<String, String> header = Constants.buildV1Header(address, null);
        for(String key : header.keySet()) {
            conn.setRequestProperty(key, header.get(key));
        }

        try (OutputStream out = new DataOutputStream(conn.getOutputStream());
             DataInputStream in = new DataInputStream(new FileInputStream(file))) {


            StringBuilder outSb = new StringBuilder();
            // 用户基本信息相关参数
            if (paramsMap != null) {
                Set keySet = paramsMap.keySet();
                for (Object key : keySet) {
                    Object value = paramsMap.get(key);
                    outSb.append(newLine)
                            .append("--").append(boundary).append("\n")
                            .append("Content-Disposition: form-data; ")
                            .append("name=\"").append(key).append("\"")
                            .append(newLine).append(newLine)
                            .append(value);
                }
            }
            // 将用户基本信息写入到输出流中
            out.write(outSb.toString().getBytes("UTF-8"));

            // 上传文件，文件内容
            outSb.setLength(0);
            outSb.append(newLine)
                    .append("--").append(boundary).append(newLine)
                    .append("Content-Disposition: form-data; name=\"file\"; filename=\"" + file.getName() + "\"").append(newLine)
                    .append("Content-Type: application/octet-stream")
                    .append(newLine).append(newLine);// 参数头设置完以后需要两个换行，然后才是参数内容
            // 将文件参数头的数据写入到输出流中
            out.write(outSb.toString().getBytes("UTF-8"));

            // 数据输入流,用于读取文件数据
            byte[] bufferOut = new byte[2048];
            int bytes = 0;
            while ((bytes = in.read(bufferOut)) != -1) {
                out.write(bufferOut, 0, bytes);
            }
            // 最后添加换行
            out.write(newLine.getBytes());

            // 参数结束
            // 定义最后数据分隔线，即--加上BOUNDARY再加上--。
            byte[] endData = new StringBuilder().append("--").append(boundary).append("--").toString().getBytes();
            // 写上结尾标识
            out.write(endData);
            out.flush();

            String result;
            StringBuilder isb = new StringBuilder();
            Integer code = conn.getResponseCode();
            InputStream is = null;
            if (code >= 200 && code <= 299) {
                is = conn.getInputStream();
            } else {
                is = conn.getErrorStream();
            }

            // 返回结果
            try (BufferedReader br = new BufferedReader(new InputStreamReader(is, "UTF-8"))) {
                while ((result = br.readLine()) != null) {
                    isb.append(result).append("\n");
                }
                return isb.toString();
            } catch (Exception e) {
                throw e;
            }
        } catch (Exception e) {
            throw e;
        }
    }
}