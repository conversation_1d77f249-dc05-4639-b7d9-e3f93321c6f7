package com.seczone.ssp.example.v3;

import cn.hutool.http.ContentType;
import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 根据条件查询编码规范漏洞信息
 */
public class ListCodingRulesByPageExample {

    public static void main(String[] args) {

        // 请求url
        String url = Constants.CS_URL + "/cs/api/v3/codingRules/listByPage";

        // 构造请求参数
        Map<String, Object> params = new HashMap<>();
        // 语言ID（4.4获取语言ID）
        params.put("languageId", 1);
        // 漏洞名称或cwe string(526)
        params.put("name", null);
        // 漏洞级别Id 1-严重 2-高 3-中 4-低 5-建议 （4.30 获取漏洞等级列表）
        params.put("riskId", null);
        // 编码规范类别id 接口/codingRules/listType获取
        params.put("categoryId", null);
        // 必填 每页个数 默认10
        params.put("pageSize", 10);
        // 必填 当前页数 默认1
        params.put("pageCurrent", 1);

        // 发起请求
        HttpUtils.get(url, params, ContentType.FORM_URLENCODED);

    }
}
