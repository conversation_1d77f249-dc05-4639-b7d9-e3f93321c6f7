package com.seczone.ssp.example.v3;

import cn.hutool.http.ContentType;
import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 4.32 获取编码规范类别列表
 */
public class ListCodingRulesTypeExample {

    public static void main(String[] args) {

        // 请求url
        String url = Constants.CS_URL + "/cs/api/v3/codingRules/listType";

        // 构造请求参数
        Map<String, Object> params = new HashMap<>();
        // 语言ID（4.4获取语言ID）
        params.put("languageId", 1);

        // 发起请求
        HttpUtils.get(url, params, ContentType.FORM_URLENCODED);

    }
}
