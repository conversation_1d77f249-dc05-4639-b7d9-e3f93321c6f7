package com.seczone.ssp.example.v1.project;

import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 6.3	创建扫描项目
 * @since 2022/12/20 17:48
 **/
public class CreateExample {

    public static void main(String[] args) {

        // 请求url
        String url = Constants.CS_URL + "/openapi/project/create";

        // 构造请求参数
        Map<String, Object> params = new HashMap<>();
        // 源码方式创建必填
        params.put("storeId", "9f35a7f8-7655-47a7-a96c-37fe559308a5");
        // string(64) 源码方式创建必填
        params.put("projectUuid", "061c341e-9321-4a11-a5f9-62a6bc3a6b90");
        // string(256) 项目名称 必填
        params.put("projectName", "项目");
        // string(1024) 项目描述
        params.put("projectDesc", null);
        // 代码类型： 1：默认类型，需要上传代码 2：git方式获取代码，并扫描代码 3：svn方式获取代码，并扫描代码
        params.put("sourceMode", 1);

        // 当sourceMode选择2（git）/ 3（svn）的时候，该字段必须要有值
        Map<String, Object> sourceUserData = new HashMap<>();
        // 是否需要登录，默认为true
        params.put("authentication", null);
        // string(128)		用户名
        params.put("username", null);
        // string(128)		密码
        params.put("password", null);
        // string(2048)		git  token
        params.put("token", null);
        // string(2048)		地址
        params.put("url", null);
        // string(128) 分支名称
        params.put("branchName", null);

        params.put("sourceUserData", sourceUserData);

        // 发起请求
        HttpUtils.post(url, params);
    }

}