package com.seczone.ssp.example.v2.project;

import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description 4.50 重复发起扫描任务
 * @since 2022/12/14 14:11
 **/
public class ScanSubProjectRepeatExample {

    public static void main(String[] args) {

        // 项目uuid
        String projectUuid = "aa41a541-8e2f-4878-9265-b2efc7cacc77";

        // 扫描任务id (4.8 4.9 4.11 返回)
        String appId = "17778059-55ef-4ae0-be40-58a7123a4d48";

        // 请求url
        String url = Constants.CS_URL + "/cs/api/v2/project/"+projectUuid+"/task/"+appId+"/scanSubProjectRepeat";

        // 发起请求
        HttpUtils.post(url);
    }
}