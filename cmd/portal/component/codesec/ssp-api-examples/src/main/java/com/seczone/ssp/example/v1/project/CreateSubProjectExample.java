package com.seczone.ssp.example.v1.project;

import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 6.4	创建扫描任务
 * @since 2022/12/21 10:35
 **/
public class CreateSubProjectExample {

    public static void main(String[] args) {

        // 请求url
        String url = Constants.CS_URL + "/openapi/project/createSubProject";

        Map<String, Object> params = new HashMap<>();

        // 项目Uuid string(40)
        params.put("projectUuid", "061c341e-9321-4a11-a5f9-62a6bc3a6b90");
        // 扫描引擎
        params.put("engineId", null);
        // 记录类型 0：代码合规扫描 1：静态代码扫描 2：动态代码扫描
        params.put("projectType", 1);
        // 是否立刻发起子项目得扫描 0：仅创建，不扫描 1：创建，并且发起扫描
        params.put("startScanning", null);

        // 代码合规扫描配置
        Map<String, Object> scanConfigCodingRules = new HashMap<>();
        // 扫描规则
        params.put("codingRuleId", null);
        // 语言选择
        params.put("language", null);
        params.put("scanConfigCodingRules", null);

        // 静态代码扫描配置
        Map<String, Object> scanConfigVulRules = new HashMap<>();
        // 开发语言
        scanConfigVulRules.put("language", 1);
        // 文件过滤列表，例：JavaScript,Css,png|jpg
        scanConfigVulRules.put("fileIgnoreList", null);
        // 自定义规则，参考preset表
        scanConfigVulRules.put("customRules", null);
        // 自定义过滤函数，例：main,test,prInt
        scanConfigVulRules.put("customFunction", null);
        // 是否开启添加依赖：0未开启，1开启
        scanConfigVulRules.put("isOpenRely", null);
        // 仓库依赖的id，开启时添加
        scanConfigVulRules.put("depotId", null);
        params.put("scanConfigVulRules", scanConfigVulRules);

        // 动态代码扫描配置
        Map<String, Object> scanConfigDynamic = new HashMap<>();
        // 动态扫描规则
        scanConfigDynamic.put("dynamicRuleId", null);
        // 是否开启定时扫描
        scanConfigDynamic.put("isTimeScan", null);

        // 定时任务配置
        Map<String, Object> timeScanConfig = new HashMap<>();
        // 类型名称,0 每天一次  1 每周一次 2 每月一次
        timeScanConfig.put("type", null);
        // 时
        timeScanConfig.put("hour", null);
        // 天
        timeScanConfig.put("day", null);
        // 周
        timeScanConfig.put("week", null);
        scanConfigDynamic.put("timeScanConfig", null);

        params.put("scanConfigDynamic", null);

        // 发起请求
        HttpUtils.post(url, params);



    }

}