package com.seczone.ssp.example.v2.whitefun;

import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

/**
 * <AUTHOR>
 * @description 4.35 查询白名单语言列表
 * ***************** 注：管理员或企业管理员调用 *****************
 * @since 2022/12/15 17:25
 **/
public class FindLanguageListExample {

    public static void main(String[] args) {

        // 请求url
        String url = Constants.CS_URL + "/cs/api/v2/whiteFunction/findLanguageList";

        // 发起请求
        HttpUtils.get(url);
    }
}