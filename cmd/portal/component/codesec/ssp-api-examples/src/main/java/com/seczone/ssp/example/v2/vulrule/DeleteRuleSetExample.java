package com.seczone.ssp.example.v2.vulrule;

import cn.hutool.http.ContentType;
import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 4.34 删除规则集信息
 * ***************** 注：管理员或企业管理员调用 *****************
 * @since 2022/12/15 17:14
 **/
public class DeleteRuleSetExample {

    public static void main(String[] args) {

        // 请求url
        String url = Constants.CS_URL + "/cs/api/v2/vulrule/deleteRuleSet";

        // 构建请求参数
        Map<String, Object> params = new HashMap<>();
        // 必填 规则集ID(4.32查询的规则集ID)
        params.put("vulRuleId", 35);

        // 发起请求
        HttpUtils.delete(url, params, ContentType.FORM_URLENCODED);
    }


}