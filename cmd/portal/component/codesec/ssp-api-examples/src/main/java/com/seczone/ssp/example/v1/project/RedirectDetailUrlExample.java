package com.seczone.ssp.example.v1.project;

import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import com.seczone.ssp.Constants;

import java.util.Base64;

/**
 * <AUTHOR>
 * @description 6.25 项目详情跳转URL
 * @since 2022/12/21 15:59
 **/
public class RedirectDetailUrlExample {

    public static void main(String[] args) {

        // 项目UUID
        String projectUuid = "4b5c0c3f-71f1-4efd-8d30-b46a57e3739f";
        // 邮箱
        String email = "<EMAIL>";
        // 加密后密码
        String password = "123";
        // 用户自定义标识
        String extraMark = "";

        StringBuffer url = new StringBuffer(Constants.CS_URL+"/#/project/detail?");
        url.append("p_id=").append(projectUuid);
        url.append("email=").append(email);
        url.append("password=").append(encrypt(password));
        url.append("extraMark=").append(extraMark);

        System.out.println(url);
    }

    /**
     * 加密方式
     * @param data
     * @return
     */
    public static String encrypt(String data){
        // 公钥请询问相关技术支持
        String publicKey = "";
        RSA rsa = new RSA(null, publicKey);
        //RSA 公钥加密
        byte[] encrypt = rsa.encrypt(data.getBytes(), KeyType.PublicKey);
        //Base 64编码
        return Base64.getEncoder().encodeToString(encrypt);
    }

}