package com.seczone.ssp.example.v4.scanResult;

import cn.hutool.http.ContentType;
import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 4.3.6 获取漏洞分类列表详情（漏洞列表页面左侧树）
 * @since 2022/12/15 10:20
 **/
public class GetVulListExample {

    public static void main(String[] args) {

        // 项目uuid
        String projectUuid = "f144b3c3-c159-4f35-b40f-a1db2d1e9dea";

        // 扫描任务id
        String appId = "a23d453e-576a-4634-bc0b-d4bae0a05d42";
        // 扫描记录id
        String recordId = "b63446f3-b1cb-4eab-b120-2ed9705fbec5";
        // 漏洞分组id
        Integer catId = 1;
        Integer vulFlagType = 2;

        // 请求url
        String url = Constants.CS_URL + "/cs/api/v4/project/"+projectUuid+"/task/"+appId+"/getVulList";

        // 构造请求参数
        Map<String, Object> params = new HashMap<>();
        // 扫描记录Id （4.15 发起扫描任务返回）
        params.put("recordId", recordId);
        // 必填 漏洞分组id（获取漏洞分类接口获得）
        params.put("catId", catId);
//        params.put("vulFlagType", vulFlagType);

        // 发起请求
        HttpUtils.get(url, params, ContentType.FORM_URLENCODED);
    }
}
