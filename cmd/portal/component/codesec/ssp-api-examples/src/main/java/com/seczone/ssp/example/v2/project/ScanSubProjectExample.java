package com.seczone.ssp.example.v2.project;

import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

/**
 * <AUTHOR>
 * @description 4.15 发起扫描任务
 * @since 2022/12/14 14:11
 **/
public class ScanSubProjectExample {

    public static void main(String[] args) {

        // 项目uuid
        String projectUuid = "8dd3811a-1f1f-40e8-a6e4-5294217eace3";

        // 扫描任务id (4.8 4.9 4.11 返回)
        String appId = "6593f1e3-3684-44c1-b824-3c79e5944020";

        // 请求url
        String url = Constants.CS_URL + "/cs/api/v2/project/"+projectUuid+"/task/"+appId+"/scanSubProject";

        // 发起请求
        HttpUtils.post(url);

    }
}