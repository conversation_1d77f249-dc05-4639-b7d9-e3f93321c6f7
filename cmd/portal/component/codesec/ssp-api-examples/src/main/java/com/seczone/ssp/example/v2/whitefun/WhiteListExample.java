package com.seczone.ssp.example.v2.whitefun;

import cn.hutool.http.ContentType;
import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 4.38 查询白名单列表
 * ***************** 注：管理员或企业管理员调用 *****************
 * @since 2022/12/16 09:49
 **/
public class WhiteListExample {

    public static void main(String[] args) {

        // 请求url
        String url = Constants.CS_URL + "/cs/api/v2/whiteFunction/whiteList";

        // 构建请求参数
        Map<String, Object> params = new HashMap<>();
        // 白名单 name（支持模糊查询） string(20)
        params.put("name", null);
        // 语言Id （4.35 获取语言Id）
        params.put("languageId", null);
        // 当前页数（默认第1页）
        params.put("pageCurrent", 1);
        // 每页个数（默认每页10条）
        params.put("pageSize", 10);

        // 发起请求
        HttpUtils.get(url, params, ContentType.FORM_URLENCODED);
    }

}