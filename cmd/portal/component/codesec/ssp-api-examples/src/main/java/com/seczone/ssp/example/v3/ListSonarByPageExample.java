package com.seczone.ssp.example.v3;

import cn.hutool.http.ContentType;
import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 分页查询sonar漏洞列表
 * 请求参数通过/sonar/getSelectParams接口获取
 */
public class ListSonarByPageExample {

    public static void main(String[] args) {

        // 请求url
        String url = Constants.CS_URL + "/cs/api/v3/sonar/listByPage";

        // 构造请求参数
        Map<String, Object> params = new HashMap<>();
        params.put("languageId", 1);
        // 漏洞名称
        params.put("name", null);
        // 漏洞类型
        params.put("type", "Security Hotspot");
        // 漏洞级别ID
        params.put("riskId", null);
        // 发起请求
        HttpUtils.get(url, params, ContentType.FORM_URLENCODED);

    }
}
