package com.seczone.ssp;

import cn.hutool.core.util.StrUtil;
import com.seczone.ssp.utils.SHA256Util;
import com.seczone.ssp.utils.SignatureUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class Constants {

    /**
     * api地址
     */
    public static final String CS_URL = "http://localhost:28081";

    /**
     * 用户API访问KEY
     */
    // public static final String accessKey =
    // "7b214572-4f1d-4630-ba73-12731ecfdf02";
    public static final String accessKey = "fb9d6032-e3ff-4357-8c42-18a17fd855bf";
    /**
     * 签名密钥
     */
    public static final String openToken = "eyJhbGciOiJIUzUxMiJ9.****************************************************************************************************************************.oq-dRoku8CGiMWGZIEj7aIEeKRiPVdpTW717iFTI69gmFKN54d0AtMPUgdEjiZZkDp2ONCb59924WLN2kR9w7w";

    // public static final String openToken =
    // "eyJhbGciOiJIUzUxMiJ9.eyJub25jZSI6IjYzNGFjMGNiLWU3NDUtNDllMC05OTkwLThiMjUyODBjNzY0MSIsInN1YiI6IjdiMjE0NTcyLTRmMWQtNDYzMC1iYTczLTEyNzMxZWNmZGYwMiJ9.IL7hoM9GMz39scLkke3_tX5ObYVD5szMnJGU2rKDZ_ehCav_vWAp7sr5_kh5cYw1f3G_rlPGttwrDqplbNsjXw";

    // public static final String openToken =
    // "eyJhbGciOiJIUzUxMiJ9.eyJub25jZSI6IjRiODkyODQ3LWYyZmEtNDEyOS1hOWY2LTM0NDNlODVhZmFkMSIsInN1YiI6ImZiOWQ2MDMyLWUzZmYtNDM1Ny04YzQyLTE4YTE3ZmQ4NTViZiJ9.66cbIzozF7Xm0I-D2N7vk7DOoSBAxg6GFJkvbfjaRGW424_2bi9O-b9V7kmw3f1hQ3xBvLpc1vT9HkFQR9QvsQ";

    public static final String POST = "POST";
    public static final String PUT = "PUT";
    public static final String GET = "GET";
    public static final String DELETE = "DELETE";

    /**
     * V2接口构建请求头信息
     * 
     * @param url    请求地址
     * @param params 请求参数
     * @return
     */
    public static Map<String, String> buildV2Header(String url, Map<String, Object> params) {
        Map<String, String> headers = new HashMap<>();
        // 用户API访问KEY
        headers.put("accessKey", accessKey);
        // 时间戳，5分钟内数据有效
        headers.put("x-cs-timestamp", String.valueOf(System.currentTimeMillis()));
        // 随机字符串，防止重复提交
        headers.put("x-cs-nonce", UUID.randomUUID().toString());

        // 拼接请求体里的参数
        String data = SignatureUtil.mergeBody(params);
        // 拼接url中的参数
        String matchesUrl = SignatureUtil.matchesUrl(url);
        // 拼接密钥时间戳随机字符串
        String end = "&" + openToken + "&" + headers.get("x-cs-timestamp") + "&" + headers.get("x-cs-nonce");

        String sha256;
        if (StringUtils.isBlank(data)) {
            if (StrUtil.isBlank(matchesUrl)) {
                data = end;
            } else {
                data = matchesUrl.substring(1) + end;
            }
        } else {
            data = data.substring(0, data.length() - 1);
            if (StrUtil.isBlank(matchesUrl)) {
                data = data + end;
            } else {
                data = data + matchesUrl + end;
            }
        }

        System.out.println("签名数据: " + data);

        sha256 = SHA256Util.toSHA(data);

        System.out.println("签名串: " + sha256);

        // 签名串
        headers.put("x-cs-signature", sha256);

        return headers;
    }

    /**
     * 构建V1接口请求参数
     * 
     * @param url
     * @param params
     * @return
     */
    public static Map<String, String> buildV1Header(String url, Map<String, Object> params) {
        Map<String, String> headers = new HashMap<>();

        // 用户API访问KEY
        headers.put("openToken", openToken);
        // 时间戳，5分钟内数据有效
        headers.put("timestamp", String.valueOf(System.currentTimeMillis()));
        // 随机字符串，防止重复提交
        headers.put("nonce", UUID.randomUUID().toString());

        // 拼接请求体里的参数
        String data = SignatureUtil.mergeBody(params);

        System.out.println("签名数据: " + data);

        String sha256;
        if (StringUtils.isBlank(data)) {
            sha256 = SHA256Util.toSHA("");
        } else {
            sha256 = SHA256Util.toSHA(data.substring(0, data.length() - 1));
        }

        System.out.println("签名串: " + sha256);
        // 签名串
        headers.put("signature", sha256);

        return headers;
    }

}
