package com.seczone.ssp.example.v3;

import cn.hutool.http.ContentType;
import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * V3版本创建扫描方案
 */
public class CreateRuleSetV3Example {

    public static void main(String[] args) {

        // 请求url
        String url = Constants.CS_URL + "/cs/api/v3/vulrule/createRuleSet";

        // 构建请求参数
        Map<String, Object> params = new HashMap<>();
        // 必填 规则集名称 string(2-20)
        params.put("name", "v23");
        // 必填 语言ID（4.4获取语言ID）
        params.put("languageIds", "1,9");
        // 必填 漏洞id集合
        params.put("vulDataIds", "0ea0bcbd-d58a-496a-adf3-23ba8e0849d7");
        // 必填 0为不生效，1为生效
        params.put("status", 1);
        // 默认扫描方案 0否1是 默认0
        params.put("isDefault", 0);
        // 扫描方案描述
        params.put("desc", "");
        // 编码规范漏洞id，多个使用逗号拼接，该字段取值为4.33返回结果中的id-type-change,使用-拼接
        params.put("codingIdList", "478-1-2");

        // 发起请求
        HttpUtils.post(url, params, ContentType.FORM_URLENCODED);

    }
}
