package com.seczone.ssp.example.v2.project;

import cn.hutool.http.ContentType;
import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 4.3.8 根据报告导出配置生成报告
 * @date 2023/9/15 16:04
 */
public class GenerateReportByConfigExample {

    public static void main(String[] args) {
        // 项目uuid
        String projectUuid = "f8afb623-4b14-408a-9974-a94b1bd9a683";

        // 请求参数
        Map<String, Object> params = new HashMap<>();
        // 必填 项目UUID
        params.put("projectUuid", projectUuid);
        // 必填 报告导出配置id (ListAllExportConfigExample接口返回)
        params.put("exportConfigId", 1);
        // 扫描记录id，为空时生成最近一次扫描成功的报告
        params.put("recordId", "46a998c4-7da3-4e5a-95af-b359ff8377dc");

        // 请求url
        String url = Constants.CS_URL + "/cs/api/v2/project/"+projectUuid+"/generateReportByConfig";

        // 发起请求
        HttpUtils.post(url, params, ContentType.FORM_URLENCODED);
    }

}
