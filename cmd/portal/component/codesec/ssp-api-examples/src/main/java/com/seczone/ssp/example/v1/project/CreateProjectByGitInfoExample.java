package com.seczone.ssp.example.v1.project;

import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 6.15 根据GIT信息创建项目
 * @since 2022/12/21 14:26
 **/
public class CreateProjectByGitInfoExample {

    public static void main(String[] args) {

        // 请求url
        String url = Constants.CS_URL + "/openapi/project/createProjectByGitInfo";

        // 构造请求参数
        Map<String, Object> params = packageData();

        // 发起请求
        HttpUtils.post(url, params);

    }

    public static Map<String, Object> packageData() {
        Map<String, Object> params = new HashMap<>();
        // 项目名称
        params.put("projectName", null);
        // 项目描述
        params.put("projectDesc", null);
        // git地址 必填
        params.put("url", "http://11111.git");
        // git 认证类型 0-用户名密码认证 1-token认证
        params.put("authenticationMethod", 0);
        // 用户名（authenticationMethod=0时可用, authenticationMethod=1且gitType=7时可用）
        params.put("username", null);
        // 密码（authenticationMethod=0时可用）（使用RSA加密传输，补位方式为RSA/ECB/PKCS1Padding,加密完成后需要使用Base64进行编码）
        params.put("password", null);
        // token（authenticationMethod=1时可用）
        params.put("token", null);
        // branch 分支名称
        params.put("branch", null);
        // commitId 提交id
        params.put("commitId", null);
        // 自定义标识符（可作为项目名称 如有多个 使用","进行分隔传输 例如 "研发部门,研发一组"）
        params.put("extraMark", null);
        // 文件过滤或文件夹过滤 多个路径以","分隔
        params.put("fileFilter", null);
        // 回调通知地址
        params.put("callBackUrl", null);
        // 项目语言ID（不填时自动识别）
        params.put("language", null);
        // 扫描类型 1 静态扫描（默认） 2 编码规范
        params.put("type", null);
        // 是否开启依赖：0不开启（默认） 1开启
        params.put("isOpenDepend", null);
        // 仓库依赖id，开启依赖时填写
        params.put("depotId", null);
        // 自定义回调通知请求头，约定采用key,value 格式，以分号分隔，如aaa,bbb;ccc,ddd;
        params.put("callBackHeaders", "");

        return params;
    }
}