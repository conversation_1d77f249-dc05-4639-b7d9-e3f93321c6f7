package com.seczone.ssp.example.v2.vul;

import cn.hutool.http.ContentType;
import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 4.27 根据语言查询漏洞树
 * ***************** 注：管理员或企业管理员调用 *****************
 * @since 2022/12/15 11:37
 **/
public class TreeListByLanguageExample {

    public static void main(String[] args) {

        // 请求url
        String url = Constants.CS_URL + "/cs/api/v2/vul/treeListByLanguage";

        // 构造请求参数
        Map<String, Object> params = new HashMap<>();
        // 必填 语言ID（4.4获取语言ID）
        params.put("languageId", 1);
        // 漏洞名称
        params.put("name", null);

        // 发起请求
        HttpUtils.get(url, params, ContentType.FORM_URLENCODED);

    }
}