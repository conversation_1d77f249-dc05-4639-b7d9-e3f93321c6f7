package com.seczone.ssp.example.v2.project;

import cn.hutool.http.ContentType;
import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 4.20 查询漏洞详情（漏洞列表页面点击右侧表格后）
 * @since 2022/12/14 17:58
 **/
public class VulDetailExample {

    public static void main(String[] args) {

        // 项目uuid
        String projectUuid = "68aa740c-3f50-42a5-a550-d8b54270c615";

        // 扫描任务id (4.8 4.9 4.11 返回)
        String appId = "11d4d19f-56ae-486c-b307-5ec0361569a3";

        // 请求url
        String url = Constants.CS_URL + "/cs/api/v2/project/"+projectUuid+"/task/"+appId+"/vulDetail";

        // 构建请求参数
        Map<String, Object> params = new HashMap<>();
        // 必填 漏洞库id string(40)
        params.put("vulDataId", "9f8d4537-b9c1-4224-a29a-11433d525217");
        // 必填 漏洞节点id（4.19 分页查询漏洞列表 接口中的data.vulTraces[i].nodeList[i].id）
        // 该字段填错的话将不会返回源码信息
        params.put("vulNodeId", 2566439);
        // 扫描记录id string(40)
        params.put("recordId", "c38e7670-ef4e-429c-a3f4-ea9b0b86b31f");

        // 发起请求
        HttpUtils.get(url, params, ContentType.FORM_URLENCODED);

    }
}