package com.seczone.ssp.example.v2.vul;

import cn.hutool.http.ContentType;
import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 4.29 根据漏洞ID查询漏洞信息
 * ***************** 注：管理员或企业管理员调用 *****************
 * @since 2022/12/15 15:21
 **/
public class GetVulDataExample {

    public static void main(String[] args) {

        // 请求url
        String url = Constants.CS_URL + "/cs/api/v2/vul/getVulData";

        // 构建请求参数
        Map<String, Object> params = new HashMap<>();
        // 必填 漏洞id string(40)
        params.put("vulDataId", "5830e3cf-ac7e-4fea-8768-fe2f79fdfe40");

        // 发起请求
        HttpUtils.get(url, params, ContentType.FORM_URLENCODED);
    }
}