package com.seczone.ssp.example.v1.project;

import cn.hutool.http.ContentType;
import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 6.14 标记漏洞状态
 * @since 2022/12/21 12:35
 **/
public class EditVulTagExample {

    public static void main(String[] args) {

        // 请求url
        String url = Constants.CS_URL + "/openapi/project/editVulTag";

        // 构造请求参数
        Map<String, Object> params = new HashMap<>();
        // 必填 漏洞状态ID 1．待确认 2．已确认 3．可疑 4. 误报 5. 已处理
        params.put("tagId", 4);
        // 必填 漏洞vulId集合，举例：vulIds: “xxx1,xxx2,xx3” 多个vulId以英文逗号隔开
        params.put("vulIds", "d31597a1-1485-44f7-8b18-f3b8406e5e82");

        // 发起调用
        HttpUtils.post(url, params, ContentType.FORM_URLENCODED);
    }
}