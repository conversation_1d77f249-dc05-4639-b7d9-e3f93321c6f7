package com.seczone.ssp.example.v2.project;

import cn.hutool.http.ContentType;
import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 4.19 分页查询漏洞列表（漏洞列表页面右侧表格）
 * @since 2022/12/14 17:27
 **/
public class GetListDetailByVulDataIdExample {

    public static void main(String[] args) {

        // 项目uuid
        String projectUuid = "68aa740c-3f50-42a5-a550-d8b54270c615";

        // 扫描任务id (4.8 4.9 4.11 返回)
        String appId = "11d4d19f-56ae-486c-b307-5ec0361569a3";

        // 请求url
        String url = Constants.CS_URL + "/cs/api/v2/project/"+projectUuid+"/task/"+appId+"/getListDetailByVulDataId";

        // 构建请求参数
        Map<String, Object> params = new HashMap<>();
        // 必填 漏洞库id
        params.put("vulDataId", "9f8d4537-b9c1-4224-a29a-11433d525217");
        // 扫描记录id（发起扫描时会返回）
        params.put("recordId", "c38e7670-ef4e-429c-a3f4-ea9b0b86b31f");
        // 当前页 默认1
        params.put("pageCurrent", 1);
        // 一页查多少条 默认10
        params.put("pageSize", 10);

        // 发起请求
        HttpUtils.get(url, params, ContentType.FORM_URLENCODED);

    }
}