package com.seczone.ssp.example.v3;

import cn.hutool.http.ContentType;
import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 获取缺陷数据统计分类
 */
public class GetCountVulCategoryExample {

    public static void main(String[] args) {

        // 项目uuid
        String projectUuid = "6a83e460-83d0-4d6f-b0f2-f79299dd9828";

        // 请求url
        String url = Constants.CS_URL + "/cs/api/v3/project/"+projectUuid+"/getCountVulCategory";

        // 构造请求参数
        Map<String, Object> params = new HashMap<>();
        // 扫描记录Id （4.15 发起扫描任务返回）
        params.put("recordId", "89ccd3fd-e4a4-42db-96a2-9957b0679c86");

        // 发起请求
        HttpUtils.get(url, params, ContentType.FORM_URLENCODED);

    }
}
