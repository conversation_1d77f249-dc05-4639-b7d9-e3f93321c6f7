package com.seczone.ssp.example.v1.project;

import cn.hutool.http.ContentType;
import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 6.8	加载项目列表（分页）
 * @since 2022/12/21 11:28
 **/
public class ListProjectExample {

    public static void main(String[] args) {
        // 请求url
        String url = Constants.CS_URL + "/openapi/project/list";

        // 请求参数
        Map<String, Object> params = new HashMap<>();
        // 当前页，默认为1
        params.put("pageCurrent", 1);
        // 默认10
        params.put("pageSize", 10);

        // 发起请求
        HttpUtils.get(url, params, ContentType.FORM_URLENCODED);
    }
}