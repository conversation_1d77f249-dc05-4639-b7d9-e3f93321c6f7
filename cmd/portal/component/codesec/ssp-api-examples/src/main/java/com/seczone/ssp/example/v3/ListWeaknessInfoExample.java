package com.seczone.ssp.example.v3;

import cn.hutool.http.ContentType;
import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 获取缺陷数据统计环形图数据
 */
public class ListWeaknessInfoExample {

    public static void main(String[] args) {

        String projectUuid = "6a83e460-83d0-4d6f-b0f2-f79299dd9828";

        // 请求url
        String url = Constants.CS_URL + "/cs/api/v3/project/"+projectUuid+"/listWeaknessInfo";

        // 构造请求参数
        Map<String, Object> params = new HashMap<>();

        // 扫描任务id 必填
        params.put("appId", "d2c83031-d145-41b8-b4bf-d69a1be0d585");
        // 扫描记录id 必填
        params.put("recordId", "89ccd3fd-e4a4-42db-96a2-9957b0679c86");
        // 分类id 必填 通过接口getCountVulCategory获取
        params.put("catId", 4);

        // 发起请求
        HttpUtils.get(url, params, ContentType.FORM_URLENCODED);

    }
}