package com.seczone.ssp.example.v1.project;

import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 6.18 编辑SVN项目
 * @since 2022/12/21 14:49
 **/
public class EditSvnProjectExample {

    public static void main(String[] args) {

        // 请求url
        String url = Constants.CS_URL + "/openapi/project/editSvnProject";

        // 构造请求参数
        Map<String, Object> params = packageData();

        // 发起请求
        HttpUtils.post(url, params);

    }

    public static Map<String, Object> packageData() {
        Map<String, Object> params = new HashMap<>();
        // 必填 项目ID string(40)
        params.put("projectUuid", "819c7268-dc66-46e8-a1d4-abbc5c597704");
        // 项目名称 string(512)
        params.put("projectName", null);
        // 项目描述 string(500)
        params.put("projectDesc", null);
        // svn地址 string(200)
        params.put("url", "https://*************:800/svn/svnseczone/ceshi/WebGoat5.0min/WebGoat5.0");
        // 用户名 string(200)
        params.put("username", "");
        // 密码（使用RSA加密传输，补位方式为RSA/ECB/PKCS1Padding,加密完成后需要使用Base64进行编码） string(200)
        params.put("password", "");
        // svn版本库ID long
        params.put("versionId", null);
        // 自定义标识符（可作为项目名称 如有多个 使用","进行分隔传输 例如 "研发部门,研发一组"） string(512)
        params.put("extraMark", null);
        // 文件过滤或文件夹过滤 多个路径以","分隔 text
        params.put("fileFilter", "jsp");
        // 回调通知地址 string(100)
        params.put("callBackUrl", null);
        // 项目语言ID（不填时自动识别）
        params.put("language", 1);
        // 扫描类型 1 静态扫描（默认） 2 编码规范
        params.put("type", 1);
        // 是否开启依赖：0不开启（默认） 1开启
        params.put("isOpenDepend", 0);
        // 仓库依赖id，开启依赖时填写
        params.put("depotId", null);
        // 拉取指定文件/文件夹名称 string(100)
        params.put("pullFileName", null);
        // 自定义回调通知请求头，约定采用key,value 格式，以分号分隔，如aaa,bbb;ccc,ddd;
        params.put("callBackHeaders", null);

        return params;
    }
}