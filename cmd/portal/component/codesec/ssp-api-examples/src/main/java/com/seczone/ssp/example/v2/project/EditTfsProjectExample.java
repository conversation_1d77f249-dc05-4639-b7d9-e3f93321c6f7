package com.seczone.ssp.example.v2.project;

import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 4.11 编辑GIT项目
 * @since 2022/12/14 10:25
 **/
public class EditTfsProjectExample {

    public static void main(String[] args) {

        String projectUuid = "d5036d51-**************-74162a59c1ae";

        // 请求url
        String url = Constants.CS_URL + "/cs/api/v2/project/"+projectUuid+"/editTfsProject";

        // 构造请求参数
        Map<String, Object> params = packageData();

        // 发起请求
        HttpUtils.put(url, params);

    }

    public static Map<String, Object> packageData() {
        Map<String, Object> params = new HashMap<>();
        // 项目名称 string(512)
        params.put("projectName", null);
        // 项目描述 string(500)
        params.put("projectDesc", null);
        // tfs地址 string(200)
        params.put("url", "https://<EMAIL>/2292512655/TFS%E6%B5%8B%E8%AF%95demo/_git/test1");
        // 必填 tfs 认证类型 0.用户名密码认证 1.	token认证
        params.put("authenticationMethod", 0);
        // 用户名（authenticationMethod=0时可用） string(200)
        params.put("username", null);
        // 密码（authenticationMethod=0时可用）（使用RSA加密传输，补位方式为RSA/ECB/PKCS1Padding,加密完成后需要使用Base64进行编码） string(200)
        params.put("password", "");
        // token（authenticationMethod=1时可用） string(128)
        params.put("token", "");
        // branch 分支名称 string(128)
        params.put("branch", null);
        // 自定义标识符（可作为项目名称 如有多个 使用","进行分隔传输 例如 "研发部门,研发一组"） string(512)
        params.put("extraMark", null);
        // 文件过滤或文件夹过滤 多个路径以","分隔 text
        params.put("fileFilter", null);
        // 回调通知地址 string(100)
        params.put("callBackUrl", null);
        // 项目语言ID（不填时自动识别）
        params.put("language", 1);
        // 扫描类型 1 静态扫描（默认） 2 编码规范
        params.put("type", 1);
        // 是否开启依赖：0不开启（默认） 1开启
        params.put("isOpenDepend", 0);
        // 仓库依赖id，开启依赖时填写
        params.put("depotId", null);
        // 规则集ID（4.41接口获取）
        params.put("ruleSetId", null);
        // 增量扫描
        params.put("isIncrScan", 1);

        return params;
    }
}
