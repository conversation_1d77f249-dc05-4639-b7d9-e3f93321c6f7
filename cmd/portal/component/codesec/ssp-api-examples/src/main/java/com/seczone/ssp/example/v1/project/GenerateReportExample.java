package com.seczone.ssp.example.v1.project;

import cn.hutool.http.ContentType;
import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 6.11 生成报告
 * @since 2022/12/21 11:35
 **/
public class GenerateReportExample {

    public static void main(String[] args) {
        // 请求url
        String url = Constants.CS_URL + "/openapi/project/generateReport";

        // 请求参数
        Map<String, Object> params = new HashMap<>();
        // 必填 项目UUID
        params.put("projectUuid", "4b5c0c3f-71f1-4efd-8d30-b46a57e3739f");
        // 报告类型 1．word 2．pdf（默认） 3．xml 4．excel
        params.put("reportFileType", 2);
        // 漏洞类型 1. OWASP TOP10 2. PCI 3. SANS TOP25 4. 严重等级（默认） 5. CODESEC
        params.put("vulType", 4);
        // 漏洞等级，默认[1,2,3,4,5]
        params.put("riskIdList", null);
        // 报告内容  2. 静态（默认） 3. 动态
        params.put("sectionIdList", null);
        // 漏洞状态，默认[1,2,3,4] 1. 待确认 2. 已确认 3. 可疑 4. 误报
        params.put("vulTagIdList", null);
        // 【reportFileType为3时（xml），当前参数才生效】xml中的描述及修复建议是否加密： true加密（默认） false不加密
        params.put("isEncryption", null);
        // 是否显示节点信息 0:不显示 1:显示
        params.put("reportType", null);
        // 是否显示代码片段 0:不显示（默认） 1:显示
        params.put("codeCont", null);
        // 是否显示与上一次扫描结果对比 0:不显示（默认） 1:显示
        params.put("contrastCont", null);
        // 内容模块：1：漏洞概览；2：漏洞明细；3：漏洞建议（可组合）
        params.put("contentModule", null);

        // 发起请求
        HttpUtils.post(url, params, ContentType.FORM_URLENCODED);
    }
}