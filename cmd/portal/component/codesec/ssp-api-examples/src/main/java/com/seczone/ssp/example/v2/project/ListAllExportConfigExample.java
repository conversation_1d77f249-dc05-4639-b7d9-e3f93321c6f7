package com.seczone.ssp.example.v2.project;

import cn.hutool.http.ContentType;
import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

/**
 * <AUTHOR>
 * @description 4.3.7 获取报告导出配置列表
 * @date 2023/9/15 15:00
 */
public class ListAllExportConfigExample {

    public static void main(String[] args) {

        // 请求url
        String url = Constants.CS_URL + "/cs/api/v2/config/listAllExportConfig";

        // 发起请求
        HttpUtils.get(url, null, ContentType.FORM_URLENCODED);
    }

}
