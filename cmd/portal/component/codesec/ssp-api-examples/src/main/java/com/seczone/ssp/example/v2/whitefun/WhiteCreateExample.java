package com.seczone.ssp.example.v2.whitefun;

import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 4.37 创建白名单
 * ***************** 注：管理员或企业管理员调用 *****************
 * @since 2022/12/15 17:37
 **/
public class WhiteCreateExample {

    public static void main(String[] args) {

        // 请求url
        String url = Constants.CS_URL + "/cs/api/v2/whiteFunction/whiteCreate";

        // 构建请求参数
        Map<String, Object> params = new HashMap<>();
        // 必填 语言ID（4.35获取语言ID）
        params.put("languageId", 1);
        // 必填 是否全选：0全选漏洞 1 不全选
        params.put("selectType", 1);
        // 必填 白名单名称 String（100）
        params.put("name", "fff");
        // 必填 是否生效 0不生效 1生效
        params.put("status", 0);
        // 必填 方法名 String(200)
        params.put("funName", "1233");
        // 漏洞ID集合（当selectType为1时必填,4.36获取对应语言支持漏洞ID）
        params.put("vulDataIds", "8d9f35de-987b-4a16-a2a2-7d57902ec727, b54b3a23-1b65-47c9-a499-3829a305ea06");
        // 类名 String(200)
        params.put("className", null);
        // 包名 String(1000)
        params.put("packageName", null);
        // 返回值类型（return|object） string(6)
        params.put("outType", "object");
        // 创建人名称 string(100)
        params.put("createBy", null);
        // 描述 string(400)
        params.put("description", null);


        // 发起请求
        HttpUtils.post(url, params);
    }

}