package com.seczone.ssp.example.v2;

import cn.hutool.http.ContentType;
import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 4.4	加载开发语言列表
 * @since 2022/12/13 14:10
 */
public class CodeLanguageListExample {

    public static void main(String[] args) {

        // 请求url
        String url = Constants.CS_URL + "/cs/api/v2/codeLanguage/list";

        // 请求参数
        Map<String, Object> params = new HashMap<>();
        // 扫描类型 默认1 (0 代码合规扫描，1 静态代码扫描)
        params.put("type", 0);

        // 发起请求
        HttpUtils.request(url, params, Constants.GET, ContentType.FORM_URLENCODED);
    }
}
