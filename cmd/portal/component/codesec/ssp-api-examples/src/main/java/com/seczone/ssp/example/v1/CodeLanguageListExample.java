package com.seczone.ssp.example.v1;

import cn.hutool.http.ContentType;
import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 6.1	加载开发语言列表
 * @since 2022/12/20 16:44
 **/
public class CodeLanguageListExample {

    public static void main(String[] args) {
        // 请求url
        String url = Constants.CS_URL + "/openapi/codeLanguage/list";

        // 请求参数
        Map<String, Object> params = new HashMap<>();
        // 扫描类型 默认1 (0 代码合规扫描，1 静态代码扫描)
        params.put("type", 0);

        // 发起请求
        HttpUtils.get(url, params, ContentType.FORM_URLENCODED);
    }
}