package com.seczone.ssp.example.v2;

import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 4.1	创建账号
 * @since 2022/12/13 10:07
 */
public class CreateAccountExample {

    public static void main(String[] args) {

        // 请求url
        String url = Constants.CS_URL + "/cs/api/v2/user/createAccount";

        // 构建请求参数
        Map<String, Object> params = new HashMap<>();
        // 用户名 必填
        params.put("userName", null);
        // 邮箱 必填
        params.put("userEmail", "<EMAIL>");
        // 密码（请使用sha256加密传输） 必填
        params.put("password", "123456");
        // 手机号 非必填
        params.put("phone", null);
        // 秘钥过期时间 非必填
        params.put("keyExpiredDate", null);

        // 发起请求
        HttpUtils.request(url, params, Constants.POST, null);
    }
}
