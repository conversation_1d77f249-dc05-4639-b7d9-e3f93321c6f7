package com.seczone.ssp.example.v1.project;

import cn.hutool.http.ContentType;
import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 6.21 查询单个项目概览结果
 * @since 2022/12/21 15:07
 **/
public class GetProjectScanOverViewExample {

    public static void main(String[] args) {
        // 请求url
        String url = Constants.CS_URL + "/openapi/project/getProjectScanOverView";

        // 请求参数
        Map<String, Object> params = new HashMap<>();
        // 必填 扫描任务ID
        params.put("appId", "8b4f451a-e7f7-4819-8ce9-11a261fef9d3");
        // 必填 扫描记录ID
        params.put("recordId", "d6b7042d-bbdf-4bc0-ae62-6afe7ebe2898");
        // 等级ID  1 严重 2高危 3中危 4 低危 5 建议
        params.put("riskId", null);
        // 漏洞名称
        params.put("typeName", null);

        // 发起请求
        HttpUtils.get(url, params, ContentType.FORM_URLENCODED);
    }
}