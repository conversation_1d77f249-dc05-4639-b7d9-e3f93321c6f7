package com.seczone.ssp.example.v2.project;

import cn.hutool.http.ContentType;
import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

/**
 * <AUTHOR>
 * @description 4.5 获取仓库依赖信息
 * @since 2022/12/13 14:15
 */
public class GetDependInfoExample {

    public static void main(String[] args) {

        // 请求url
        String url = Constants.CS_URL + "/cs/api/v2/project/getDependInfo";

        // 发起请求
        HttpUtils.request(url, null, Constants.GET, ContentType.FORM_URLENCODED);

    }
}
