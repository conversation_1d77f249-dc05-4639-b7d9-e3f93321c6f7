package com.seczone.ssp.example.v2.vul;

import cn.hutool.http.ContentType;
import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 4.21 获取漏洞分类
 * @since 2022/12/14 19:19
 **/
public class GetCategoryListExample {

    public static void main(String[] args) {

        // 请求url
        String url = Constants.CS_URL + "/cs/api/v2/vul/getCategoryList";

        // 构建请求参数
        Map<String, Object> params = new HashMap<>();
        // 扫描记录id（发起扫描时会返回）
        params.put("recordId", "c38e7670-ef4e-429c-a3f4-ea9b0b86b31f");

        // 发起请求
        HttpUtils.get(url, params, ContentType.FORM_URLENCODED);
    }

}