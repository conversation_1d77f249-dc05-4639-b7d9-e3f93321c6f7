package com.seczone.ssp.utils;

import com.seczone.ssp.Constants;

import java.io.File;
import java.util.*;
import java.util.regex.Pattern;

/**
 * 签名工具类
 */
public class SignatureUtil {

    public static final List<String> antFilterPathList = Arrays.asList("/cs/api/v2", "/cs/api/v3" , "/cs/api/v4");

    public static Map<String, String> urlPath = new LinkedHashMap<String, String>() {{
        put("/user/.+/.+", "2");
        put("/project/.+/task/.+/.+", "2,4");
        put("/project/.+/.+", "2");
        put("/dashboard/.+/.+", "4");
    }};

    /**
     * 拼接请求体里的参数
     * 所有非空字段
     * 按key的字典序排序
     * 所有的key=value用“&”符号拼接
     * @param params
     * @return
     */
    public static String mergeBody(Map<String, Object> params) {
        if(params == null) {
            return null;
        }
        List<String> list = new ArrayList<>();
        // 获取参数中所有的key值
        for(String key : params.keySet()) {
            Object value = params.get(key);
            if(value instanceof File) {
                continue;
            }
            if (value != null && value.toString().length() > 0) {
                list.add(key);
            }
        }
        // 排序
        Collections.sort(list);
        // 循环拼接
        StringBuffer buffer = new StringBuffer();
        for(String name : list) {
            buffer.append(name);
            buffer.append("=");
            buffer.append(parseMapToString(params.get(name)));
            buffer.append("&");
        }
        return buffer.toString();
    }

    /**
     * 拼接url中的参数
     * 按照path中的顺序将所有的value进行拼接
     * @param url
     * @return
     */
    public static String matchesUrl(String url) {
        for (String antFilterPath : antFilterPathList) {
            if (url.indexOf(antFilterPath) == -1) {
                continue;
            }
            StringBuilder result;
            url = url.replace(Constants.CS_URL + antFilterPath, "");
            for (Map.Entry<String, String> entry : urlPath.entrySet()) {
                boolean matches = Pattern.matches(entry.getKey(), url);
                if (matches) {
                    result = new StringBuilder();
                    String[] split = entry.getValue().split(",");
                    for (String s : split) {
                        String[] urlSplit = url.split("/");
                        result.append("&").append(urlSplit[Integer.parseInt(s)]);
                    }
                    return result.toString();
                }
            }
        }
        return null;
    }

    /**
     * 参数为Map的情况
     * @param objectMap
     * @return
     */
    public static String parseMapToString(Object objectMap){
        StringBuffer buffer = new StringBuffer();
        if(objectMap instanceof Map){
            List<String> list  = new ArrayList<>();
            Map<String, Object> map=(Map<String, Object>)objectMap;
            for (String MapKey : map.keySet()) {
                Object value = map.get(MapKey);
                if(value!=null&&value.toString().length()>0){
                    list.add(MapKey);
                }
            }
            Collections.sort(list);
            for(int i=0;i<list.size();i++){
                String name = list.get(i);
                buffer.append(name);
                buffer.append("=");
                buffer.append(parseMapToString(map.get(name)));
                buffer.append("&");
            }
        } else if(objectMap instanceof List) {
            String str = objectMap.toString();
            return str.replace("[","").replace("]","");
        } else {
            return objectMap.toString();
        }
        String s = buffer.toString();
        if(s==null || s.length()==0){
            return s;
        }
        return  s.substring(0,s.length()-1);
    }

}
