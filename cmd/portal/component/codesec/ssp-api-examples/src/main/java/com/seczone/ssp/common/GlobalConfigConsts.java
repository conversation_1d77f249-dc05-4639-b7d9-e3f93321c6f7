package com.seczone.ssp.common;

public class GlobalConfigConsts {
    public static boolean RANDOM_CODE_CONFIG = true;
    public static boolean SUPERVISE_IS_ENABLE = true;

    //公钥和私钥
    public static String publicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDL4jPRaLMN2qpkaoy8W/iw7RvV\n" +
            "gQlTZHC6ZdJ8VUHjWuYa1dHMM/G44KBlYI9hJAOCIP1phI4cYODoTFgdDudePeoE\n" +
            "0gaMMgGCrgt55m/GEFbm1n5dSnqtOteoHPVuSApqVESpBCn1EQbqHHm9IhklzOnV\n" +
            "FukkBF+umXrSFDqwjwIDAQAB";

    public static String privateKey = "MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBAMviM9Fosw3aqmRq\n" +
            "jLxb+LDtG9WBCVNkcLpl0nxVQeNa5hrV0cwz8bjgoGVgj2EkA4Ig/WmEjhxg4OhM\n" +
            "WB0O51496gTSBowyAYKuC3nmb8YQVubWfl1Keq0616gc9W5ICmpURKkEKfURBuoc\n" +
            "eb0iGSXM6dUW6SQEX66ZetIUOrCPAgMBAAECgYB1QjeDLLAuO6Db99vRSSQSvYvQ\n" +
            "k1IFwDKFQaJ5F5+5XsIQlQEAbK+NIMOfCpXrzIaoBMk6Mp54+87eS2ox7CxsI8v2\n" +
            "u6r7R9M2MYc1x2NEENvxJISIMfTj/MlDatW5hCujrx6k6NMFWP62Py+sVgUX9BRx\n" +
            "k8nckykIdVLmIgU1QQJBAPTWp8UE69Ku/ZK9Qejnot5vbwDbLY3dNkPSYaEpIoS1\n" +
            "8L7HMbEet0kqaq6KLBukfhzKbFiiiIf9JX1mOMry6FcCQQDVLZcVTtgFBw/Yrl6T\n" +
            "7KJPpxE/JRjRTzV6zU8Ka4XOpqhm762pw2VBTMlW6W4MjwV2Y4+zMfwWOVMxdziC\n" +
            "VjaJAkEAznPKmClkKQ2BqKCJ2Tipb2MLeO9YZE1qGppl1J15C+rXDCevUSkTaFCq\n" +
            "EG9WbiCwbtqJrduvwOvHFwyBSzC0lQJBALfP2K8Bzdf/79T4/Qn1hzrBJmjZFt4b\n" +
            "u1RIxTeJ4NV/9ELVtVtk5OD6Ub0EB+UCSUZ8sIJlviR3h1lmoC4aImkCQQDIaLF1\n" +
            "dUxKbA6FGmjGVZYcaPdHt2PX88nyLteBCSaPtXR6pNrw3+hmYqBzAK6Da2O98SX0\n" +
            "uwvEz8RyBRseLldt";


    public static String upPublicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEArj449zYBKOGOp8NW6t5o\n" +
            "nWL1e18MM3GvBo/kvpW1b0ctZgxvEHw1LFQg4Dpbxaqb/bwWRywM7gfprG2uS22r\n" +
            "TM7L+bzrCJTVURABzB6LOZpgUH8WgLRutw+smBPbi6XPcKswnDpvQoDOvBiSpQEi\n" +
            "hFezC8mhhF0yB6HsQBB7IIyagK8kmKdOT8Qkl+dgV2SIshc9JvbUbujG3Xc597oI\n" +
            "lX4Hh2qdrw3ZiVjsZYGFhDFKOCDh1ltiw+6c3oJTKLsbKavSigi5b9TQyb/MPkrG\n" +
            "aljaR3SNe7AbwzqHrFrt74XakqK5edztbwkWfCmGdORBzwsX5SThmI2jx4HErm2o\n" +
            "1QIDAQAB";

    public static String upPrivateKey = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCuPjj3NgEo4Y6n\n" +
            "w1bq3midYvV7Xwwzca8Gj+S+lbVvRy1mDG8QfDUsVCDgOlvFqpv9vBZHLAzuB+ms\n" +
            "ba5LbatMzsv5vOsIlNVREAHMHos5mmBQfxaAtG63D6yYE9uLpc9wqzCcOm9CgM68\n" +
            "GJKlASKEV7MLyaGEXTIHoexAEHsgjJqArySYp05PxCSX52BXZIiyFz0m9tRu6Mbd\n" +
            "dzn3ugiVfgeHap2vDdmJWOxlgYWEMUo4IOHWW2LD7pzeglMouxspq9KKCLlv1NDJ\n" +
            "v8w+SsZqWNpHdI17sBvDOoesWu3vhdqSorl53O1vCRZ8KYZ05EHPCxflJOGYjaPH\n" +
            "gcSubajVAgMBAAECggEAHllaJLz0UMGQzmm4vuoEQL2W0mlIv8EiYjs39XYwBvTX\n" +
            "1Hl/8tgfjtwMaeHgUDqaNwTGwJjMm70nF1B3Vk4A0z0FzDb6nSEHUro+BeagC2V3\n" +
            "Ny4bpklHK32Aa3CyAc3cptw6eEu5S1USLTL+f90FvRVlaz0wc9SjvS6X7sSwHYf2\n" +
            "PGE7OhoH2usI0iKpE9sOG/rW3QfF2TMhrMxPCumhkgJwd7bHuMQionpj+i3WnT86\n" +
            "y4MCwzNmLJXY5pnd6lJWYO/bu9+Yp67nRsfBC6Fj31cgK9YYyAw//Og3Yp5R+xDh\n" +
            "hzsPUVI3lyNg9tbrHEJcEHlAgRxDvgLAqfQDRkOw1QKBgQDhDKcGbvgTMNQyvK7H\n" +
            "PymJO9VVhbR3YFVs53Bt+usPRTZES5XRgMC4lWymg9FpKSSivlnhStCdeWnPLfUw\n" +
            "B0w12Amy/wyD9vwytptiCHueAi2mofJ1AyqauOvHWzXFpdMjcjWUbebiSyuocaPz\n" +
            "/zHtyfgd1/YpHjEcPAnn6vWzEwKBgQDGNNKvGUMuLox3UDuCOvdh5ELfZvsG8HUC\n" +
            "0A8CMA+HganeUleOuDpwN5sfiq0+qp9kBa3VUwA7owOGhRf9yTk8Tj3WtQzWa/Xz\n" +
            "0aIkeifjhLIKpsfOXrUuqenTKLduz5xR+uS2EjqDleey7koNjjiFm90bxeS3Tjy2\n" +
            "n/kxDx1JdwKBgE1j9Xgf5w1qaD5+Zjg8f0MjIUzEqve2WAoYWLBpbjYVHgFEYOPn\n" +
            "u959/BwTDot0S5XvkacI/E569yj46+01RT7q6QL19E7ZO4cRBsQimKgQpbQ80szs\n" +
            "ZtVILESlGAu856uz1bsSRCijowLM+Y2pv3i/UKSqpIslR8wm1V08jnujAoGADHgo\n" +
            "RQHG2zHsflka1U+WXp84octdwYsBIYMrXozdfT3oDOeLQJxQeNzWsn5L9eRQEL/W\n" +
            "ttP48MeJoj3fS4UMR8H4W/iZ7Sdvmr42gCr7NXIUrVzetxn4Ng5UeodWOu9uuy2Y\n" +
            "k+fDrKGSqQwato2a+rTajTO+9tVCNo8L4007H3MCgYEArnj0KrrQTApiNcr8YZaS\n" +
            "WzJHQvIIJn1IVNAC8i9KJw7fBaP0+pCMfgSj786zFUM3K+gBpugi/EXJrO2CLzfG\n" +
            "Yw0IEtN84vsQUZAZnG0qhjnc6JgPo14+3rM071pWTqESxpzvCYluy66BueHJftPD\n" +
            "mxBRt+Gj2QzMZJ3i8STRDRw=";




    public static String SSO_PUBLIC_KEY = "-----BEGIN CERTIFICATE-----\n" +
            "MIIDYTCCAkmgAwIBAgIEJ7dYJTANBgkqhkiG9w0BAQsFADBhMQswCQYDVQQGEwJj\n" +
            "bjEOMAwGA1UECBMFYW5odWkxDjAMBgNVBAcTBWhlZmVpMQ0wCwYDVQQKEwRreXdh\n" +
            "MQ0wCwYDVQQLEwRreXdhMRQwEgYDVQQDEwt4aWVzaGFvcGluZzAeFw0yMTAzMjMw\n" +
            "NDAwNDRaFw0yMTA2MjEwNDAwNDRaMGExCzAJBgNVBAYTAmNuMQ4wDAYDVQQIEwVh\n" +
            "bmh1aTEOMAwGA1UEBxMFaGVmZWkxDTALBgNVBAoTBGt5d2ExDTALBgNVBAsTBGt5\n" +
            "d2ExFDASBgNVBAMTC3hpZXNoYW9waW5nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8A\n" +
            "MIIBCgKCAQEAk9iq+x5KBzz5kOIVS3p2uL4tWJeJr5cPh5lh7NU3Pox3ovhafOyw\n" +
            "wSxXy3OdxRb3MRUH8JhnPGU1UUTEKNilJpPdeKat6HvFOTPcbUJriT0QdLdfe/Uh\n" +
            "omHLuVfygS5+LWo7tgq/mZvHWGSYvpU9iCVLtVoy5+ZH6E7+BVQ+9ye5svYW7tya\n" +
            "2PFbI+Unnd2Qqg3RypKK3SvwAFCPSZ6miASJ2xc0Gjqa39EVnz+jWPpbcmRS/31N\n" +
            "kJgCTcJ5eu/H4UHQAsVtEaM7C5dYdMSColBtTYwF9NtDi6A4y6UjNTz+m59jM3Ft\n" +
            "6vTxdXKI/waXZv4UVFOzvMzhjBErZpaj8wIDAQABoyEwHzAdBgNVHQ4EFgQUwuUg\n" +
            "NPAeI/p/32TZ2aUd9yqGxwYwDQYJKoZIhvcNAQELBQADggEBAG0Kwfze/wdce9bo\n" +
            "qeexbP95v9PF2NvfrU20MiOzW0BVggmgC8bwY0wCHPsROTucCwRuBeFkmXy92P1e\n" +
            "bdt4C6Z/TuS6EhDJjNYNe301LQCWkjBBhJeQBhnHMNzkzY4tl504iDXuwwsPdgzP\n" +
            "Lvbv9UykcHJSpzEobrTHRVM0+3vi7XvXsEvb0GormKMYfuVYV89gDgPJVa5iZM9+\n" +
            "Jje8GrmpX1jsUyAHEPGtMGoGxsScozRsm8m6EtxOl4wWwNN8P7nAwgPtA6YVzlX9\n" +
            "PqPAaPQb2DHWGn342qKWoFn27StdO+AdX6GQU6aj3rwtmPK1Aaa54whdy++XhSvT\n" +
            "VleiWV8=\n" +
            "-----END CERTIFICATE-----";


    //公钥和私钥
    public static String publicKeyCont = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDuDR7XtIuzpftaRR7y13vAbRBSgfMkfTTsH4Ma9OCO15/X4t0Scni1dZ7ypKo4ssqSxP8j/gKkr8Bf4R+GqhdAF0izcdaP1f/c88EF4/d0g97UOTUwWd/NYSJxXDAVkOtURgOrCJwQDn51cRnH2+TOKKVUsrYPvr4yOEGk231QvQIDAQAB";

    public static String privateKeyCont = "MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAO4NHte0i7Ol+1pFHvLXe8BtEFKB8yR9NOwfgxr04I7Xn9fi3RJyeLV1nvKkqjiyypLE/yP+AqSvwF/hH4aqF0AXSLNx1o/V/9zzwQXj93SD3tQ5NTBZ381hInFcMBWQ61RGA6sInBAOfnVxGcfb5M4opVSytg++vjI4QaTbfVC9AgMBAAECgYAYMwzA3Fl8ToIGC/NIuAecSOoJHS1Atxq3az+qcJzYYIqfCXC0VJ85QScCnyMvxtspfu259LNDlxs9E/deuvFIcDRRUeRIIYD5H1LiMw7BVYnKNae+28dUKjydYjWtbTLkInM3mXiE5Y/w6XAifR660pBe97LrpZiQQIRT9DXP6QJBAPiGg4AKrLUO7dOCZ16ri62gQYqO2WrJhwiZD4N9QcCt5gYITgp6aK7RA7jbq4R2yZCx5yX4mPqXUoNRue6wZAkCQQD1NfZWMsBVQ1UnQcxMGhrMRKxQab37AFYFWp+X5EQgTnC5kNOnJCqJMV6Rupo2qeWI1Sg1VgnDHarNK8Q1uzwVAkEA1tcBqpiPwF1NfsCGN7K4JroYrnzaupPIZchbPuHzC2vJI4HeZPR4tlb9jrpnRTWi/bCtD4geRtK1g8wMnahicQJAPU41fU2I+ogwBPWVXz1zj7hnoF8fiV6HIyWKGw6N3cnesvY7hGEtZUl7jFGr9pi5c2FfgR42qJqFNnCHyKs8FQJBALJTSv8VuKIVRRtD+wWSvNoYNgaFU+0V7IKYzpMEUeXycU5Zl+wcrSmNxBzvUj86s0R42LJAFt+ps5fcHfwJVW0=";

    //v2接口公钥和私钥
    public static String v2PublicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDJkBK4+Mc6xRAWrSFL8NUoPwKZZ6aKRn/JbSlpYmvOIwva0MUvSVi3WBB/VGaFKWlz1ovv8SuWn8LFLVoSHFE8DDdyrFTuuRWS5hOtO2bvUgKpjiF+JUu1h92kNL04W8Sk7P2GGRk92FHksiY2rKP4teI3Uisu5oEyIXvPWHqmPwIDAQAB";

    public static String v2privateKey = "MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAMmQErj4xzrFEBatIUvw1Sg/AplnpopGf8ltKWlia84jC9rQxS9JWLdYEH9UZoUpaXPWi+/xK5afwsUtWhIcUTwMN3KsVO65FZLmE607Zu9SAqmOIX4lS7WH3aQ0vThbxKTs/YYZGT3YUeSyJjaso/i14jdSKy7mgTIhe89YeqY/AgMBAAECgYAA9aahKs4IAZgqA3j9G5BoOKSShmPMlLlXBCYGUmmg+Zm82PGXYAoEFW8+q/AxAhl2a7cO1V8XwHr05Vpga7IXTOySjLhp8nCbGdfmIQ4eZJGW8zH0DRIRfEIj3hYUUl47YoLFeBEzdrqw/YzbCsljWrNz0EZ8eOAccmvIsZdgQQJBAPNn5Rsssv+NxkUYed8aXUlHbWF8wDW+cn2Wkvq6+pb9oZQrqafekvMNQD6DFY3iUFaMds+6qcYNa92iYQSOwPsCQQDT/e73wE0TVU3muZUKC2HhXHNTSXaIEbqRJiS7sX8K4xz+b2qGA0HtWJCPqTUbHxSbZBlZKvvN7CAVtg4FGFSNAkEAmdHmby3oUKd/zNqS6fL3UP/al+kGbvBT2hn1+I2BHzpZihGtvXBi3UZaeybL0EZdDtTloJEOCSMiL4YWWZL9UQJAAc09s7NZtFlcQSLApoY0PcMYY9PTAxMLf+JQG5xFlSUbAPqXcC4k6EyBZgctIV7hQPTX1k8sNrLUDvsuIVL0nQJBAIYKldPFcnJbY9zGnn7Lit5///DM89kHqKLjm+y9C/k4ZlXV7+juGZ3K1S7gm7HJHPuUlIEhDhtv4riDoMMN48A=";

    public static String PUBLIC_LICENSE_KEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtP7oH2AB4IRtU/N4TzH4F9S2o/Kn449uYkQAbGGa8YdYvDaGHRsU++wn88Si6RVXwCShQ4SwhBB6mSwOiWitcDjeCWp/55fNsKe86qACtr5vZnSk8b5r2VLExoD1AqQrf6l4kB1FU0j1YIC2YPZXGjbYcuC4gheRKQTQU+kKgvckwpN51Xc0NBeMXfr5O0SgcedUw3of1pQhxa0i87mso+OMm96TuqZxkVezZbe9AWQl3Jz2kD6CFENoupYxjxzFpFLMJTuE+IXh2hgGJxg6WAi/i3Fmc8ajVe7IqXRHjsSrz8oQW6yfuOwxQyQyRI4g73CN+rukl8jgPtPJb0zJBwIDAQAB";
    public static String PRIVATE_LICENSE_KEY = "MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBAOpQdkkjYFftwE3XzjBPcb4ZwGmiVzbJUvZCJ3WgOk3QXUfK5L+X5pyolIsRW71rdt2So214EFKKkjzq4G81ur4lsJ4ELJfScxQtaA+FJRMRv0eOr/rjtADDf9R4LX/ImTzWCiL18MhgziChPxe7yaKSiM0QUGD/MlXsD3TIQsrJAgMBAAECgYEAiiOHqNTHXu9W2U2qTIb5qrZp3p0Lx8vnMjGMK+ebl/aIl+PA+kdmeONWN6qzKDgr6rMh0E7haJgzYOCkEkxd281i0siYNMu4LxnMRskDmnq/Fe6uVWjNer7KG+TFyzHvUwYfSSgVzpBepmTGzLEBRCSyomKkL3DvaxUIyXomqAECQQD7HdFU18NcbpWG9oLrBRlb2xMI3slCK5z6S4aDFbMB6wPKL5RLZl5UzdOIfcxf6l+EXqUK3FzxpatrJFK4knEBAkEA7t7+rG+b1L1f92Gp04+n/GR9J+9QZL+oijG1ldRlaMG8a08n4n0r8Tz+q9UrKK6CLedSVOfaiqo10hreTMcRyQJAUPuBuZnHf/SNtjOjbsV7MxtIXTDzuKo1+qYGoBmMXA79buO/MD24KCMkl7zx/vCnkxmuof0E4E0sJeZV4v4fAQJBAKRWhp/bqToTj5KCfnQuwUPDXte6rXSi5zEsQeFy3tHTd+3b53TK6RBvBkbxwV+br7QlEV+Yo6ICuER0d7wauQECQQDEDqGhDah37iIeHivXKUBFPULofQQIj/LLm5jKkJeSX5EeaPrOnPk1mKKUDZGTGVL/fD9Z4OQPFn9ix9QZAfj3";


    //访问控制
    public final static Integer ACCESS_CONTROL_STATUS_NEGATIVE = -1;
    public final static Integer ACCESS_CONTROL_STATUS_0 = 0;
    // 所有人不可修改
    public final static Integer ACCESS_CONTROL_STATUS__2 = -2;

    // 管理员 团队管理员可修改
    public final static Integer ACCESS_CONTROL_STATUS_1 = 1;

    // 管理可见
    public final static Integer SHOW_FLAG_1 = 1;

    // 管理员企业管理员可见
    public final static Integer SHOW_FLAG_2 = 2;

    //质量门禁检测有效期配置
    public final static String QUALITY_CONTROL_DAY = "quality_control_day";
    //质量门禁高危漏洞数
    public final static String QUALITY_HIGH_VUL_NUM = "quality_high_vul_num";

    // 质量门禁第三方组件个数卡口数字
    public final static String QUALITY_HIGH_PLUGIN_NUM = "quality_high_plugin_num";

}
