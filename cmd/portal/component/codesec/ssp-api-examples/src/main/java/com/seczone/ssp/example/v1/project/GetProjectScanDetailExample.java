package com.seczone.ssp.example.v1.project;

import cn.hutool.http.ContentType;
import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 6.22 查询单个项目详细结果
 * @since 2022/12/21 15:17
 **/
public class GetProjectScanDetailExample {

    public static void main(String[] args) {
        // 请求url
        String url = Constants.CS_URL + "/openapi/project/getProjectScanDetail";

        // 请求参数
        Map<String, Object> params = new HashMap<>();
        // 必填 扫描任务ID
        params.put("appId", "8fd049ab-b266-409c-b5e6-900362fe745d");
        // 扫描记录ID
        params.put("recordId", null);

        // 发起请求
        HttpUtils.get(url, params, ContentType.FORM_URLENCODED);
    }
}