package com.seczone.ssp.example.v2.project;

import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 4.6	创建空项目
 * @since 2022/12/13 14:27
 **/
public class CreateProjectByVoidExample {

    public static void main(String[] args) {

        // 请求url
        String url = Constants.CS_URL + "/cs/api/v2/project/createProjectByVoid";

        // 构建请求参数
        Map<String, Object> params = new HashMap<>();
        params.put("projectName", "项目名称2");
        params.put("projectDesc", "项目描述");
        // 指定团队Id 企业管理员调用该接口时必填
        params.put("orgUuid", "00a7b33e-f075-4758-9ef0-d1bb989748ac");
        // 发起请求
        HttpUtils.post(url, params);
    }
}
