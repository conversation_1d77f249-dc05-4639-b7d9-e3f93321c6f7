package com.seczone.ssp.example.v2.project;

import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

/**
 * <AUTHOR>
 * @description 4.25 统计代码行数
 * ************* 注意：实时统计耗时较长，只给出主语言，js，html统计结果，与页面数据一致 *************
 * @since 2022/12/15 11:29
 **/
public class StatisticCodeExample {

    public static void main(String[] args) {

        // 项目uuid
        String projectUuid = "68aa740c-3f50-42a5-a550-d8b54270c615";

        // 扫描任务id (4.8 4.9 4.11 返回)
        String appId = "11d4d19f-56ae-486c-b307-5ec0361569a3";

        // 请求url
        String url = Constants.CS_URL + "/cs/api/v2/project/"+projectUuid+"/task/"+appId+"/statisticCode";

        // 发起请求
        HttpUtils.get(url);


    }
}