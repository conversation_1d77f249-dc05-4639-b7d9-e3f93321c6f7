package com.seczone.ssp.example.v2.project;

import cn.hutool.http.ContentType;
import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 4.42 获取扫描日志
 * @since 2022/12/16 10:57
 **/
public class ScanLogExample {

    public static void main(String[] args) {

        // 项目uuid
        String projectUuid = "68aa740c-3f50-42a5-a550-d8b54270c615";

        // 扫描任务id (4.8 4.9 4.11 返回)
        String appId = "11d4d19f-56ae-486c-b307-5ec0361569a3";

        // 请求url
        String url = Constants.CS_URL + "/cs/api/v2/project/"+projectUuid+"/task/"+appId+"/scanLog";

        // 构造请求参数
        Map<String, Object> params = new HashMap<>();
        // 扫描记录Id （4.15 发起扫描任务返回）
        params.put("recordId", "c38e7670-ef4e-429c-a3f4-ea9b0b86b31f");

        // 发起请求
        HttpUtils.get(url, params, ContentType.FORM_URLENCODED);

    }
}