package com.seczone.ssp.example.v1.project;

import cn.hutool.http.ContentType;
import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 6.17 编辑GIT项目
 * @since 2022/12/21 14:42
 **/
public class EditGitProjectExample {

    public static void main(String[] args) {

        // 请求url
        String url = Constants.CS_URL + "/openapi/project/editGitProject";

        // 构造请求参数
        Map<String, Object> params = packageData();

        // 发起请求
        HttpUtils.post(url, params);

    }

    public static Map<String, Object> packageData() {
        Map<String, Object> params = new HashMap<>();
        // 必填 项目ID string(40)
        params.put("projectUuid", "25fb68d0-3317-40c0-88b8-15ca00587e12");
        // 项目名称 string(512)
        params.put("projectName", null);
        // 项目描述 string(500)
        params.put("projectDesc", null);
        // git地址 string(200)
        params.put("url", "http://************:9001/yipei/0224.git");
        // git地址是否以https开头（默认0） 0：否  1：是
        params.put("urlHead", null);
        // 必填 git 认证类型 0.用户名密码认证 1.	token认证
        params.put("authenticationMethod", 0);
        // 用户名（authenticationMethod=0时可用） string(200)
        params.put("username", "");
        // 密码（authenticationMethod=0时可用）（使用RSA加密传输，补位方式为RSA/ECB/PKCS1Padding,加密完成后需要使用Base64进行编码） string(200)
        params.put("password", "");
        // token（authenticationMethod=1时可用） string(128)
        params.put("token", null);
        // branch 分支名称 string(128)
        params.put("branch", null);
        // commitId 提交id string(64)
        params.put("commitId", null);
        // 自定义标识符（可作为项目名称 如有多个 使用","进行分隔传输 例如 "研发部门,研发一组"） string(512)
        params.put("extraMark", null);
        // 文件过滤或文件夹过滤 多个路径以","分隔 text
        params.put("fileFilter", null);
        // 回调通知地址 string(100)
        params.put("callBackUrl", null);
        // 项目语言ID（不填时自动识别）
        params.put("language", 1);
        // 扫描类型 1 静态扫描（默认） 2 编码规范
        params.put("type", 1);
        // 是否开启依赖：0不开启（默认） 1开启
        params.put("isOpenDepend", 0);
        // 仓库依赖id，开启依赖时填写
        params.put("depotId", null);
        // 自定义回调通知请求头，约定采用key,value 格式，以分号分隔，如aaa,bbb;ccc,ddd;
        params.put("callBackHeaders", null);

        return params;
    }

}