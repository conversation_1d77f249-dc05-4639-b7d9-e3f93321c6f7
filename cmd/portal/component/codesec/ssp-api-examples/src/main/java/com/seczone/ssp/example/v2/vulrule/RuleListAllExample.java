package com.seczone.ssp.example.v2.vulrule;

import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

/**
 * <AUTHOR>
 * @description 4.31 查询同组下全部规则集列表
 * @since 2022/12/16 10:53
 **/
public class RuleListAllExample {

    public static void main(String[] args) {

        // 请求url
        String url = Constants.CS_URL + "/cs/api/v2/vulrule/ruleListALL";

        // 发起请求
        HttpUtils.get(url);
    }

}