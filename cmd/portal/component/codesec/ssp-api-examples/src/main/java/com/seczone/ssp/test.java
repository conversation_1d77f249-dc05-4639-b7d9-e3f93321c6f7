package com.seczone.ssp;

import org.junit.jupiter.api.Test;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

public class ConstantsTest {

    @Test
    public void testBuildV2HeaderWithBodyParams() {
        // 准备测试数据
        String url = "http://localhost:28081/cs/api/v2/project/123";
        Map<String, Object> params = new HashMap<>();
        params.put("name", "testProject");
        params.put("description", "这是一个测试项目");
        params.put("enabled", true);
        params.put("priority", 1);
        
        // 调用被测试方法
        Map<String, String> headers = Constants.buildV2Header(url, params);
        
        // 验证基础headers
        assertNotNull(headers);
        assertEquals(Constants.accessKey, headers.get("accessKey"));
        assertNotNull(headers.get("x-cs-timestamp"));
        assertNotNull(headers.get("x-cs-nonce"));
        assertNotNull(headers.get("x-cs-signature"));
        
        // 验证body参数是否被正确处理
        // 由于签名是动态生成的，我们只能验证控制台输出
        System.out.println("\n测试body传参结果:");
        System.out.println("URL: " + url);
        System.out.println("Params: " + params);
        System.out.println("Generated Headers:");
        headers.forEach((key, value) -> System.out.println(key + ": " + value));
        
        // 验证不同类型的参数是否被正确处理
        assertTrue(params.containsKey("name"));
        assertTrue(params.containsKey("description"));
        assertTrue(params.containsKey("enabled"));
        assertTrue(params.containsKey("priority"));
    }

    @Test
    public void testBuildV2HeaderWithEmptyParams() {
        // 测试空参数情况
        String url = "http://localhost:28081/cs/api/v2/project/123";
        Map<String, Object> emptyParams = new HashMap<>();
        
        Map<String, String> headers = Constants.buildV2Header(url, emptyParams);
        
        assertNotNull(headers);
        // 空参数时签名应该只包含时间戳、随机数和token
        System.out.println("\n测试空参数结果:");
        headers.forEach((key, value) -> System.out.println(key + ": " + value));
    }
}