package com.seczone.ssp.example.v2.whitefun;

import cn.hutool.http.ContentType;
import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 4.40 根据Id删除白名单
 * ***************** 注：管理员或企业管理员调用 *****************
 * @since 2022/12/16 10:44
 **/
public class WhiteDeleteBatchExample {

    public static void main(String[] args) {

        // 请求url
        String url = Constants.CS_URL + "/cs/api/v2/whiteFunction/whiteDeleteBatch";

        // 构建请求参数
        Map<String, Object> params = new HashMap<>();
        // 白名单ID(4.38返回白名单ID)
        params.put("funIds", "10,11");

        // 发起请求
        HttpUtils.delete(url, params, ContentType.FORM_URLENCODED);
    }
}