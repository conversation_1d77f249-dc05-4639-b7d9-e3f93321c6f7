package com.seczone.ssp.example.v2.project;

import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 4.10 编辑项目
 * @since 2022/12/14 09:48
 **/
public class EditProjectExample {

    public static void main(String[] args) {

        String projectUuid = "8dd3811a-1f1f-40e8-a6e4-5294217eace3";

        // 请求url
        String url = Constants.CS_URL + "/cs/api/v2/project/"+projectUuid+"/editProject";

        // 构造请求参数
        Map<String, Object> params = packageData();

        // 发起请求
        HttpUtils.put(url, params);
    }

    public static Map<String, Object> packageData() {

        Map<String, Object> params = new HashMap<>();
        //  必填 代码类型： 1：上传代码 2：git方式获取代码 3：svn方式获取代码
        params.put("sourceMode", 1);
        // 源码上传id（sourceMode=1时必填） string(40)
        params.put("storeId", "99b41e30-e3ff-4525-a68a-eab0dc642760");
        // svn版本库ID（sourceMode=3时可用）
        params.put("versionId", null);
        // 项目名称 string(512)
        params.put("projectName", null);
        // 项目描述 string(500)
        params.put("projectDesc", null);
        // git/svn地址（sourceMode=2或3时必填） string(200)
        params.put("url", null);
        // git地址是否以https开头（默认0） 0：否  1：是
        params.put("urlHead", null);
        // git类型（sourceMode=2时必填） 1 : gitlab 2 : github 3 : gitee 6 : gerrit
        params.put("gitType", null);
        // git 认证类型（sourceMode=2时必填） 0.用户名密码认证 1.	token认证 2.	SSH密钥
        params.put("authenticationMethod", null);
        // 用户名（authenticationMethod=0且sourceMode=2时可用） string(200)
        params.put("username", "");
        // 密码（authenticationMethod=0且sourceMode=2时可用）（使用RSA加密传输，补位方式为RSA/ECB/PKCS1Padding,加密完成后需要使用Base64进行编码） string(200)
        params.put("password", "");
        // token（authenticationMethod=1且sourceMode=2时可用） string(128)
        params.put("token", null);
        // token是否加密
        params.put("isTokenEncrypt",false);
        // SSH密钥（authenticationMethod=2时可用）（使用RSA加密传输，补位方式为RSA/ECB/PKCS1Padding,加密完成后需要使用Base64进行编码） longtext
        params.put("sshKey", null);
        // branch 分支名称 string(128)
        params.put("branch", null);
        // tag git标签名称 string(128)
        params.put("tag", null);
        // checkCommitId git指定拉取commitId string(64)
        params.put("checkCommitId",null);
        // commitId 提交id string(64)
        params.put("commitId", null);
        // 自定义标识符（可作为项目名称 如有多个 使用","进行分隔传输 例如 "研发部门,研发一组"） string(512)
        params.put("extraMark", null);
        // 文件过滤或文件夹过滤 多个路径以","分隔 text
        params.put("fileFilter", null);
        // 回调通知地址 string(100)
        params.put("callBackUrl", null);
        // 项目语言ID（不填时自动识别）
        params.put("language", 1);
        // 扫描类型 1 静态扫描（默认） 2 编码规范
        params.put("type", 1);
        // 是否开启依赖：0不开启（默认） 1开启
        params.put("isOpenDepend", 0);
        // 仓库依赖id，开启依赖时填写
        params.put("depotId", null);
        // 拉取指定文件/文件夹名称 string(100)
        params.put("pullFileName", null);
        // 规则集ID（4.41接口获取）
        params.put("ruleSetId", null);
        // 指定团队Id 企业管理员调用该接口时必填
        params.put("orgUuid", "1");
        return params;
    }
}
