package com.seczone.ssp.example.v3;

import cn.hutool.http.ContentType;
import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 获取工具规则列表
 */
public class ListToolRulesExample {

    public static void main(String[] args) {

        // 请求url
        String url = Constants.CS_URL + "/cs/api/v3/toolRules/list";

        // 构造请求参数
        Map<String, Object> params = new HashMap<>();

        // 类型 内置规则、集成规则
        params.put("type", "内置规则");
        // 内置规则
        params.put("name", null);


        // 发起请求
        HttpUtils.get(url, params, ContentType.FORM_URLENCODED);

    }
}
