package com.seczone.ssp.example.v2.project;

import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @description 4.47 根据项目名称或项目ID查询项目相关信息
 * @since 2022/12/16 16:26
 **/
public class GetProjectOverViewExample {

    public static void main(String[] args) {

        String projectUuid = "5855896e-0e14-4a8d-bf2d-6c699df73673";

        // 项目名称
        String projectName = null;

        // 请求url
        String url = "";
        if(StringUtils.isBlank(projectUuid)) {
            url = Constants.CS_URL + "/cs/api/v2/project/getProjectOverView";
        } else {
            url = Constants.CS_URL + "/cs/api/v2/project/"+projectUuid+"/getProjectOverView";
        }
        if(!StringUtils.isBlank(projectName)) {
            url += ("?projectName="+projectName);
        }


        // 发起请求
        HttpUtils.get(url);
    }
}