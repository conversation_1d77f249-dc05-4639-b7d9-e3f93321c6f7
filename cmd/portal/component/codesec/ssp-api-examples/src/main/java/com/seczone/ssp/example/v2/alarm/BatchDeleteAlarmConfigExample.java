package com.seczone.ssp.example.v2.alarm;

import cn.hutool.http.ContentType;
import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 4.46 删除告警条件配置信息
 * @since 2022/12/16 16:14
 **/
public class BatchDeleteAlarmConfigExample {

    public static void main(String[] args) {

        // 请求url
        String url = Constants.CS_URL + "/cs/api/v2/alarmConfig/batchDeleteAlarmConfig";

        // 构建请求参数
        Map<String, Object> params = new HashMap<>();
        // 必填 欲删除的配置id集合（逗号拼接）
        params.put("configIdList", "1,2");

        // 发起请求
        HttpUtils.delete(url, params, ContentType.MULTIPART);
    }
}