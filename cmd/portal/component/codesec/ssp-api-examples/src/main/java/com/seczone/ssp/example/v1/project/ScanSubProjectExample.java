package com.seczone.ssp.example.v1.project;

import cn.hutool.http.ContentType;
import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 6.5	执行扫描任务
 * @since 2022/12/21 11:09
 **/
public class ScanSubProjectExample {

    public static void main(String[] args) {
        // 请求url
        String url = Constants.CS_URL + "/openapi/project/scanSubProject";

        // 请求参数
        Map<String, Object> params = new HashMap<>();
        // 子项目ID，即当前需要执行的扫描任务
        params.put("appId", "e1a28cb3-37bb-4898-9e6f-6381bc3119ea");

        // 发起请求
        HttpUtils.post(url, params, ContentType.FORM_URLENCODED);
    }
}