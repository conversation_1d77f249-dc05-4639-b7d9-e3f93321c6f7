package com.seczone.ssp.example.v4.datacenter;

import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Qi
 * @description 开发语言超、高危缺陷统计（TOP5）接口
 * @since 2025/3/21 15:25
 **/
public class LanguageTop5StatisticsExample {

    public static void main(String[] args) {
        // 请求url
        String url = Constants.CS_URL + "/cs/api/v4/dashboard/languageTop5Statistics";
        // 发起请求
        HttpUtils.get(url);
    }
}
