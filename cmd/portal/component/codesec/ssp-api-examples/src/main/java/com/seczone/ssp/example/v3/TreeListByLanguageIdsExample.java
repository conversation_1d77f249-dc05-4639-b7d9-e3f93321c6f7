package com.seczone.ssp.example.v3;

import cn.hutool.http.ContentType;
import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 4.27 根据多个语言查询安全规则漏洞树
 **/
public class TreeListByLanguageIdsExample {

    public static void main(String[] args) {


        // 请求url
        String url = Constants.CS_URL + "/cs/api/v3/vul/treeListByLanguageIds";

        // 构造请求参数
        Map<String, Object> params = new HashMap<>();

        // 语言id，多个使用逗号拼接 必填
        params.put("languageIds", "1");

        // 发起请求
        HttpUtils.get(url, params, ContentType.FORM_URLENCODED);

    }

}
