package com.seczone.ssp.example.v1.project;

import cn.hutool.http.ContentType;
import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 6.10 统计项目漏洞数
 * @since 2022/12/21 11:32
 **/
public class GetProjectVulAllCountExample {

    public static void main(String[] args) {
        // 请求url
        String url = Constants.CS_URL + "/openapi/project/getProjectVulAllCount";

        // 请求参数
        Map<String, Object> params = new HashMap<>();
        // 项目Uuid
        params.put("projectUuid", "4b5c0c3f-71f1-4efd-8d30-b46a57e3739f");

        // 发起请求
        HttpUtils.get(url, params, ContentType.FORM_URLENCODED);
    }
}