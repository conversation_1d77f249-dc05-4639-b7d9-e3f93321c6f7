package com.seczone.ssp.example.v2.whitefun;

import cn.hutool.http.ContentType;
import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 4.36 根据语言ID查询白名单对应支持漏洞信息
 * ***************** 注：管理员或企业管理员调用 *****************
 * @since 2022/12/15 17:29
 **/
public class FindVulListExample {

    public static void main(String[] args) {

        // 请求url
        String url = Constants.CS_URL + "/cs/api/v2/whiteFunction/findVulList";

        // 构建请求参数
        Map<String, Object> params = new HashMap<>();
        // 必填 语言ID（4.4获取语言ID）
        params.put("languageId", 1);

        // 发起请求
        HttpUtils.get(url, params, ContentType.FORM_URLENCODED);
    }
}