package com.seczone.ssp.example.v2.alarm;

import cn.hutool.http.ContentType;
import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 4.45 查询告警条件配置信息
 * @since 2022/12/16 16:08
 **/
public class ListAlarmConfigExample {

    public static void main(String[] args) {

        // 请求url
        String url = Constants.CS_URL + "/cs/api/v2/alarmConfig/listAlarmConfig";

        // 构建请求参数
        Map<String, Object> params = new HashMap<>();
        // 每页个数(默认10条)
        params.put("pageSize", 10);
        // 当前页数（默认第1页）
        params.put("pageCurrent", 1);

        // 发起请求
        HttpUtils.get(url, params, ContentType.FORM_URLENCODED);

    }
}