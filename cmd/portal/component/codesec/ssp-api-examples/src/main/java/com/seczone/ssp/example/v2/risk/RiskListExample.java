package com.seczone.ssp.example.v2.risk;

import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

/**
 * <AUTHOR>
 * @description 4.30 获取漏洞等级列表
 * @since 2022/12/15 15:58
 **/
public class RiskListExample {

    public static void main(String[] args) {

        // 请求url
        String url = Constants.CS_URL + "/cs/api/v2/risk/riskList";

        // 发起请求
        HttpUtils.get(url);
    }
}