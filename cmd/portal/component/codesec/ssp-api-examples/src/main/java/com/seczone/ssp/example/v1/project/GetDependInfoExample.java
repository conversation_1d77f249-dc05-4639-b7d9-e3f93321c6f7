package com.seczone.ssp.example.v1.project;

import cn.hutool.http.ContentType;
import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

/**
 * <AUTHOR>
 * @description 6.27 获取依赖信息
 * @since 2022/12/21 16:13
 **/
public class GetDependInfoExample {

    public static void main(String[] args) {

        // 请求url
        String url = Constants.CS_URL + "/openapi/project/getDependInfo";

        // 发起请求
        HttpUtils.get(url);

    }
}