package com.seczone.ssp.example.v2.vulrule;

import cn.hutool.http.ContentType;
import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 4.32 查询规则集列表
 * ***************** 注：管理员或企业管理员调用 *****************
 * @since 2022/12/15 16:47
 **/
public class RuleListExample {

    public static void main(String[] args) {

        // 请求url
        String url = Constants.CS_URL + "/cs/api/v2/vulrule/rulelist";

        // 构建请求参数
        Map<String, Object> params = new HashMap<>();
        // 语言id（4.4获取语言ID）
        params.put("languageId", 1);
        // 规则集名称 string(20)
        params.put("name", "");
        // 每页个数
        params.put("pageSize", 10);
        // 当前页数
        params.put("pageCurrent", 1);

        // 发起请求
        HttpUtils.get(url, params, ContentType.FORM_URLENCODED);
    }
}