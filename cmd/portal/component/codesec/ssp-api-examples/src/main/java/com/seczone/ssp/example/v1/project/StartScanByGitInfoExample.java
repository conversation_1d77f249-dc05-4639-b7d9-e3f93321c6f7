package com.seczone.ssp.example.v1.project;

import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 6.19 启动扫描任务
 * @since 2022/12/21 15:01
 **/
public class StartScanByGitInfoExample {

    public static void main(String[] args) {

        // 请求url
        String url = Constants.CS_URL + "/openapi/project/startScanByGitInfo";

        // 构建请求参数
        Map<String, Object> params = new HashMap<>();
        // 扫描任务Id
        params.put("appId", "8b4f451a-e7f7-4819-8ce9-11a261fef9d3");
        // 项目名称 appId于projectName任选其一，两者必须存在其中一个 两者都存在的情况下仅使用appId
        params.put("projectName", null);
        // 扫描类型 1 静态扫描（默认） 2 编码规范
        params.put("type", 1);
        // 分支名称
        params.put("branch", null);
        // 提交ID
        params.put("commitId", null);

        // 发起请求
        HttpUtils.post(url, params);
    }
}