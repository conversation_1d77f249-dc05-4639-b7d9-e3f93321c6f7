package com.seczone.ssp.example.v1.project;

import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import com.seczone.ssp.Constants;

import java.util.Base64;

/**
 * <AUTHOR>
 * @description 6.26 项目扫描结果跳转URL
 * @since 2022/12/21 16:08
 **/
public class RedirectScanResultUrlExample {

    public static void main(String[] args) {

        // 扫描任务ID
        String appId = "4b5c0c3f-71f1-4efd-8d30-b46a57e3739f";
        // 扫描记录ID
        String recordId = "4b5c0c3f-71f1-4efd-8d30-b46a57e3739f";
        // 任务类型：默认为1，静态代码扫描
        int rType = 1;
        // 需要跳转的位置： 0：显示概览 1：显示漏洞列表
        int pTo = 1;
        // 邮箱
        String email = "<EMAIL>";
        // 加密后密码
        String password = "123";
        // 用户自定义标识
        String extraMark = "";

        StringBuffer url = new StringBuffer(Constants.CS_URL+"/#/project/viewer/static?");
        url.append("a_id=").append(appId);
        url.append("r_id=").append(recordId);
        url.append("r_type=").append(rType);
        url.append("p_to=").append(pTo);
        url.append("email=").append(email);
        url.append("password=").append(encrypt(password));
        url.append("extraMark=").append(extraMark);

        System.out.println(url);
    }

    /**
     * 加密方式
     * @param data
     * @return
     */
    public static String encrypt(String data){
        // 公钥请询问相关技术支持
        String publicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEArj449zYBKOGOp8NW6t5o\n" +
                "nWL1e18MM3GvBo/kvpW1b0ctZgxvEHw1LFQg4Dpbxaqb/bwWRywM7gfprG2uS22r\n" +
                "TM7L+bzrCJTVURABzB6LOZpgUH8WgLRutw+smBPbi6XPcKswnDpvQoDOvBiSpQEi\n" +
                "hFezC8mhhF0yB6HsQBB7IIyagK8kmKdOT8Qkl+dgV2SIshc9JvbUbujG3Xc597oI\n" +
                "lX4Hh2qdrw3ZiVjsZYGFhDFKOCDh1ltiw+6c3oJTKLsbKavSigi5b9TQyb/MPkrG\n" +
                "aljaR3SNe7AbwzqHrFrt74XakqK5edztbwkWfCmGdORBzwsX5SThmI2jx4HErm2o\n" +
                "1QIDAQAB";
        RSA rsa = new RSA(null, publicKey);
        //RSA 公钥加密
        byte[] encrypt = rsa.encrypt(data.getBytes(), KeyType.PublicKey);
        //Base 64编码
        return Base64.getEncoder().encodeToString(encrypt);
    }
}