package com.seczone.ssp.example.v3;

import cn.hutool.http.ContentType;
import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 根据多个语言查询编码规范漏洞树
 * 该接口仅返回一层数据，parentId传值和不传值，返回的数据结构不一致
 */
public class TreeListCodingByLanguageIdsExample {

    public static void main(String[] args) {

        // 请求url
        String url = Constants.CS_URL + "/cs/api/v3/codingRules/treeListByLanguageIds";

        // 构造请求参数
        Map<String, Object> params = new HashMap<>();

        // 语言id，多个使用逗号拼接 默认1
        params.put("languageIds", "9");
        // 父级id
        params.put("parentId", 9);
        // 扫描方案id
        params.put("ruleSetId", 11);

        // 发起请求
        HttpUtils.get(url, params, ContentType.FORM_URLENCODED);

    }
}
