package com.seczone.ssp.example.v2.license;

import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

/**
 * <AUTHOR>
 * @description 4.26 获取License信息
 * @since 2022/12/15 11:33
 **/
public class GetLicenseInfoExample {

    public static void main(String[] args) {

        // 请求url
        String url = Constants.CS_URL + "/cs/api/v2/license/getLicenseInfo";

        // 发起请求
        HttpUtils.post(url);
    }
}