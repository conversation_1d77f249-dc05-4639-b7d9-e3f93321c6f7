package com.seczone.ssp.example.v2.alarm;

import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 4.44 修改告警条件配置信息
 * @since 2022/12/16 15:51
 **/
public class ModifyAlarmConfigExample {

    public static void main(String[] args) {

        // 请求url
        String url = Constants.CS_URL + "/cs/api/v2/alarmConfig/modifyAlarmConfig";

        // 构造请求参数
        Map<String, Object> params = packageData();

        // 发起请求
        HttpUtils.post(url, params);

    }

    public static Map<String, Object> packageData() {
        // 构建请求参数
        Map<String, Object> params = new HashMap<>();
        // 欲修改的配置id
        params.put("id", 111);
        // 必填 启用状态: 0:未启用 1:启用
        params.put("status", 0);
        // 必填 适用对象 0:针对指定团队 1:针对所有团队
        params.put("type", 1);
        // 必填 告警方式 1.邮件 2.系统通知 3.短信 string(5)
        params.put("alarmType", "1,2");
        // 必填 触发通知条件:  1：分数为触发条件  2：严重漏洞数为触发条件  3：高危漏洞数为触发条件 4：复合触发条件
        params.put("alarmTrigger", 1);
        // 接收告警的指定用户的uuid string(400)
        params.put("receiveUser", null);
        // 接收告警的角色  2:企业管理员 3:审计人员 5: 团队管理员 99:指定成员
        params.put("receiveRole", 2);
        // 指定检查的团队的uuid
        params.put("checkOrg", null);
        // 扫描任务状态：0：失败 1：成功
        params.put("taskStatus", null);
        // 项目风险值
        params.put("score", null);
        // 严重漏洞数
        params.put("criticalNum", null);
        // 高危漏洞数
        params.put("highNum", null);
        // 中危漏洞数
        params.put("mediumNum", 9000);
        // 低危漏洞数
        params.put("lowNum", null);
        // 建议漏洞数
        params.put("noteNum", null);
        // 圈复杂度超标个数
        params.put("cyclomaticComplexityNum", null);
        // 代码重复率（%）
        params.put("repetitiveRate", null);
        // 严重编码缺陷
        params.put("codingCriticalNum", null);
        // 高危编码缺陷
        params.put("codingHighNum", null);
        // 中危编码缺陷
        params.put("codingMediumNum", null);
        // 低危编码缺陷
        params.put("codingLowNum", null);
        // 建议编码缺陷
        params.put("codingNoteNum", null);

        return params;
    }
}