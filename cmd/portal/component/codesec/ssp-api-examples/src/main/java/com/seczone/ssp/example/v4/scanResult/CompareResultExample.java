package com.seczone.ssp.example.v4.scanResult;

import cn.hutool.http.ContentType;
import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 4.3.16扫描结果对比分析
 * @since 2024/11/28 10:20
 **/
public class CompareResultExample {

    public static void main(String[] args) {

        // 项目uuid
        String projectUuid = "f7fa3352-9e89-46aa-bf29-52e9e4d3e9ac";

        // 扫描任务id
        String appId = "5316d99d-05f5-4d20-ac71-68755457a8c5";
        // 扫描记录id
        String lastRecordId = "5ee48326-5fbe-4a8d-a1ed-bbb2b8182b92";
        // 扫描记录id
        String recordId = "f50c97ec-6779-41c9-8c8c-0476ded75c89";

        // 请求url
        String url = Constants.CS_URL + "/cs/api/v4/project/"+projectUuid+"/task/"+appId+"/compareResult";

        // 构造请求参数
        Map<String, Object> params = new HashMap<>();
        // 扫描记录Id （4.15 发起扫描任务返回）
        params.put("lastRecordId", lastRecordId);

        params.put("recordId", recordId);

        // 发起请求
        HttpUtils.get(url, params, ContentType.FORM_URLENCODED);
    }
}
