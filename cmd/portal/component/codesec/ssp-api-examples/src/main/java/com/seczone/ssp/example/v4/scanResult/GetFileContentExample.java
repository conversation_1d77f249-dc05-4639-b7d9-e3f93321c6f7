package com.seczone.ssp.example.v4.scanResult;

import cn.hutool.http.ContentType;
import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 4.3.18扫描结果对比-缺陷文件内容对比
 * @since 2024/11/28 10:20
 **/
public class GetFileContentExample {

    public static void main(String[] args) {

        // 项目uuid
        String projectUuid = "f7fa3352-9e89-46aa-bf29-52e9e4d3e9ac";

        // 扫描任务id
        String appId = "5316d99d-05f5-4d20-ac71-68755457a8c5";
        // 扫描记录id
        String lastRecordId = "5ee48326-5fbe-4a8d-a1ed-bbb2b8182b92";
        // 扫描记录id
        String recordId = "f50c97ec-6779-41c9-8c8c-0476ded75c89";
        // 文件路径；对应扫描结果对比-缺陷明细列表中的真实路径
        String filePath ="/test123/src/main/java/cn/bugstack/domain/strategy/model/entity/StrategyEntity.java";
        // 漏洞节点ID；对应扫描结果对比-缺陷明细列表中的漏洞节点ID（data.vulTraces[i].nodeLis[i].id）
        Integer vulNodeId = 754;
        // 漏洞名称
        String vulDataName="";
        // nodePosition
        Integer nodePosition=0;
        // rowNum
        Integer rowNum=0;

        // 请求url
        String url = Constants.CS_URL + "/cs/api/v4/project/"+projectUuid+"/task/"+appId+"/getFileContent";

        // 构造请求参数
        Map<String, Object> params = new HashMap<>();
        // 扫描记录Id （4.15 发起扫描任务返回）
        params.put("recordId", recordId);
        params.put("lastRecordId", lastRecordId);
        // 必填
        params.put("filePath", filePath);
        params.put("vulNodeId", vulNodeId);

        // 可选
//        params.put("vulDataName", vulDataName);
//        params.put("nodePosition", nodePosition);
//        params.put("rowNum", rowNum);

        // 发起请求
        HttpUtils.get(url, params, ContentType.FORM_URLENCODED);
    }
}
