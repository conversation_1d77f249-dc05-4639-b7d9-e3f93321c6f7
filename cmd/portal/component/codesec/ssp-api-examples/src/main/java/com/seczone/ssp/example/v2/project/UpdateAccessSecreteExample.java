package com.seczone.ssp.example.v2.project;

import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 4.3 更新密钥
 * @since 2022/12/13 11:50
 */
public class UpdateAccessSecreteExample {

    public static void main(String[] args) {

        String userId = "3c35c6fe-2ead-483d-bae5-a3942edfd680";

        // 请求url
        String url = Constants.CS_URL + "/cs/api/v2/user/"+userId+"/updateAccessSecrete";

        // 请求参数
        Map<String, Object> params = new HashMap<>();
        // 秘钥过期时间 非必填 默认为 2099-01-01 00:00:00
        params.put("keyExpiredDate", "2023-01-03 00:00:00");

        // 发起请求
        HttpUtils.request(url, params, Constants.PUT, null);
    }

}
