package com.seczone.ssp.example.v2.vulrule;

import cn.hutool.http.ContentType;
import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 4.31 创建规则集信息
 * ***************** 注：管理员或企业管理员调用 *****************
 * @since 2022/12/15 16:02
 **/
public class CreateRuleSetExample {

    public static void main(String[] args) {

        // 请求url
        String url = Constants.CS_URL + "/cs/api/v2/vulrule/createRuleSet";

        // 构建请求参数
        Map<String, Object> params = new HashMap<>();
        // 必填 规则集名称 string(2-20)
        params.put("name", "长度20-111111111111111");
        // 必填 语言ID（4.4获取语言ID）
        params.put("languageId", 1);
        // 必填 漏洞id集合(4.27获取漏洞ID)
        params.put("vulDataIds", "4d0bda87-1b3b-4a69-8bd1-42ca44cbe064,ebc836bd-176d-4cce-8e83-92295c68af45");
        // 必填 0为不生效，1为生效
        params.put("status", 1);

        // 发起请求
        HttpUtils.post(url, params, ContentType.FORM_URLENCODED);

    }
}