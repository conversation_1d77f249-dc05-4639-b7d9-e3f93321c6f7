package com.seczone.ssp.utils;

import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import cn.hutool.crypto.asymmetric.Sign;
import cn.hutool.crypto.asymmetric.SignAlgorithm;
import com.seczone.ssp.common.GlobalConfigConsts;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @ClassName: RSAUtils
 * @Description: 公钥、密钥生成和校验
 * @date 2018年2月2日 下午3:57:14
 **/
public class RSAUtils {

    public final static String PUBLICKEY = "publicKey";
    public final static String PRIVATEKEY = "privateKey";


    private static RSA rsa = new RSA(GlobalConfigConsts.upPrivateKey, GlobalConfigConsts.upPublicKey);
    private static RSA rsaV2 = new RSA(GlobalConfigConsts.v2privateKey, GlobalConfigConsts.v2PublicKey);
    private static RSA rsaCont = new RSA(GlobalConfigConsts.privateKeyCont, GlobalConfigConsts.publicKeyCont);
    private static RSA rsaLicense = new RSA(GlobalConfigConsts.PRIVATE_LICENSE_KEY,GlobalConfigConsts.PUBLIC_LICENSE_KEY);

    public static String decrypt(String data) {
        byte[] decode = Base64Utils.base64Decode(data);
        byte[] decrypt = rsa.decrypt(decode, KeyType.PrivateKey);
        return new String(decrypt);
    }

    public static String encrypt(String data) {
        byte[] encrypt = rsa.encrypt(data.getBytes(), KeyType.PublicKey);
        return Base64Utils.base64Encode(encrypt);
    }

    public static String decryptV2(String data) {
        byte[] decode = Base64Utils.base64Decode(data);
        byte[] decrypt = rsaV2.decrypt(decode, KeyType.PrivateKey);
        return new String(decrypt);
    }

    public static String encryptV2(String data) {
        byte[] encrypt = rsaV2.encrypt(data.getBytes(), KeyType.PublicKey);
        return Base64Utils.base64Encode(encrypt);
    }

    public static String encryptByPrivateKey(String data) {
        byte[] encrypt = rsa.encrypt(data.getBytes(), KeyType.PrivateKey);
        return Base64Utils.base64Encode(encrypt);
    }

    /**
     * license平台公钥加密
     * @param data
     * @return
     */
    public static String encryptLicense(String data) {
        byte[] encrypt = rsaLicense.encrypt(data.getBytes(),KeyType.PublicKey);
        return Base64Utils.base64Encode(encrypt);
    }

    /**
     * RSA 签名，模式为：MD5withRSA
     *
     * @param data
     * @return
     */
    public static String sign(String data) {
        Sign sign = SecureUtil.sign(SignAlgorithm.MD5withRSA, GlobalConfigConsts.upPrivateKey, GlobalConfigConsts.upPublicKey);
        return Base64Utils.base64Encode(sign.sign(data.getBytes(StandardCharsets.UTF_8)));
    }

    /**
     * RSA 签名验证，模式为：MD5withRSA
     *
     * @param data
     * @return
     */
    public static boolean verifySignature(String data, String message) {
        Sign sign = SecureUtil.sign(SignAlgorithm.MD5withRSA, GlobalConfigConsts.upPrivateKey, GlobalConfigConsts.upPublicKey);
        return sign.verify(data.getBytes(StandardCharsets.UTF_8), Base64Utils.base64Decode(message));
    }

    public static String decryptCont(String data) {
        byte[] decode = Base64Utils.base64Decode(data);
        byte[] decrypt = rsaCont.decrypt(decode, KeyType.PrivateKey);
        return new String(decrypt,StandardCharsets.UTF_8);
    }

    public static String encryptCont(String data) {
        byte[] encrypt = rsaCont.encrypt(data.getBytes(), KeyType.PublicKey);
        return Base64Utils.base64Encode(encrypt);
    }

    /**
     * ASCII码转BCD码
     */
    public static byte[] ASCII_To_BCD(byte[] ascii, int asc_len) {
        byte[] bcd = new byte[asc_len / 2];
        int j = 0;
        for (int i = 0; i < (asc_len + 1) / 2; i++) {
            bcd[i] = asc_to_bcd(ascii[j++]);
            bcd[i] = (byte) (((j >= asc_len) ? 0x00 : asc_to_bcd(ascii[j++])) + (bcd[i] << 4));
        }
        return bcd;
    }

    public static byte asc_to_bcd(byte asc) {
        byte bcd;

        if ((asc >= '0') && (asc <= '9'))
            bcd = (byte) (asc - '0');
        else if ((asc >= 'A') && (asc <= 'F'))
            bcd = (byte) (asc - 'A' + 10);
        else if ((asc >= 'a') && (asc <= 'f'))
            bcd = (byte) (asc - 'a' + 10);
        else
            bcd = (byte) (asc - 48);
        return bcd;
    }

    /**
     * BCD转字符串
     */
    public static String bcd2Str(byte[] bytes) {
        char temp[] = new char[bytes.length * 2], val;

        for (int i = 0; i < bytes.length; i++) {
            val = (char) (((bytes[i] & 0xf0) >> 4) & 0x0f);
            temp[i * 2] = (char) (val > 9 ? val + 'A' - 10 : val + '0');

            val = (char) (bytes[i] & 0x0f);
            temp[i * 2 + 1] = (char) (val > 9 ? val + 'A' - 10 : val + '0');
        }
        return new String(temp);
    }

    /**
     * 拆分字符串
     */
    public static String[] splitString(String string, int len) {
        int x = string.length() / len;
        int y = string.length() % len;
        int z = 0;
        if (y != 0) {
            z = 1;
        }
        String[] strings = new String[x + z];
        String str = "";
        for (int i = 0; i < x + z; i++) {
            if (i == x + z - 1 && y != 0) {
                str = string.substring(i * len, i * len + y);
            } else {
                str = string.substring(i * len, i * len + len);
            }
            strings[i] = str;
        }
        return strings;
    }

    /**
     * 拆分数组
     */
    public static byte[][] splitArray(byte[] data, int len) {
        int x = data.length / len;
        int y = data.length % len;
        int z = 0;
        if (y != 0) {
            z = 1;
        }
        byte[][] arrays = new byte[x + z][];
        byte[] arr;
        for (int i = 0; i < x + z; i++) {
            arr = new byte[len];
            if (i == x + z - 1 && y != 0) {
                System.arraycopy(data, i * len, arr, 0, y);
            } else {
                System.arraycopy(data, i * len, arr, 0, len);
            }
            arrays[i] = arr;
        }
        return arrays;
    }

}
