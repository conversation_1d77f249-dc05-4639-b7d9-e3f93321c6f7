package com.seczone.ssp.example.v2.project;

import cn.hutool.http.ContentType;
import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 4.17 终止扫描任务
 * @since 2022/12/14 14:42
 **/
public class StopScanSubProjectExample {

    public static void main(String[] args) {

        // 项目uuid
        String projectUuid = "aa41a541-8e2f-4878-9265-b2efc7cacc77";

        // 扫描任务id (4.8 4.9 4.11 返回)
        String appId = "17778059-55ef-4ae0-be40-58a7123a4d48";

        // 请求url
        String url = Constants.CS_URL + "/cs/api/v2/project/"+projectUuid+"/task/"+appId+"/stopScanSubProject";

        // 构造请求参数
        Map<String, Object> params = new HashMap<>();
        // 扫描记录Id （4.50 重复发起扫描任务返回）
        params.put("recordId", null);

        // 发起请求
        HttpUtils.post(url, params, ContentType.FORM_URLENCODED);

    }
}