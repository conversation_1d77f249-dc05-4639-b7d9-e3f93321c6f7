package com.seczone.ssp.example.v2.vulrule;

import cn.hutool.http.ContentType;
import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 4.33 编辑规则集信息
 * ***************** 注：管理员或企业管理员调用 *****************
 * @since 2022/12/15 16:58
 **/
public class EditRuleSetExample {

    public static void main(String[] args) {

        // 请求url
        String url = Constants.CS_URL + "/cs/api/v2/vulrule/editRuleSet";

        // 构建请求参数
        Map<String, Object> params = new HashMap<>();
        // 必填 规则集ID(4.32查询的规则集ID)
        params.put("vulRuleId", 30);
        // 规则集名称
        params.put("name", null);
        // 语言ID（4.4获取语言ID）
        params.put("languageId", 1);
        // 必填 漏洞id集合
        params.put("vulDataIds", "faec2c97-7470-4cd4-85d2-1fe5a5a73141,9f8d4537-b9c1-4224-a29a-11433d525217");
        // 必填 状态 0为不生效，1为生效
        params.put("status", 0);
        // 描述
        params.put("desc", "desc");

        // 发起请求
        HttpUtils.put(url, params, ContentType.FORM_URLENCODED);
    }
}