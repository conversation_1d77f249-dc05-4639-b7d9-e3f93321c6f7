package com.seczone.ssp.example.v3;

import cn.hutool.http.ContentType;
import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 查询sonar是否开启
 */
public class GetSonarEnableExample {

    public static void main(String[] args) {

        // 请求url
        String url = Constants.CS_URL + "/cs/api/v3/sonar/getEnable";

        // 构造请求参数
        Map<String, Object> params = new HashMap<>();

        // 发起请求
        HttpUtils.get(url, params, ContentType.FORM_URLENCODED);

    }
}
