package com.seczone.ssp.example.v2.project;

import cn.hutool.http.ContentType;
import com.seczone.ssp.Constants;
import com.seczone.ssp.utils.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 4.2.6	删除扫描记录
 * @date 2023/9/15 16:15
 */
public class DeleteScanRecordExample {

    public static void main(String[] args) {

        // 项目uuid
        String projectUuid = "a0f6497e-3851-4b6a-8b03-5b033641f13d";

        // 扫描任务id
        String appId = "15a279ca-39a7-43f6-909d-e8c042bf7b09";

        // 请求url
        String url = Constants.CS_URL + "/cs/api/v2/project/"+projectUuid+"/task/"+appId+"/deleteScanRecord";

        // 请求参数
        Map<String, Object> params = new HashMap<>();
        // 必填 扫描记录id
        params.put("recordId", "97eb39d1-bf2e-4d6b-8962-a788a0752197");

        // 发起请求
        HttpUtils.delete(url, params, ContentType.FORM_URLENCODED);

    }

}
