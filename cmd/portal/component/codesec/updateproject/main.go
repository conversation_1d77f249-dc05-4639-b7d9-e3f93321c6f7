package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"

	"gitlab.com/piccolo_su/vegeta/cmd/portal/consts"
	"gitlab.com/piccolo_su/vegeta/cmd/portal/utils"
)

type CreateProjectParam struct {
	ProjectBody  Project
	AccessSecret string
	AccessKey    string
}
type DeleteProjectParam struct {
	ProjectUuid  string
	AccessSecret string
	AccessKey    string
}

// Project 定义请求结构体
type Project struct {
	ProjectName          string `json:"projectName"`
	Url                  string `json:"url"`
	GitType              int    `json:"gitType"`              // git类型  1 : gitlab  2 : github  3 : gitee  6 : gerrit  7 : bitbucket
	UrlHead              int    `json:"urlHead,omitempty"`    // git地址是否以https开头 0：否    1：是
	AuthenticationMethod int    `json:"authenticationMethod"` // git 认证类型  0.用户名密码认证（默认）  1.token认证 2.SSH密钥  凭据认证
	Token                string `json:"token"`
	ProjectDesc          string `json:"projectDesc"`
	Branch               string `json:"branch"` // 分支名
}

func (s *Project) Serializer() {
	s.ProjectName = strings.TrimSpace(s.ProjectName)
	s.Url = strings.TrimSpace(s.Url)
	s.Token = strings.TrimSpace(s.Token)
	s.AuthenticationMethod = consts.ProjectGitAuthMethodToken
	s.ProjectDesc = strings.TrimSpace(s.ProjectDesc)
	s.Branch = strings.TrimSpace(s.Branch)
}
func (s *Project) Check() error {
	if s.ProjectName == "" {
		return fmt.Errorf("project name is empty")
	}
	if s.Url == "" {
		return fmt.Errorf("project url is empty")
	}
	if s.Token == "" {
		return fmt.Errorf("project token is empty")
	}
	return nil
}

type SpecifiedMember struct {
	UserID         string `json:"userId"`
	RoleID         int    `json:"roleId"`
	PermissionType int    `json:"permissionType,omitempty"`
}

// CreateProjectResponse 定义响应结构体
type CreateProjectResponse struct {
	Status  bool   `json:"status"`
	Message string `json:"message"`
	Data    struct {
		ProjectUuid string `json:"projectUuid"`
		OrgUuid     string `json:"orgUuid"`
	} `json:"data"`
}

func CreateProject(baseUrl string, param *CreateProjectParam) (*CreateProjectResponse, error) {
	// 定义请求URL
	apiURL := fmt.Sprintf("%s/%s", baseUrl, "cs/api/v4/project/createProjectByGitInfo")
	pro := param.ProjectBody
	pro.Serializer()
	if err := pro.Check(); err != nil {
		return nil, err
	}

	// 将请求体转换为JSON格式
	jsonData, err := json.Marshal(pro)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %v", err)
	}
	data := map[string]interface{}{}
	_ = json.Unmarshal(jsonData, &data)

	// 创建HTTP请求
	req, err := http.NewRequest("POST", apiURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %v", err)
	}
	header := utils.BuildV2Header(data, param.AccessKey, param.AccessSecret, nil)

	// 设置自定义请求头
	for k, v := range header {
		req.Header.Set(k, v)
	}
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer func() { _ = resp.Body.Close() }()

	// 解析响应体
	response := &CreateProjectResponse{}
	if err := json.NewDecoder(resp.Body).Decode(response); err != nil {
		return nil, err
	}
	if !response.Status {
		return nil, fmt.Errorf("create project failed: %s", response.Message)
	}
	return response, nil
}

func UpdateProject(baseUrl string, proUUID string, param *CreateProjectParam) error {
	// 定义请求URL

	apiURL := fmt.Sprintf("%s/cs/api/v4/project/%s/editGitProject", baseUrl, proUUID)
	pro := param.ProjectBody
	pro.Serializer()
	if err := pro.Check(); err != nil {
		return err
	}

	// 将请求体转换为JSON格式
	jsonData, err := json.Marshal(pro)
	if err != nil {
		return fmt.Errorf("failed to marshal request: %v", err)
	}
	data := map[string]interface{}{}
	_ = json.Unmarshal(jsonData, &data)

	// 创建HTTP请求
	req, err := http.NewRequest(http.MethodPut, apiURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create request: %v", err)
	}
	header := utils.BuildV2Header(data, param.AccessKey, param.AccessSecret, []string{proUUID})

	// 设置自定义请求头
	for k, v := range header {
		req.Header.Set(k, v)
	}
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer func() { _ = resp.Body.Close() }()

	// 解析响应体
	response := &CreateProjectResponse{}
	if err := json.NewDecoder(resp.Body).Decode(response); err != nil {
		return err
	}
	if !response.Status {
		return fmt.Errorf("update project failed: %s", response.Message)
	}
	return nil
}
func DeleteProject(baseUrl string, param DeleteProjectParam) (*CreateProjectResponse, error) {
	// 定义请求URL
	apiURL := fmt.Sprintf("%s/cs/api/v4/project/%s/deleteProject", baseUrl, param.ProjectUuid)

	// 创建HTTP请求
	req, err := http.NewRequest(http.MethodDelete, apiURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %v", err)
	}
	header := utils.BuildV2Header(nil, param.AccessKey, param.AccessSecret, []string{param.ProjectUuid})

	// 设置自定义请求头
	for k, v := range header {
		req.Header.Set(k, v)
	}
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer func() { _ = resp.Body.Close() }()

	// 解析响应体
	response := &CreateProjectResponse{}
	if err := json.NewDecoder(resp.Body).Decode(response); err != nil {
		return nil, err
	}
	return response, nil
}

// git地址是否以https开头
// 0：否    1：是
func urlHead(url string) int {
	if strings.HasPrefix(url, "https://") {
		return 1
	}
	return 0
}

// 定义请求结构体
type CodesecProject struct {
	ProjectName          string `json:"projectName"`
	Url                  string `json:"url"`
	GitType              int    `json:"gitType"`              // git类型  1 : gitlab  2 : github  3 : gitee  6 : gerrit  7 : bitbucket
	UrlHead              int    `json:"urlHead,omitempty"`    // git地址是否以https开头 0：否    1：是
	AuthenticationMethod int    `json:"authenticationMethod"` // git 认证类型  0.用户名密码认证（默认）  1.token认证 2.SSH密钥  凭据认证
	Token                string `json:"token"`
}

// 定义请求结构体
type CreateProjectRequest struct {
	ProjectName          string           `json:"projectName"`
	Url                  string           `json:"url"`
	GitType              int              `json:"gitType"`              // git类型  1 : gitlab  2 : github  3 : gitee  6 : gerrit  7 : bitbucket
	UrlHead              int              `json:"urlHead,omitempty"`    // git地址是否以https开头 0：否    1：是
	AuthenticationMethod int              `json:"authenticationMethod"` // git 认证类型  0.用户名密码认证（默认）  1.token认证 2.SSH密钥  凭据认证
	Token                string           `json:"token"`
	ProjectDesc          string           `json:"projectDesc"`
	Branch               string           `json:"branch"` // 分支名
	SpecifiedMemberList  *SpecifiedMember `json:"specifiedMemberList,omitempty"`
}

func main() {
	// 定义请求URL
	uu := "fec6bec7-7914-49b9-ac7a-a96b6338256d"
	apiURL := fmt.Sprintf("http://1.95.60.107:28081/cs/api/v4/project/%s/editGitProject", uu)
	// 定义请求体
	requestBody := CreateProjectRequest{
		ProjectName: "liuqianli33",
		// Url:                  "ssh://*************************:30022/microseg-agent/agent.git",
		Url:                  "https://github.com/qianliout/algorithm.git",
		GitType:              2,
		UrlHead:              0,
		AuthenticationMethod: 1,
		Token:                "*********************************************************************************************",
		ProjectDesc:          "liuqianli",
		Branch:               "master",
	}
	// singeData := "projectName=liuqianli&specifiedMemberList=roleId=3&userId=70b00b10-1fb5-4a71-a028-4df42d822ecf"
	// singeData := "projectName=liuqianli"

	// 将请求体转换为JSON格式
	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		fmt.Println("Error marshaling JSON:", err)
		return
	}
	fmt.Println(string(jsonData))
	data := map[string]interface{}{}
	_ = json.Unmarshal(jsonData, &data)

	// 创建HTTP请求
	req, err := http.NewRequest(http.MethodPut, apiURL, bytes.NewBuffer(jsonData))
	if err != nil {
		fmt.Println("Error creating request:", err)
		return
	}
	// accessKey := "0de4fd1c-6201-4c76-a22f-d19d11b5ed03"
	// openToken := "eyJhbGciOiJIUzUxMiJ9.eyJub25jZSI6ImVjMzVhMmFkLWI1ZTYtNDQyMy1iMzFlLTk1YzJiMmQ4ZDkyNSIsInN1YiI6IjBkZTRmZDFjLTYyMDEtNGM3Ni1hMjJmLWQxOWQxMWI1ZWQwMyJ9.JaX_JnA5EGTzGYiYxMX1s0siPpX0Ttqkyi61n-FN9ddn8axxfh3C3NZ2o7RDqdffpfNO8NPQu1k2e3rQ-Ky8xQ"
	accessKey := "7b214572-4f1d-4630-ba73-12731ecfdf02"
	openToken := "eyJhbGciOiJIUzUxMiJ9.eyJub25jZSI6IjlkNTlhZDMyLTBhMzItNDAyNC04YjQ0LTYyZDUyOGY0Yzc3YiIsInN1YiI6IjdiMjE0NTcyLTRmMWQtNDYzMC1iYTczLTEyNzMxZWNmZGYwMiJ9.MbTQcgp_lzRq6ejwstlLoOsfgZkkEbwSbF08CfSBNbDSpPosCHD8zF7-a-yiFE8j6P_E_I-u-Nb2JElW47zCJA"
	header := utils.BuildV2Header(data, accessKey, openToken, []string{uu})

	// 设置自定义请求头
	for k, v := range header {
		req.Header.Set(k, v)
	}
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Println("Error sending request:", err)
		return
	}
	defer resp.Body.Close()

	// 解析响应体
	var response CreateProjectResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		fmt.Println("Error sending request:", err)
	}
	marshal, _ := json.Marshal(response)
	fmt.Println(string(marshal))

	// 输出响应结果
	fmt.Printf("Status: %v\n", response.Status)
	fmt.Printf("Message: %s\n", response.Message)
	fmt.Printf("Project UUID: %s\n", response.Data.ProjectUuid)
	fmt.Printf("Team UUID: %s\n", response.Data.OrgUuid)
}

// func BuildV2Header(url string, params map[string]interface{}) map[string]string {
// 	headers := make(map[string]string)

// 	// 用户API访问KEY
// 	headers["accessKey"] = AccessKey
// 	// 时间戳，5分钟内数据有效
// 	headers["x-cs-timestamp"] = strconv.FormatInt(time.Now().UnixMilli(), 10)
// 	// 随机字符串，防止重复提交
// 	headers["x-cs-nonce"] = generateNonce()

// 	// 拼接请求体里的参数
// 	data := MergeBody(params)
// 	// 拼接url中的参数
// 	matchesUrl := MatchesUrl(url)
// 	// 拼接密钥时间戳随机字符串
// 	end := "&" + OpenToken + "&" + headers["x-cs-timestamp"] + "&" + headers["x-cs-nonce"]

// 	var signData string
// 	if data == "" {
// 		if matchesUrl == "" {
// 			signData = end
// 		} else {
// 			signData = matchesUrl[1:] + end
// 		}
// 	} else {
// 		data = data[:len(data)-1]
// 		if matchesUrl == "" {
// 			signData = data + end
// 		} else {
// 			signData = data + matchesUrl + end
// 		}
// 	}

// 	fmt.Println("签名数据: ", signData)

// 	sha256 := ToSHA256(signData)
// 	fmt.Println("签名串: ", sha256)

// 	// 签名串
// 	headers["x-cs-signature"] = sha256

// 	return headers
// }
