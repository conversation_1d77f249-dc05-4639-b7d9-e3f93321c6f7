{"status": true, "code": "B200", "message": "成功。", "data": {"pageCurrent": 1, "pageSize": 100, "pageTotal": 1, "total": 1, "vulTraces": [{"vulId": "c23d13f8-00c5-48de-81b8-c81ae729931e", "vulTypeId": "53f031ce-17bd-4398-9020-48593cd8e0ff", "filename": "/agent/net-policy/policy/engine.h", "realFileName": "/agent/net-policy/policy/engine.h", "tagId": 1, "rowNum": "72", "riskId": 2, "vulFlag": "复发", "vulDataId": "4483151f-e9d2-4fbc-9be7-97077b6ba873", "tag": {"id": 1, "cnName": "待确认", "enName": "Unconfirmed", "nameLocale": "待确认"}, "risk": {"id": 2, "cnName": "高危", "enName": "high", "nameLocale": "高危", "status": 1}, "name": "函数可以静态", "cnName": "函数可以静态", "enName": "Functions can be statically", "nodeList": [{"id": 71562, "orgUuid": "2440fb48-4eed-44af-8486-4779b00d0378", "appId": "22f2b9ed-e381-4ea2-9fb9-ed2b5bdb92ba", "recordId": "4fa05f47-859d-4a2e-98fe-bbb1beeabbc7", "vulId": "c23d13f8-00c5-48de-81b8-c81ae729931e", "filename": "/agent/net-policy/policy/engine.h", "realFileName": "/agent/net-policy/policy/engine.h", "lineCode": "", "lineNum": "72", "userId": "1bc44feb-15dd-4178-a815-46e3ea87312e", "isSource": 1, "codeBlock": ""}, {"id": 71563, "orgUuid": "2440fb48-4eed-44af-8486-4779b00d0378", "appId": "22f2b9ed-e381-4ea2-9fb9-ed2b5bdb92ba", "recordId": "4fa05f47-859d-4a2e-98fe-bbb1beeabbc7", "vulId": "c23d13f8-00c5-48de-81b8-c81ae729931e", "filename": "/agent/net-policy/policy/engine.cc", "realFileName": "/agent/net-policy/policy/engine.cc", "lineCode": "", "lineNum": "301", "userId": "1bc44feb-15dd-4178-a815-46e3ea87312e", "isSource": 1, "codeBlock": ""}], "vulCount": 0, "languageName": "C/C++", "languageId": 5, "signer": "DB8A03CDB56EF2453A65E57F3F0C17B8"}]}}