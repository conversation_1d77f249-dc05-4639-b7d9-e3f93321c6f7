{"status": true, "code": "B200", "message": "成功。", "data": {"language": {"id": 33, "name": "Go"}, "cyclomaticComplexityNum": 0, "repetitiveLines": 0, "fileNum": 310, "fileSize": 21012, "scanResult": {"securityVulNum": 8815, "codeSecurityWeaknessNum": 0, "criticalNum": 20, "highNum": 35, "mediumNum": 93, "lowNum": 780, "noteNum": 7887, "newDiscoveryNum": 0, "repeatNum": 8815, "isReviseNum": 0, "libraryNum": 0, "cveNum": 0, "cnnvdNum": 0, "codeLineNum": 56276, "commentLines": 8097, "blankLines": 9172, "treeList": [{"label": "C/C++(8531)", "auditCount": 0, "count": 8531, "data": {"isTop": true}, "children": [{"label": "高危(1)", "auditCount": 0, "count": 1, "children": [{"label": "函数可以静态(1)", "data": {"severity": "高危", "vulcatName": "函数可以静态", "vulDataId": "4483151f-e9d2-4fbc-9be7-97077b6ba873"}, "vulDataId": "4483151f-e9d2-4fbc-9be7-97077b6ba873", "auditCount": 0, "count": 1, "riskId": 2, "children": [], "vulDataName": "函数可以静态", "vulDataCnName": "函数可以静态"}]}, {"label": "中危(64)", "auditCount": 0, "count": 64, "children": [{"label": "按值传递参数(15)", "data": {"severity": "中危", "vulcatName": "按值传递参数", "vulDataId": "4eae7768-04eb-404f-b422-d51e4af9f006"}, "vulDataId": "4eae7768-04eb-404f-b422-d51e4af9f006", "auditCount": 0, "count": 15, "riskId": 3, "children": [], "vulDataName": "按值传递参数", "vulDataCnName": "按值传递参数"}, {"label": "影子变量(7)", "data": {"severity": "中危", "vulcatName": "影子变量", "vulDataId": "53c4e693-b950-47a2-a38f-913efdf196fc"}, "vulDataId": "53c4e693-b950-47a2-a38f-913efdf196fc", "auditCount": 0, "count": 7, "riskId": 3, "children": [], "vulDataName": "影子变量", "vulDataCnName": "影子变量"}, {"label": "指针参数的可疑赋值(3)", "data": {"severity": "中危", "vulcatName": "指针参数的可疑赋值", "vulDataId": "6fab953c-01e3-4f9f-8b01-cf66657ad7c5"}, "vulDataId": "6fab953c-01e3-4f9f-8b01-cf66657ad7c5", "auditCount": 0, "count": 3, "riskId": 3, "children": [], "vulDataName": "指针参数的可疑赋值", "vulDataCnName": "指针参数的可疑赋值"}, {"label": "移动或转发的变量的存取(1)", "data": {"severity": "中危", "vulcatName": "移动或转发的变量的存取", "vulDataId": "81a017f8-eb8a-44b6-b53f-d89d6e9307ea"}, "vulDataId": "81a017f8-eb8a-44b6-b53f-d89d6e9307ea", "auditCount": 0, "count": 1, "riskId": 3, "children": [], "vulDataName": "移动或转发的变量的存取", "vulDataCnName": "移动或转发的变量的存取"}, {"label": "无参的函数参数赋值(4)", "data": {"severity": "中危", "vulcatName": "无参的函数参数赋值", "vulDataId": "8329c18b-0fb3-413b-a9a6-c24d8d0aca95"}, "vulDataId": "8329c18b-0fb3-413b-a9a6-c24d8d0aca95", "auditCount": 0, "count": 4, "riskId": 3, "children": [], "vulDataName": "无参的函数参数赋值", "vulDataCnName": "无参的函数参数赋值"}, {"label": "变量声明为const(14)", "data": {"severity": "中危", "vulcatName": "变量声明为const", "vulDataId": "8c6ba1e8-f8cc-4806-a286-94f4d6d00bf6"}, "vulDataId": "8c6ba1e8-f8cc-4806-a286-94f4d6d00bf6", "auditCount": 0, "count": 14, "riskId": 3, "children": [], "vulDataName": "变量声明为const", "vulDataCnName": "变量声明为const"}, {"label": "移位运算导致的基本类型数溢出(1)", "data": {"severity": "中危", "vulcatName": "移位运算导致的基本类型数溢出", "vulDataId": "c4a3fd33-2a8c-4e77-bb92-d81c6089efcf"}, "vulDataId": "c4a3fd33-2a8c-4e77-bb92-d81c6089efcf", "auditCount": 0, "count": 1, "riskId": 3, "children": [], "vulDataName": "移位运算导致的基本类型数溢出", "vulDataCnName": "移位运算导致的基本类型数溢出"}, {"label": "负左操作数的按位运算(2)", "data": {"severity": "中危", "vulcatName": "负左操作数的按位运算", "vulDataId": "ce913d98-d5b2-5efa-1a06-9e00a7af58eb"}, "vulDataId": "ce913d98-d5b2-5efa-1a06-9e00a7af58eb", "auditCount": 0, "count": 2, "riskId": 3, "children": [], "vulDataName": "负左操作数的按位运算", "vulDataCnName": "负左操作数的按位运算"}, {"label": "对void指针进行算术运算(13)", "data": {"severity": "中危", "vulcatName": "对void指针进行算术运算", "vulDataId": "da004287-d885-45e2-932a-c144cb9952fc"}, "vulDataId": "da004287-d885-45e2-932a-c144cb9952fc", "auditCount": 0, "count": 13, "riskId": 3, "children": [], "vulDataName": "对void指针进行算术运算", "vulDataCnName": "对void指针进行算术运算"}, {"label": "间接引用已删除的迭代器(4)", "data": {"severity": "中危", "vulcatName": "间接引用已删除的迭代器", "vulDataId": "f6121202-4b3b-4282-aadb-cce3d0cf45bf"}, "vulDataId": "f6121202-4b3b-4282-aadb-cce3d0cf45bf", "auditCount": 0, "count": 4, "riskId": 3, "children": [], "vulDataName": "间接引用已删除的迭代器", "vulDataCnName": "间接引用已删除的迭代器"}]}, {"label": "低危(579)", "auditCount": 0, "count": 579, "children": [{"label": "未使用的函数(144)", "data": {"severity": "低危", "vulcatName": "未使用的函数", "vulDataId": "06e69477-684f-016b-d982-86a5753c238b"}, "vulDataId": "06e69477-684f-016b-d982-86a5753c238b", "auditCount": 0, "count": 144, "riskId": 4, "children": [], "vulDataName": "未使用的函数", "vulDataCnName": "未使用的函数"}, {"label": "函数没有通过常量引用返回成员变量(4)", "data": {"severity": "低危", "vulcatName": "函数没有通过常量引用返回成员变量", "vulDataId": "0aa12701-01a1-4acb-9135-79362a311803"}, "vulDataId": "0aa12701-01a1-4acb-9135-79362a311803", "auditCount": 0, "count": 4, "riskId": 4, "children": [], "vulDataName": "函数没有通过常量引用返回成员变量", "vulDataCnName": "函数没有通过常量引用返回成员变量"}, {"label": "考虑使用STL算法而不是原始循环(2)", "data": {"severity": "低危", "vulcatName": "考虑使用STL算法而不是原始循环", "vulDataId": "16ffa91a-928b-4017-b8b1-8a043e1845f4"}, "vulDataId": "16ffa91a-928b-4017-b8b1-8a043e1845f4", "auditCount": 0, "count": 2, "riskId": 4, "children": [], "vulDataName": "考虑使用STL算法而不是原始循环", "vulDataCnName": "考虑使用STL算法而不是原始循环"}, {"label": "成员函数被声明为const(11)", "data": {"severity": "低危", "vulcatName": "成员函数被声明为const", "vulDataId": "2cdb3f73-1b69-4b36-a027-e931ffa271f5"}, "vulDataId": "2cdb3f73-1b69-4b36-a027-e931ffa271f5", "auditCount": 0, "count": 11, "riskId": 4, "children": [], "vulDataName": "成员函数被声明为const", "vulDataCnName": "成员函数被声明为const"}, {"label": "变量范围可以被限制(12)", "data": {"severity": "低危", "vulcatName": "变量范围可以被限制", "vulDataId": "6309ae78-e6cf-4353-8b50-32817127414a"}, "vulDataId": "6309ae78-e6cf-4353-8b50-32817127414a", "auditCount": 0, "count": 12, "riskId": 4, "children": [], "vulDataName": "变量范围可以被限制", "vulDataCnName": "变量范围可以被限制"}, {"label": "冗余空指针检查(1)", "data": {"severity": "低危", "vulcatName": "冗余空指针检查", "vulDataId": "6ad2513a-52aa-47aa-a010-a3dde086639d"}, "vulDataId": "6ad2513a-52aa-47aa-a010-a3dde086639d", "auditCount": 0, "count": 1, "riskId": 4, "children": [], "vulDataName": "冗余空指针检查", "vulDataCnName": "冗余空指针检查"}, {"label": "检测使用&的地方是否使用了|(6)", "data": {"severity": "低危", "vulcatName": "检测使用&的地方是否使用了|", "vulDataId": "7c5bfe59-6a84-45dd-816f-3f620f8f82ba"}, "vulDataId": "7c5bfe59-6a84-45dd-816f-3f620f8f82ba", "auditCount": 0, "count": 6, "riskId": 4, "children": [], "vulDataName": "检测使用&的地方是否使用了|", "vulDataCnName": "检测使用&的地方是否使用了|"}, {"label": "函数声明和定义参数名称不同(22)", "data": {"severity": "低危", "vulcatName": "函数声明和定义参数名称不同", "vulDataId": "89777600-a2e5-48b6-8ef7-41b31ed33617"}, "vulDataId": "89777600-a2e5-48b6-8ef7-41b31ed33617", "auditCount": 0, "count": 22, "riskId": 4, "children": [], "vulDataName": "函数声明和定义参数名称不同", "vulDataCnName": "函数声明和定义参数名称不同"}, {"label": "变量被声明为const其值不能被修改(85)", "data": {"severity": "低危", "vulcatName": "变量被声明为const其值不能被修改", "vulDataId": "8af162d2-2000-4c8c-8ffd-60d2e5098c58"}, "vulDataId": "8af162d2-2000-4c8c-8ffd-60d2e5098c58", "auditCount": 0, "count": 85, "riskId": 4, "children": [], "vulDataName": "变量被声明为const其值不能被修改", "vulDataCnName": "变量被声明为const其值不能被修改"}, {"label": "构造器应该是明确的(3)", "data": {"severity": "低危", "vulcatName": "构造器应该是明确的", "vulDataId": "925d39a2-72dd-4dae-8d73-8f380f263185"}, "vulDataId": "925d39a2-72dd-4dae-8d73-8f380f263185", "auditCount": 0, "count": 3, "riskId": 4, "children": [], "vulDataName": "构造器应该是明确的", "vulDataCnName": "构造器应该是明确的"}, {"label": "回调函数中的参数被声明为const其值不能被修改(10)", "data": {"severity": "低危", "vulcatName": "回调函数中的参数被声明为const其值不能被修改", "vulDataId": "98140824-52b7-4077-af3e-21218a882632"}, "vulDataId": "98140824-52b7-4077-af3e-21218a882632", "auditCount": 0, "count": 10, "riskId": 4, "children": [], "vulDataName": "回调函数中的参数被声明为const其值不能被修改", "vulDataCnName": "回调函数中的参数被声明为const其值不能被修改"}, {"label": "C++ 代码中的 C 样式指针强制转换(55)", "data": {"severity": "低危", "vulcatName": "C++ 代码中的 C 样式指针强制转换", "vulDataId": "aa1ed018-43ba-4ba7-826f-e35a8f2cb442"}, "vulDataId": "aa1ed018-43ba-4ba7-826f-e35a8f2cb442", "auditCount": 0, "count": 55, "riskId": 4, "children": [], "vulDataName": "C++ 代码中的 C 样式指针强制转换", "vulDataCnName": "C++ 代码中的 C 样式指针强制转换"}, {"label": "空指针冗余未检查(14)", "data": {"severity": "低危", "vulcatName": "空指针冗余未检查", "vulDataId": "ac9b2a07-d9d3-495a-ac9e-65a8118a45e3"}, "vulDataId": "ac9b2a07-d9d3-495a-ac9e-65a8118a45e3", "auditCount": 0, "count": 14, "riskId": 4, "children": [], "vulDataName": "空指针冗余未检查", "vulDataCnName": "空指针冗余未检查"}, {"label": "代码不可达(194)", "data": {"severity": "低危", "vulcatName": "代码不可达", "vulDataId": "b15ce320-2c82-4fba-8d46-d68aca0d6d4c"}, "vulDataId": "b15ce320-2c82-4fba-8d46-d68aca0d6d4c", "auditCount": 0, "count": 194, "riskId": 4, "children": [], "vulDataName": "代码不可达", "vulDataCnName": "代码不可达"}, {"label": "无效的Printf参数%u(3)", "data": {"severity": "低危", "vulcatName": "无效的Printf参数%u", "vulDataId": "b312eb40-6885-4f49-bf68-6dce3d23abaa"}, "vulDataId": "b312eb40-6885-4f49-bf68-6dce3d23abaa", "auditCount": 0, "count": 3, "riskId": 4, "children": [], "vulDataName": "无效的Printf参数%u", "vulDataCnName": "无效的Printf参数%u"}, {"label": "始终为真/假的条件(5)", "data": {"severity": "低危", "vulcatName": "始终为真/假的条件", "vulDataId": "d503e07b-4b1d-49f3-9419-efee6d326c63"}, "vulDataId": "d503e07b-4b1d-49f3-9419-efee6d326c63", "auditCount": 0, "count": 5, "riskId": 4, "children": [], "vulDataName": "始终为真/假的条件", "vulDataCnName": "始终为真/假的条件"}, {"label": "成员变量未初始化(8)", "data": {"severity": "低危", "vulcatName": "成员变量未初始化", "vulDataId": "fab99381-ef72-4a9e-a08e-10798aa68784"}, "vulDataId": "fab99381-ef72-4a9e-a08e-10798aa68784", "auditCount": 0, "count": 8, "riskId": 4, "children": [], "vulDataName": "成员变量未初始化", "vulDataCnName": "成员变量未初始化"}]}, {"label": "建议(7887)", "auditCount": 0, "count": 7887, "children": [{"label": "未使用的结构体成员(10)", "data": {"severity": "建议", "vulcatName": "未使用的结构体成员", "vulDataId": "098521b0-2db8-4fec-a389-1841f4194c11"}, "vulDataId": "098521b0-2db8-4fec-a389-1841f4194c11", "auditCount": 0, "count": 10, "riskId": 5, "children": [], "vulDataName": "未使用的结构体成员", "vulDataCnName": "未使用的结构体成员"}, {"label": "除非对象的类型为“指向const char 的指针”，否则不得将字符串常量赋值给该对象(10)", "data": {"severity": "建议", "vulcatName": "除非对象的类型为“指向const char 的指针”，否则不得将字符串常量赋值给该对象", "vulDataId": "0c143bc1-d658-4e70-ae11-a2a073ae4e95"}, "vulDataId": "0c143bc1-d658-4e70-ae11-a2a073ae4e95", "auditCount": 0, "count": 10, "riskId": 5, "children": [], "vulDataName": "除非对象的类型为“指向const char 的指针”，否则不得将字符串常量赋值给该对象", "vulDataCnName": "除非对象的类型为“指向const char 的指针”，否则不得将字符串常量赋值给该对象"}, {"label": "项目不应包含未被使用的类型(type)声明(3)", "data": {"severity": "建议", "vulcatName": "项目不应包含未被使用的类型(type)声明", "vulDataId": "0dd80e0e-7567-431e-9849-d767917d4adc"}, "vulDataId": "0dd80e0e-7567-431e-9849-d767917d4adc", "auditCount": 0, "count": 3, "riskId": 5, "children": [], "vulDataName": "项目不应包含未被使用的类型(type)声明", "vulDataCnName": "项目不应包含未被使用的类型(type)声明"}, {"label": "内联函数应使用静态存储类声明(3)", "data": {"severity": "建议", "vulcatName": "内联函数应使用静态存储类声明", "vulDataId": "138e9f11-a384-42bc-96bd-b9404e6023fb"}, "vulDataId": "138e9f11-a384-42bc-96bd-b9404e6023fb", "auditCount": 0, "count": 3, "riskId": 5, "children": [], "vulDataName": "内联函数应使用静态存储类声明", "vulDataCnName": "内联函数应使用静态存储类声明"}, {"label": "表达式中运算符的优先级应明确(155)", "data": {"severity": "建议", "vulcatName": "表达式中运算符的优先级应明确", "vulDataId": "14440912-3464-4c8d-8bb6-583e5ee72584"}, "vulDataId": "14440912-3464-4c8d-8bb6-583e5ee72584", "auditCount": 0, "count": 155, "riskId": 5, "children": [], "vulDataName": "表达式中运算符的优先级应明确", "vulDataCnName": "表达式中运算符的优先级应明确"}, {"label": "每个switch 语句都应具有default 标签(99)", "data": {"severity": "建议", "vulcatName": "每个switch 语句都应具有default 标签", "vulDataId": "1647dde9-ae85-4693-b3c9-33eb65f29abf"}, "vulDataId": "1647dde9-ae85-4693-b3c9-33eb65f29abf", "auditCount": 0, "count": 99, "riskId": 5, "children": [], "vulDataName": "每个switch 语句都应具有default 标签", "vulDataCnName": "每个switch 语句都应具有default 标签"}, {"label": "项目不应包含未被使用的宏(macro)声明(151)", "data": {"severity": "建议", "vulcatName": "项目不应包含未被使用的宏(macro)声明", "vulDataId": "16977d49-d734-493c-83a5-636f5c661158"}, "vulDataId": "16977d49-d734-493c-83a5-636f5c661158", "auditCount": 0, "count": 151, "riskId": 5, "children": [], "vulDataName": "项目不应包含未被使用的宏(macro)声明", "vulDataCnName": "项目不应包含未被使用的宏(macro)声明"}, {"label": "局部全局(internal linkage)对象和函数的标识符应是唯一的(2)", "data": {"severity": "建议", "vulcatName": "局部全局(internal linkage)对象和函数的标识符应是唯一的", "vulDataId": "19affcc4-2ca4-41d6-8f1a-291ceee8df79"}, "vulDataId": "19affcc4-2ca4-41d6-8f1a-291ceee8df79", "auditCount": 0, "count": 2, "riskId": 5, "children": [], "vulDataName": "局部全局(internal linkage)对象和函数的标识符应是唯一的", "vulDataCnName": "局部全局(internal linkage)对象和函数的标识符应是唯一的"}, {"label": "操作数不得为不适当的基本类型(160)", "data": {"severity": "建议", "vulcatName": "操作数不得为不适当的基本类型", "vulDataId": "1d179e75-e798-41d8-b954-da560692f99d"}, "vulDataId": "1d179e75-e798-41d8-b954-da560692f99d", "auditCount": 0, "count": 160, "riskId": 5, "children": [], "vulDataName": "操作数不得为不适当的基本类型", "vulDataCnName": "操作数不得为不适当的基本类型"}, {"label": "不得使用标准库时间和日期功能(6)", "data": {"severity": "建议", "vulcatName": "不得使用标准库时间和日期功能", "vulDataId": "1d348145-59ec-4dcb-a99c-e757297d3ea0"}, "vulDataId": "1d348145-59ec-4dcb-a99c-e757297d3ea0", "auditCount": 0, "count": 6, "riskId": 5, "children": [], "vulDataName": "不得使用标准库时间和日期功能", "vulDataCnName": "不得使用标准库时间和日期功能"}, {"label": "未读取的变量(8)", "data": {"severity": "建议", "vulcatName": "未读取的变量", "vulDataId": "1e6b46c4-d2a8-42ed-a13a-921c73b12a6d"}, "vulDataId": "1e6b46c4-d2a8-42ed-a13a-921c73b12a6d", "auditCount": 0, "count": 8, "riskId": 5, "children": [], "vulDataName": "未读取的变量", "vulDataCnName": "未读取的变量"}, {"label": "+，-，+=和-=运算符不得应用于指针类型的表达式(120)", "data": {"severity": "建议", "vulcatName": "+，-，+=和-=运算符不得应用于指针类型的表达式", "vulDataId": "240fc2f0-13a5-4442-bd4c-8113137164a3"}, "vulDataId": "240fc2f0-13a5-4442-bd4c-8113137164a3", "auditCount": 0, "count": 120, "riskId": 5, "children": [], "vulDataName": "+，-，+=和-=运算符不得应用于指针类型的表达式", "vulDataCnName": "+，-，+=和-=运算符不得应用于指针类型的表达式"}, {"label": "若一个对象的标识符仅在一个函数中出现，则应将它定义在块范围内(70)", "data": {"severity": "建议", "vulcatName": "若一个对象的标识符仅在一个函数中出现，则应将它定义在块范围内", "vulDataId": "241f98d6-4583-49f8-b7e9-001cfcd41ee5"}, "vulDataId": "241f98d6-4583-49f8-b7e9-001cfcd41ee5", "auditCount": 0, "count": 70, "riskId": 5, "children": [], "vulDataName": "若一个对象的标识符仅在一个函数中出现，则应将它定义在块范围内", "vulDataCnName": "若一个对象的标识符仅在一个函数中出现，则应将它定义在块范围内"}, {"label": "表达式的值不得赋值给具有较窄基本类型或不同基本类型的对象(44)", "data": {"severity": "建议", "vulcatName": "表达式的值不得赋值给具有较窄基本类型或不同基本类型的对象", "vulDataId": "255b9811-86ae-4588-8cbd-3d4becd55115"}, "vulDataId": "255b9811-86ae-4588-8cbd-3d4becd55115", "auditCount": 0, "count": 44, "riskId": 5, "children": [], "vulDataName": "表达式的值不得赋值给具有较窄基本类型或不同基本类型的对象", "vulDataCnName": "表达式的值不得赋值给具有较窄基本类型或不同基本类型的对象"}, {"label": "仅在本编译单元中调用的对象和函数，应定义成局部属性(146)", "data": {"severity": "建议", "vulcatName": "仅在本编译单元中调用的对象和函数，应定义成局部属性", "vulDataId": "25c89f68-98df-47df-8cf7-7721d7f86752"}, "vulDataId": "25c89f68-98df-47df-8cf7-7721d7f86752", "auditCount": 0, "count": 146, "riskId": 5, "children": [], "vulDataName": "仅在本编译单元中调用的对象和函数，应定义成局部属性", "vulDataCnName": "仅在本编译单元中调用的对象和函数，应定义成局部属性"}, {"label": "for 循环应为良好格式(3)", "data": {"severity": "建议", "vulcatName": "for 循环应为良好格式", "vulDataId": "29cc1455-91af-4878-aeb4-be080fd00e9b"}, "vulDataId": "29cc1455-91af-4878-aeb4-be080fd00e9b", "auditCount": 0, "count": 3, "riskId": 5, "children": [], "vulDataName": "for 循环应为良好格式", "vulDataCnName": "for 循环应为良好格式"}, {"label": "不得使用可变长数组类型(3)", "data": {"severity": "建议", "vulcatName": "不得使用可变长数组类型", "vulDataId": "2d9c69d9-0506-44ca-862b-b9aa141e4a8c"}, "vulDataId": "2d9c69d9-0506-44ca-862b-b9aa141e4a8c", "auditCount": 0, "count": 3, "riskId": 5, "children": [], "vulDataName": "不得使用可变长数组类型", "vulDataCnName": "不得使用可变长数组类型"}, {"label": "不得使用<stdlib.h>中的内存分配和释放函数(44)", "data": {"severity": "建议", "vulcatName": "不得使用<stdlib.h>中的内存分配和释放函数", "vulDataId": "3428c844-81c5-4288-9d88-e580cd31d223"}, "vulDataId": "3428c844-81c5-4288-9d88-e580cd31d223", "auditCount": 0, "count": 44, "riskId": 5, "children": [], "vulDataName": "不得使用<stdlib.h>中的内存分配和释放函数", "vulDataCnName": "不得使用<stdlib.h>中的内存分配和释放函数"}, {"label": "禁止隐式声明函数(74)", "data": {"severity": "建议", "vulcatName": "禁止隐式声明函数", "vulDataId": "3918616a-c6b4-4cae-b5e2-4c772671e17a"}, "vulDataId": "3918616a-c6b4-4cae-b5e2-4c772671e17a", "auditCount": 0, "count": 74, "riskId": 5, "children": [], "vulDataName": "禁止隐式声明函数", "vulDataCnName": "禁止隐式声明函数"}, {"label": "不得使用标准库输入/输出函数(9)", "data": {"severity": "建议", "vulcatName": "不得使用标准库输入/输出函数", "vulDataId": "4707eb36-4698-4842-9322-08ee1a036d90"}, "vulDataId": "4707eb36-4698-4842-9322-08ee1a036d90", "auditCount": 0, "count": 9, "riskId": 5, "children": [], "vulDataName": "不得使用标准库输入/输出函数", "vulDataCnName": "不得使用标准库输入/输出函数"}, {"label": "不得使用逗号(,)运算符(110)", "data": {"severity": "建议", "vulcatName": "不得使用逗号(,)运算符", "vulDataId": "474ccc02-622a-4a79-b87e-9a5d036d1c77"}, "vulDataId": "474ccc02-622a-4a79-b87e-9a5d036d1c77", "auditCount": 0, "count": 110, "riskId": 5, "children": [], "vulDataName": "不得使用逗号(,)运算符", "vulDataCnName": "不得使用逗号(,)运算符"}, {"label": "不应使用goto 语句(1110)", "data": {"severity": "建议", "vulcatName": "不应使用goto 语句", "vulDataId": "4b131657-2eb4-45d9-b047-9acece6a26ec"}, "vulDataId": "4b131657-2eb4-45d9-b047-9acece6a26ec", "auditCount": 0, "count": 1110, "riskId": 5, "children": [], "vulDataName": "不应使用goto 语句", "vulDataCnName": "不应使用goto 语句"}, {"label": "复合表达式的值不得转换为其他基本类型或更宽的基本类型(24)", "data": {"severity": "建议", "vulcatName": "复合表达式的值不得转换为其他基本类型或更宽的基本类型", "vulDataId": "4fb093f2-8de2-4afc-89b8-dd7799055a51"}, "vulDataId": "4fb093f2-8de2-4afc-89b8-dd7799055a51", "auditCount": 0, "count": 24, "riskId": 5, "children": [], "vulDataName": "复合表达式的值不得转换为其他基本类型或更宽的基本类型", "vulDataCnName": "复合表达式的值不得转换为其他基本类型或更宽的基本类型"}, {"label": "标签(tag)名称应是唯一标识符(22)", "data": {"severity": "建议", "vulcatName": "标签(tag)名称应是唯一标识符", "vulDataId": "61e42b8b-8497-45ef-b3ac-f0dc742f2595"}, "vulDataId": "61e42b8b-8497-45ef-b3ac-f0dc742f2595", "auditCount": 0, "count": 22, "riskId": 5, "children": [], "vulDataName": "标签(tag)名称应是唯一标识符", "vulDataCnName": "标签(tag)名称应是唯一标识符"}, {"label": "字符序列“/*”和“//”不得在注释中使用(3)", "data": {"severity": "建议", "vulcatName": "字符序列“/*”和“//”不得在注释中使用", "vulDataId": "669104e0-2a44-4986-81e5-dc873065fa44"}, "vulDataId": "669104e0-2a44-4986-81e5-dc873065fa44", "auditCount": 0, "count": 3, "riskId": 5, "children": [], "vulDataName": "字符序列“/*”和“//”不得在注释中使用", "vulDataCnName": "字符序列“/*”和“//”不得在注释中使用"}, {"label": "#include 指令之前仅允许出现预处理指令或注释(2)", "data": {"severity": "建议", "vulcatName": "#include 指令之前仅允许出现预处理指令或注释", "vulDataId": "6dc3f4c6-d853-44b5-90c5-8cd517449cd3"}, "vulDataId": "6dc3f4c6-d853-44b5-90c5-8cd517449cd3", "auditCount": 0, "count": 2, "riskId": 5, "children": [], "vulDataName": "#include 指令之前仅允许出现预处理指令或注释", "vulDataCnName": "#include 指令之前仅允许出现预处理指令或注释"}, {"label": "不得将#define 和#undef 用于保留的标识符或保留的宏名称(21)", "data": {"severity": "建议", "vulcatName": "不得将#define 和#undef 用于保留的标识符或保留的宏名称", "vulDataId": "7309e827-7e12-4b84-84a1-a1b494b516eb"}, "vulDataId": "7309e827-7e12-4b84-84a1-a1b494b516eb", "auditCount": 0, "count": 21, "riskId": 5, "children": [], "vulDataName": "不得将#define 和#undef 用于保留的标识符或保留的宏名称", "vulDataCnName": "不得将#define 和#undef 用于保留的标识符或保留的宏名称"}, {"label": "全局标识符应在且只在一处定义(56)", "data": {"severity": "建议", "vulcatName": "全局标识符应在且只在一处定义", "vulDataId": "7383afbe-b19e-4e89-b929-a4b3735384e5"}, "vulDataId": "7383afbe-b19e-4e89-b929-a4b3735384e5", "auditCount": 0, "count": 56, "riskId": 5, "children": [], "vulDataName": "全局标识符应在且只在一处定义", "vulDataCnName": "全局标识符应在且只在一处定义"}, {"label": "不得将指向void 的指针转换为指向对象的指针(118)", "data": {"severity": "建议", "vulcatName": "不得将指向void 的指针转换为指向对象的指针", "vulDataId": "75316614-17a7-47ab-b869-a9313f862907"}, "vulDataId": "75316614-17a7-47ab-b869-a9313f862907", "auditCount": 0, "count": 118, "riskId": 5, "children": [], "vulDataName": "不得将指向void 的指针转换为指向对象的指针", "vulDataCnName": "不得将指向void 的指针转换为指向对象的指针"}, {"label": "宏标识符与其他标识符不得重名(3)", "data": {"severity": "建议", "vulcatName": "宏标识符与其他标识符不得重名", "vulDataId": "7b27fe05-d082-4f8a-a46f-0bd31afc9058"}, "vulDataId": "7b27fe05-d082-4f8a-a46f-0bd31afc9058", "auditCount": 0, "count": 3, "riskId": 5, "children": [], "vulDataName": "宏标识符与其他标识符不得重名", "vulDataCnName": "宏标识符与其他标识符不得重名"}, {"label": "不得有无效代码(2)", "data": {"severity": "建议", "vulcatName": "不得有无效代码", "vulDataId": "7f2048f4-3231-49d0-ab4e-ba61b11d3ca5"}, "vulDataId": "7f2048f4-3231-49d0-ab4e-ba61b11d3ca5", "auditCount": 0, "count": 2, "riskId": 5, "children": [], "vulDataName": "不得有无效代码", "vulDataCnName": "不得有无效代码"}, {"label": "在枚举列表中，隐式指定的枚举常量的值应唯一(5)", "data": {"severity": "建议", "vulcatName": "在枚举列表中，隐式指定的枚举常量的值应唯一", "vulDataId": "80320743-30ff-4cbc-a0e0-4cd75bf47511"}, "vulDataId": "80320743-30ff-4cbc-a0e0-4cd75bf47511", "auditCount": 0, "count": 5, "riskId": 5, "children": [], "vulDataName": "在枚举列表中，隐式指定的枚举常量的值应唯一", "vulDataCnName": "在枚举列表中，隐式指定的枚举常量的值应唯一"}, {"label": "不得使用union 关键字(17)", "data": {"severity": "建议", "vulcatName": "不得使用union 关键字", "vulDataId": "844468ac-ce58-490b-9caf-aa16e0182f5a"}, "vulDataId": "844468ac-ce58-490b-9caf-aa16e0182f5a", "auditCount": 0, "count": 17, "riskId": 5, "children": [], "vulDataName": "不得使用union 关键字", "vulDataCnName": "不得使用union 关键字"}, {"label": "未使用的变量(8)", "data": {"severity": "建议", "vulcatName": "未使用的变量", "vulDataId": "8468c282-4ed3-4501-81e7-53a9a6d35f81"}, "vulDataId": "8468c282-4ed3-4501-81e7-53a9a6d35f81", "auditCount": 0, "count": 8, "riskId": 5, "children": [], "vulDataName": "未使用的变量", "vulDataCnName": "未使用的变量"}, {"label": "不得使用<stdlib.h>中的abort, exit, getenv 和 system 函数(500)", "data": {"severity": "建议", "vulcatName": "不得使用<stdlib.h>中的abort, exit, getenv 和 system 函数", "vulDataId": "8656cca2-d590-4480-8b53-9b7fa085f11b"}, "vulDataId": "8656cca2-d590-4480-8b53-9b7fa085f11b", "auditCount": 0, "count": 500, "riskId": 5, "children": [], "vulDataName": "不得使用<stdlib.h>中的abort, exit, getenv 和 system 函数", "vulDataCnName": "不得使用<stdlib.h>中的abort, exit, getenv 和 system 函数"}, {"label": "复合表达式的值不得赋值给具有较宽基本类型的对象(5)", "data": {"severity": "建议", "vulcatName": "复合表达式的值不得赋值给具有较宽基本类型的对象", "vulDataId": "882f9025-acac-42bf-aaaf-52b316200ce2"}, "vulDataId": "882f9025-acac-42bf-aaaf-52b316200ce2", "auditCount": 0, "count": 5, "riskId": 5, "children": [], "vulDataName": "复合表达式的值不得赋值给具有较宽基本类型的对象", "vulDataCnName": "复合表达式的值不得赋值给具有较宽基本类型的对象"}, {"label": "不得在指向不同对象类型的指针之间执行强制转换(12)", "data": {"severity": "建议", "vulcatName": "不得在指向不同对象类型的指针之间执行强制转换", "vulDataId": "8a0816a5-a8d8-414f-9b5c-3842eedf948c"}, "vulDataId": "8a0816a5-a8d8-414f-9b5c-3842eedf948c", "auditCount": 0, "count": 12, "riskId": 5, "children": [], "vulDataName": "不得在指向不同对象类型的指针之间执行强制转换", "vulDataCnName": "不得在指向不同对象类型的指针之间执行强制转换"}, {"label": "建议使用初始化列表(6)", "data": {"severity": "建议", "vulcatName": "建议使用初始化列表", "vulDataId": "9144b6ab-8a68-4dec-8ad4-62c101169525"}, "vulDataId": "9144b6ab-8a68-4dec-8ad4-62c101169525", "auditCount": 0, "count": 6, "riskId": 5, "children": [], "vulDataName": "建议使用初始化列表", "vulDataCnName": "建议使用初始化列表"}, {"label": "所有的if…else if 构造都应以else 语句结束(23)", "data": {"severity": "建议", "vulcatName": "所有的if…else if 构造都应以else 语句结束", "vulDataId": "91622c58-c829-497c-995e-3d47c033dcc5"}, "vulDataId": "91622c58-c829-497c-995e-3d47c033dcc5", "auditCount": 0, "count": 23, "riskId": 5, "children": [], "vulDataName": "所有的if…else if 构造都应以else 语句结束", "vulDataCnName": "所有的if…else if 构造都应以else 语句结束"}, {"label": "阴影参数(2)", "data": {"severity": "建议", "vulcatName": "阴影参数", "vulDataId": "930a6677-81b8-4908-9af1-c2374f0bf213"}, "vulDataId": "930a6677-81b8-4908-9af1-c2374f0bf213", "auditCount": 0, "count": 2, "riskId": 5, "children": [], "vulDataName": "阴影参数", "vulDataCnName": "阴影参数"}, {"label": "不得使用赋值运算符的结果(12)", "data": {"severity": "建议", "vulcatName": "不得使用赋值运算符的结果", "vulDataId": "93fc77f3-56a7-443c-a8d7-82d8f15ba5e4"}, "vulDataId": "93fc77f3-56a7-443c-a8d7-82d8f15ba5e4", "auditCount": 0, "count": 12, "riskId": 5, "children": [], "vulDataName": "不得使用赋值运算符的结果", "vulDataCnName": "不得使用赋值运算符的结果"}, {"label": "执行常规算术转换的运算符运算符的两个操作数应有相同的基本类型(336)", "data": {"severity": "建议", "vulcatName": "执行常规算术转换的运算符运算符的两个操作数应有相同的基本类型", "vulDataId": "9ae47128-b731-4021-b31c-9c4f5e66d2dd"}, "vulDataId": "9ae47128-b731-4021-b31c-9c4f5e66d2dd", "auditCount": 0, "count": 336, "riskId": 5, "children": [], "vulDataName": "执行常规算术转换的运算符运算符的两个操作数应有相同的基本类型", "vulDataCnName": "执行常规算术转换的运算符运算符的两个操作数应有相同的基本类型"}, {"label": "不得使用<stdlib.h>中的atof、atoi、atol 和atoll 函数(1)", "data": {"severity": "建议", "vulcatName": "不得使用<stdlib.h>中的atof、atoi、atol 和atoll 函数", "vulDataId": "9d4c3c2c-c39c-4cf6-b620-0bfb277bebe3"}, "vulDataId": "9d4c3c2c-c39c-4cf6-b620-0bfb277bebe3", "auditCount": 0, "count": 1, "riskId": 5, "children": [], "vulDataName": "不得使用<stdlib.h>中的atof、atoi、atol 和atoll 函数", "vulDataCnName": "不得使用<stdlib.h>中的atof、atoi、atol 和atoll 函数"}, {"label": "强制转换不得从指针指向的类型中删除任何const 或volatile 限定符(32)", "data": {"severity": "建议", "vulcatName": "强制转换不得从指针指向的类型中删除任何const 或volatile 限定符", "vulDataId": "a4553f0a-19de-414e-a013-fd59cc4fcb47"}, "vulDataId": "a4553f0a-19de-414e-a013-fd59cc4fcb47", "auditCount": 0, "count": 32, "riskId": 5, "children": [], "vulDataName": "强制转换不得从指针指向的类型中删除任何const 或volatile 限定符", "vulDataCnName": "强制转换不得从指针指向的类型中删除任何const 或volatile 限定符"}, {"label": "不得在指向void 的指针和算术类型之间执行强制转换(80)", "data": {"severity": "建议", "vulcatName": "不得在指向void 的指针和算术类型之间执行强制转换", "vulDataId": "a7a4b1b7-5284-4d22-a9cf-d3cae2cc2258"}, "vulDataId": "a7a4b1b7-5284-4d22-a9cf-d3cae2cc2258", "auditCount": 0, "count": 80, "riskId": 5, "children": [], "vulDataName": "不得在指向void 的指针和算术类型之间执行强制转换", "vulDataCnName": "不得在指向void 的指针和算术类型之间执行强制转换"}, {"label": "集合或联合体的初始化应括在花括号“{}”中(19)", "data": {"severity": "建议", "vulcatName": "集合或联合体的初始化应括在花括号“{}”中", "vulDataId": "ae61763d-fd89-49a4-8038-ea744615dd75"}, "vulDataId": "ae61763d-fd89-49a4-8038-ea744615dd75", "auditCount": 0, "count": 19, "riskId": 5, "children": [], "vulDataName": "集合或联合体的初始化应括在花括号“{}”中", "vulDataCnName": "集合或联合体的初始化应括在花括号“{}”中"}, {"label": "函数不得直接或间接调用自身(不得使用递归函数)(9)", "data": {"severity": "建议", "vulcatName": "函数不得直接或间接调用自身(不得使用递归函数)", "vulDataId": "b3ba8897-152c-4c0f-aed1-011254167ce6"}, "vulDataId": "b3ba8897-152c-4c0f-aed1-011254167ce6", "auditCount": 0, "count": 9, "riskId": 5, "children": [], "vulDataName": "函数不得直接或间接调用自身(不得使用递归函数)", "vulDataCnName": "函数不得直接或间接调用自身(不得使用递归函数)"}, {"label": "非void 返回类型的函数的返回值应该被使用(168)", "data": {"severity": "建议", "vulcatName": "非void 返回类型的函数的返回值应该被使用", "vulDataId": "b4a6735c-7904-4eea-8852-1ddf85c1ae4f"}, "vulDataId": "b4a6735c-7904-4eea-8852-1ddf85c1ae4f", "auditCount": 0, "count": 168, "riskId": 5, "children": [], "vulDataName": "非void 返回类型的函数的返回值应该被使用", "vulDataCnName": "非void 返回类型的函数的返回值应该被使用"}, {"label": "应仅在函数的末尾有单个函数出口(1040)", "data": {"severity": "建议", "vulcatName": "应仅在函数的末尾有单个函数出口", "vulDataId": "b5193b65-b01c-425c-bd26-edd579aa8dd3"}, "vulDataId": "b5193b65-b01c-425c-bd26-edd579aa8dd3", "auditCount": 0, "count": 1040, "riskId": 5, "children": [], "vulDataName": "应仅在函数的末尾有单个函数出口", "vulDataCnName": "应仅在函数的末尾有单个函数出口"}, {"label": "最多只能有一个用于终止循环语句的break 或goto 语句(10)", "data": {"severity": "建议", "vulcatName": "最多只能有一个用于终止循环语句的break 或goto 语句", "vulDataId": "b727a290-2e36-4424-91fd-3067915e0963"}, "vulDataId": "b727a290-2e36-4424-91fd-3067915e0963", "auditCount": 0, "count": 10, "riskId": 5, "children": [], "vulDataName": "最多只能有一个用于终止循环语句的break 或goto 语句", "vulDataCnName": "最多只能有一个用于终止循环语句的break 或goto 语句"}, {"label": "每个switch 语句应至少有两个switch 子句(241)", "data": {"severity": "建议", "vulcatName": "每个switch 语句应至少有两个switch 子句", "vulDataId": "b90fbea5-790f-4768-a2f1-bbc1ef8f26d4"}, "vulDataId": "b90fbea5-790f-4768-a2f1-bbc1ef8f26d4", "auditCount": 0, "count": 241, "riskId": 5, "children": [], "vulDataName": "每个switch 语句应至少有两个switch 子句", "vulDataCnName": "每个switch 语句应至少有两个switch 子句"}, {"label": "包含自增(++)或自减(--)运算符的完整表达式，除由自增或自减运算符引起的副作用外，不应有其他潜在的副作用(28)", "data": {"severity": "建议", "vulcatName": "包含自增(++)或自减(--)运算符的完整表达式，除由自增或自减运算符引起的副作用外，不应有其他潜在的副作用", "vulDataId": "bbd0beb0-0d26-4203-a7bb-ed3dff19b5a3"}, "vulDataId": "bbd0beb0-0d26-4203-a7bb-ed3dff19b5a3", "auditCount": 0, "count": 28, "riskId": 5, "children": [], "vulDataName": "包含自增(++)或自减(--)运算符的完整表达式，除由自增或自减运算符引起的副作用外，不应有其他潜在的副作用", "vulDataCnName": "包含自增(++)或自减(--)运算符的完整表达式，除由自增或自减运算符引起的副作用外，不应有其他潜在的副作用"}, {"label": "宏参数展开产生的表达式应放在括号内(36)", "data": {"severity": "建议", "vulcatName": "宏参数展开产生的表达式应放在括号内", "vulDataId": "beee51f1-5a3f-4c9f-8948-127554107985"}, "vulDataId": "beee51f1-5a3f-4c9f-8948-127554107985", "auditCount": 0, "count": 36, "riskId": 5, "children": [], "vulDataName": "宏参数展开产生的表达式应放在括号内", "vulDataCnName": "宏参数展开产生的表达式应放在括号内"}, {"label": "不应更改函数形参(483)", "data": {"severity": "建议", "vulcatName": "不应更改函数形参", "vulDataId": "c175913a-f940-421d-bb8b-d4e34fc7cc61"}, "vulDataId": "c175913a-f940-421d-bb8b-d4e34fc7cc61", "auditCount": 0, "count": 483, "riskId": 5, "children": [], "vulDataName": "不应更改函数形参", "vulDataCnName": "不应更改函数形参"}, {"label": "不应该使用语言扩展(1)", "data": {"severity": "建议", "vulcatName": "不应该使用语言扩展", "vulDataId": "c4c1fb65-d5d0-4aab-8dfc-96dd23480749"}, "vulDataId": "c4c1fb65-d5d0-4aab-8dfc-96dd23480749", "auditCount": 0, "count": 1, "riskId": 5, "children": [], "vulDataName": "不应该使用语言扩展", "vulDataCnName": "不应该使用语言扩展"}, {"label": "函数类型应为带有命名形参的原型形式(103)", "data": {"severity": "建议", "vulcatName": "函数类型应为带有命名形参的原型形式", "vulDataId": "c53abba2-88ed-45ba-9c78-0edd2d6ac382"}, "vulDataId": "c53abba2-88ed-45ba-9c78-0edd2d6ac382", "auditCount": 0, "count": 103, "riskId": 5, "children": [], "vulDataName": "函数类型应为带有命名形参的原型形式", "vulDataCnName": "函数类型应为带有命名形参的原型形式"}, {"label": "循环语句和选择语句的主体应为复合语句(309)", "data": {"severity": "建议", "vulcatName": "循环语句和选择语句的主体应为复合语句", "vulDataId": "c6efae8f-8320-4030-a8c8-ca02f0c824e6"}, "vulDataId": "c6efae8f-8320-4030-a8c8-ca02f0c824e6", "auditCount": 0, "count": 309, "riskId": 5, "children": [], "vulDataName": "循环语句和选择语句的主体应为复合语句", "vulDataCnName": "循环语句和选择语句的主体应为复合语句"}, {"label": "初始化程序列表不得包含持久性副作用(2)", "data": {"severity": "建议", "vulcatName": "初始化程序列表不得包含持久性副作用", "vulDataId": "c8ac472c-6916-47fb-9106-cd1e09185661"}, "vulDataId": "c8ac472c-6916-47fb-9106-cd1e09185661", "auditCount": 0, "count": 2, "riskId": 5, "children": [], "vulDataName": "初始化程序列表不得包含持久性副作用", "vulDataCnName": "初始化程序列表不得包含持久性副作用"}, {"label": "全局(external linkage)对象和函数的标识符应是唯一的(3)", "data": {"severity": "建议", "vulcatName": "全局(external linkage)对象和函数的标识符应是唯一的", "vulDataId": "ca8183f1-8e49-40f6-acfc-3ac202a2432f"}, "vulDataId": "ca8183f1-8e49-40f6-acfc-3ac202a2432f", "auditCount": 0, "count": 3, "riskId": 5, "children": [], "vulDataName": "全局(external linkage)对象和函数的标识符应是唯一的", "vulDataCnName": "全局(external linkage)对象和函数的标识符应是唯一的"}, {"label": "函数中不应有未使用的变量(252)", "data": {"severity": "建议", "vulcatName": "函数中不应有未使用的变量", "vulDataId": "cf78109e-8293-4eb1-9a2c-6720d668e90a"}, "vulDataId": "cf78109e-8293-4eb1-9a2c-6720d668e90a", "auditCount": 0, "count": 252, "riskId": 5, "children": [], "vulDataName": "函数中不应有未使用的变量", "vulDataCnName": "函数中不应有未使用的变量"}, {"label": "宏标识符不得重名(4)", "data": {"severity": "建议", "vulcatName": "宏标识符不得重名", "vulDataId": "d131e9ba-c56a-4304-86fd-939127c25e1e"}, "vulDataId": "d131e9ba-c56a-4304-86fd-939127c25e1e", "auditCount": 0, "count": 4, "riskId": 5, "children": [], "vulDataName": "宏标识符不得重名", "vulDataCnName": "宏标识符不得重名"}, {"label": "每一个switch 子句(switch-clause)都应以无条件break 语句终止(979)", "data": {"severity": "建议", "vulcatName": "每一个switch 子句(switch-clause)都应以无条件break 语句终止", "vulDataId": "d7e9ec5a-7050-489b-8a86-a360e29dfcaf"}, "vulDataId": "d7e9ec5a-7050-489b-8a86-a360e29dfcaf", "auditCount": 0, "count": 979, "riskId": 5, "children": [], "vulDataName": "每一个switch 子句(switch-clause)都应以无条件break 语句终止", "vulDataCnName": "每一个switch 子句(switch-clause)都应以无条件break 语句终止"}, {"label": "不应使用“#”和“##”预处理运算符(7)", "data": {"severity": "建议", "vulcatName": "不应使用“#”和“##”预处理运算符", "vulDataId": "dacdad2c-e27d-4aad-8483-4a32cf1ff393"}, "vulDataId": "dacdad2c-e27d-4aad-8483-4a32cf1ff393", "auditCount": 0, "count": 7, "riskId": 5, "children": [], "vulDataName": "不应使用“#”和“##”预处理运算符", "vulDataCnName": "不应使用“#”和“##”预处理运算符"}, {"label": "Switch 语句应格式正确(3)", "data": {"severity": "建议", "vulcatName": "Switch 语句应格式正确", "vulDataId": "dc22914f-6e1d-477d-9a6d-17b0830e6d25"}, "vulDataId": "dc22914f-6e1d-477d-9a6d-17b0830e6d25", "auditCount": 0, "count": 3, "riskId": 5, "children": [], "vulDataName": "Switch 语句应格式正确", "vulDataCnName": "Switch 语句应格式正确"}, {"label": "if 语句和循环语句的控制表达式的基本类型应为布尔型(186)", "data": {"severity": "建议", "vulcatName": "if 语句和循环语句的控制表达式的基本类型应为布尔型", "vulDataId": "dd8b4da0-7308-4e4f-935b-66087e348b9b"}, "vulDataId": "dd8b4da0-7308-4e4f-935b-66087e348b9b", "auditCount": 0, "count": 186, "riskId": 5, "children": [], "vulDataName": "if 语句和循环语句的控制表达式的基本类型应为布尔型", "vulDataCnName": "if 语句和循环语句的控制表达式的基本类型应为布尔型"}, {"label": "不应使用#undef(11)", "data": {"severity": "建议", "vulcatName": "不应使用#undef", "vulDataId": "dfd1b9da-6b3a-4344-8f4b-5e29ed6916ec"}, "vulDataId": "dfd1b9da-6b3a-4344-8f4b-5e29ed6916ec", "auditCount": 0, "count": 11, "riskId": 5, "children": [], "vulDataName": "不应使用#undef", "vulDataCnName": "不应使用#undef"}, {"label": "无法找到指定的include文件(123)", "data": {"severity": "建议", "vulcatName": "无法找到指定的include文件", "vulDataId": "e65f6f9f-fe74-46f7-94e3-69691d5bcd72"}, "vulDataId": "e65f6f9f-fe74-46f7-94e3-69691d5bcd72", "auditCount": 0, "count": 123, "riskId": 5, "children": [], "vulDataName": "无法找到指定的include文件", "vulDataCnName": "无法找到指定的include文件"}, {"label": "项目不应包含未被使用的类型标签(tag)声明(41)", "data": {"severity": "建议", "vulcatName": "项目不应包含未被使用的类型标签(tag)声明", "vulDataId": "e68ebca9-0527-44cb-b8b0-89d3eeb3f701"}, "vulDataId": "e68ebca9-0527-44cb-b8b0-89d3eeb3f701", "auditCount": 0, "count": 41, "riskId": 5, "children": [], "vulDataName": "项目不应包含未被使用的类型标签(tag)声明", "vulDataCnName": "项目不应包含未被使用的类型标签(tag)声明"}, {"label": "宏“NULL”是整数型空指针常量的唯一允许形式(11)", "data": {"severity": "建议", "vulcatName": "宏“NULL”是整数型空指针常量的唯一允许形式", "vulDataId": "e7a53c2b-31c7-492c-ac03-869789cd3ed7"}, "vulDataId": "e7a53c2b-31c7-492c-ac03-869789cd3ed7", "auditCount": 0, "count": 11, "riskId": 5, "children": [], "vulDataName": "宏“NULL”是整数型空指针常量的唯一允许形式", "vulDataCnName": "宏“NULL”是整数型空指针常量的唯一允许形式"}, {"label": "字符类型的表达式不得在加减运算中使用不当(1)", "data": {"severity": "建议", "vulcatName": "字符类型的表达式不得在加减运算中使用不当", "vulDataId": "eb663ec9-9002-4992-bbad-4bd438d5f6c0"}, "vulDataId": "eb663ec9-9002-4992-bbad-4bd438d5f6c0", "auditCount": 0, "count": 1, "riskId": 5, "children": [], "vulDataName": "字符类型的表达式不得在加减运算中使用不当", "vulDataCnName": "字符类型的表达式不得在加减运算中使用不当"}, {"label": "重复的break语句(1)", "data": {"severity": "建议", "vulcatName": "重复的break语句", "vulDataId": "eca46ceb-a4d6-4252-a47e-6e460c964b1d"}, "vulDataId": "eca46ceb-a4d6-4252-a47e-6e460c964b1d", "auditCount": 0, "count": 1, "riskId": 5, "children": [], "vulDataName": "重复的break语句", "vulDataCnName": "重复的break语句"}, {"label": "#if 或#elif 预处理指令的控制表达式中使用的所有标识符应在其评估前被#define 定义(1)", "data": {"severity": "建议", "vulcatName": "#if 或#elif 预处理指令的控制表达式中使用的所有标识符应在其评估前被#define 定义", "vulDataId": "ed28c4c7-3157-4937-96cc-e59a6ee4eb16"}, "vulDataId": "ed28c4c7-3157-4937-96cc-e59a6ee4eb16", "auditCount": 0, "count": 1, "riskId": 5, "children": [], "vulDataName": "#if 或#elif 预处理指令的控制表达式中使用的所有标识符应在其评估前被#define 定义", "vulDataCnName": "#if 或#elif 预处理指令的控制表达式中使用的所有标识符应在其评估前被#define 定义"}, {"label": "全局(external linkage)的对象和函数，应有显式的合规的声明(184)", "data": {"severity": "建议", "vulcatName": "全局(external linkage)的对象和函数，应有显式的合规的声明", "vulDataId": "f395b31b-5c26-44e7-99ec-19e821676dd3"}, "vulDataId": "f395b31b-5c26-44e7-99ec-19e821676dd3", "auditCount": 0, "count": 184, "riskId": 5, "children": [], "vulDataName": "全局(external linkage)的对象和函数，应有显式的合规的声明", "vulDataCnName": "全局(external linkage)的对象和函数，应有显式的合规的声明"}, {"label": "查找未使用的goto标签(1)", "data": {"severity": "建议", "vulcatName": "查找未使用的goto标签", "vulDataId": "f8b33d33-f703-46b4-8120-20131172b435"}, "vulDataId": "f8b33d33-f703-46b4-8120-20131172b435", "auditCount": 0, "count": 1, "riskId": 5, "children": [], "vulDataName": "查找未使用的goto标签", "vulDataCnName": "查找未使用的goto标签"}]}]}, {"label": "HTML(2)", "auditCount": 0, "count": 2, "data": {"isTop": true}, "children": [{"label": "中危(2)", "auditCount": 0, "count": 2, "children": [{"label": "跨站请求伪造(2)", "data": {"severity": "中危", "vulcatName": "跨站请求伪造", "vulDataId": "9704f083-5790-49c0-bc12-b4216a084773"}, "vulDataId": "9704f083-5790-49c0-bc12-b4216a084773", "auditCount": 0, "count": 2, "riskId": 3, "children": [], "vulDataName": "跨站请求伪造", "vulDataCnName": "跨站请求伪造"}]}]}, {"label": "Go(282)", "auditCount": 0, "count": 282, "data": {"isTop": true}, "children": [{"label": "超危(20)", "auditCount": 0, "count": 20, "children": [{"label": "命令行注入(20)", "data": {"severity": "超危", "vulcatName": "命令行注入", "vulDataId": "a76babe0-d3c7-4f2e-aa88-b0c47850e087"}, "vulDataId": "a76babe0-d3c7-4f2e-aa88-b0c47850e087", "auditCount": 0, "count": 20, "riskId": 1, "children": [], "vulDataName": "命令行注入", "vulDataCnName": "命令行注入"}]}, {"label": "高危(34)", "auditCount": 0, "count": 34, "children": [{"label": "TLS版本过低(1)", "data": {"severity": "高危", "vulcatName": "TLS版本过低", "vulDataId": "2c5f08f2-e32a-4c73-a48a-c956a30c36be"}, "vulDataId": "2c5f08f2-e32a-4c73-a48a-c956a30c36be", "auditCount": 0, "count": 1, "riskId": 2, "children": [], "vulDataName": "TLS版本过低", "vulDataCnName": "TLS版本过低"}, {"label": "/debug/pprof 上暴露的剖析端点会泄露敏感信息(1)", "data": {"severity": "高危", "vulcatName": "/debug/pprof 上暴露的剖析端点会泄露敏感信息", "vulDataId": "abff447d-0c61-4cdf-9fa8-d5592ef5f1af"}, "vulDataId": "abff447d-0c61-4cdf-9fa8-d5592ef5f1af", "auditCount": 0, "count": 1, "riskId": 2, "children": [], "vulDataName": "/debug/pprof 上暴露的剖析端点会泄露敏感信息", "vulDataCnName": "/debug/pprof 上暴露的剖析端点会泄露敏感信息"}, {"label": "潜在的文件路径遍历(32)", "data": {"severity": "高危", "vulcatName": "潜在的文件路径遍历", "vulDataId": "b29ecc27-1a22-4da1-bbf6-553edffffea4"}, "vulDataId": "b29ecc27-1a22-4da1-bbf6-553edffffea4", "auditCount": 0, "count": 32, "riskId": 2, "children": [], "vulDataName": "潜在的文件路径遍历", "vulDataCnName": "潜在的文件路径遍历"}]}, {"label": "中危(27)", "auditCount": 0, "count": 27, "children": [{"label": "不安全的MD5加密方法(4)", "data": {"severity": "中危", "vulcatName": "不安全的MD5加密方法", "vulDataId": "6a69cf45-be03-4244-8004-efd0a90577f8"}, "vulDataId": "6a69cf45-be03-4244-8004-efd0a90577f8", "auditCount": 0, "count": 4, "riskId": 3, "children": [], "vulDataName": "不安全的MD5加密方法", "vulDataCnName": "不安全的MD5加密方法"}, {"label": "使用不安全的伪随机数生成器(2)", "data": {"severity": "中危", "vulcatName": "使用不安全的伪随机数生成器", "vulDataId": "76f16936-c580-40b2-af18-91f9c4ba50c4"}, "vulDataId": "76f16936-c580-40b2-af18-91f9c4ba50c4", "auditCount": 0, "count": 2, "riskId": 3, "children": [], "vulDataName": "使用不安全的伪随机数生成器", "vulDataCnName": "使用不安全的伪随机数生成器"}, {"label": "不安全的SHA1加密方法(1)", "data": {"severity": "中危", "vulcatName": "不安全的SHA1加密方法", "vulDataId": "8803ad78-4a2c-45e1-99b8-ef19557f6f25"}, "vulDataId": "8803ad78-4a2c-45e1-99b8-ef19557f6f25", "auditCount": 0, "count": 1, "riskId": 3, "children": [], "vulDataName": "不安全的SHA1加密方法", "vulDataCnName": "不安全的SHA1加密方法"}, {"label": "通过解压炸弹实现的潜在DoS攻击(4)", "data": {"severity": "中危", "vulcatName": "通过解压炸弹实现的潜在DoS攻击", "vulDataId": "b394de1b-6856-4ec8-86bf-30d2a6a8e1da"}, "vulDataId": "b394de1b-6856-4ec8-86bf-30d2a6a8e1da", "auditCount": 0, "count": 4, "riskId": 3, "children": [], "vulDataName": "通过解压炸弹实现的潜在DoS攻击", "vulDataCnName": "通过解压炸弹实现的潜在DoS攻击"}, {"label": "在 range 语句中使用隐式的元素别名(2)", "data": {"severity": "中危", "vulcatName": "在 range 语句中使用隐式的元素别名", "vulDataId": "b945d6d9-0b23-4cc0-a9de-7cfa1ca680a4"}, "vulDataId": "b945d6d9-0b23-4cc0-a9de-7cfa1ca680a4", "auditCount": 0, "count": 2, "riskId": 3, "children": [], "vulDataName": "在 range 语句中使用隐式的元素别名", "vulDataCnName": "在 range 语句中使用隐式的元素别名"}, {"label": "使用易破解的编码加密密码(7)", "data": {"severity": "中危", "vulcatName": "使用易破解的编码加密密码", "vulDataId": "e8d67f70-83fe-4ca0-9252-10e78a333069"}, "vulDataId": "e8d67f70-83fe-4ca0-9252-10e78a333069", "auditCount": 0, "count": 7, "riskId": 3, "children": [], "vulDataName": "使用易破解的编码加密密码", "vulDataCnName": "使用易破解的编码加密密码"}, {"label": "提取 zip/tar 文档时未检查 zip/tar  条目的文件路径(7)", "data": {"severity": "中危", "vulcatName": "提取 zip/tar 文档时未检查 zip/tar  条目的文件路径", "vulDataId": "fe254942-89bf-43c2-be5a-4f829a4d6045"}, "vulDataId": "fe254942-89bf-43c2-be5a-4f829a4d6045", "auditCount": 0, "count": 7, "riskId": 3, "children": [], "vulDataName": "提取 zip/tar 文档时未检查 zip/tar  条目的文件路径", "vulDataCnName": "提取 zip/tar 文档时未检查 zip/tar  条目的文件路径"}]}, {"label": "低危(201)", "auditCount": 0, "count": 201, "children": [{"label": "错误未处理(197)", "data": {"severity": "低危", "vulcatName": "错误未处理", "vulDataId": "448512ff-8c18-4cdd-9753-1bb070db0c1f"}, "vulDataId": "448512ff-8c18-4cdd-9753-1bb070db0c1f", "auditCount": 0, "count": 197, "riskId": 4, "children": [], "vulDataName": "错误未处理", "vulDataCnName": "错误未处理"}, {"label": "应审计 unsafe 的使用(4)", "data": {"severity": "低危", "vulcatName": "应审计 unsafe 的使用", "vulDataId": "d9d169a4-3826-4fd7-9ed3-a4ec07b5aae5"}, "vulDataId": "d9d169a4-3826-4fd7-9ed3-a4ec07b5aae5", "auditCount": 0, "count": 4, "riskId": 4, "children": [], "vulDataName": "应审计 unsafe 的使用", "vulDataCnName": "应审计 unsafe 的使用"}]}]}], "qualityWeaknessNum": 0, "statisticalCodelist": [{"id": 123, "codeLanguageName": "Java/Jsp", "codeLineNum": 87, "commentLines": 2, "blankLines": 10, "cyclomaticComplexityNum": 0, "repetitiveLines": 0, "fileNum": 1, "fileSize": 0.0, "codeRate": 87.88}, {"id": 125, "codeLanguageName": "C/C++", "codeLineNum": 25769, "commentLines": 5098, "blankLines": 3181, "cyclomaticComplexityNum": 0, "repetitiveLines": 0, "fileNum": 55, "fileSize": 0.0, "codeRate": 75.68}, {"id": 126, "codeLanguageName": "HTML", "codeLineNum": 185, "commentLines": 0, "blankLines": 15, "cyclomaticComplexityNum": 0, "repetitiveLines": 0, "fileNum": 2, "fileSize": 0.0, "codeRate": 92.5}, {"id": 127, "codeLanguageName": "Swift", "codeLineNum": 6485, "commentLines": 45, "blankLines": 1212, "cyclomaticComplexityNum": 0, "repetitiveLines": 0, "fileNum": 62, "fileSize": 0.0, "codeRate": 83.76}, {"id": 124, "codeLanguageName": "Go", "codeLineNum": 23750, "commentLines": 2952, "blankLines": 4754, "cyclomaticComplexityNum": 0, "repetitiveLines": 0, "fileNum": 190, "fileSize": 0.0, "codeRate": 75.5}]}, "gitInfo": {"branch": "main-tz", "commitId": "", "extraMark": ""}}}