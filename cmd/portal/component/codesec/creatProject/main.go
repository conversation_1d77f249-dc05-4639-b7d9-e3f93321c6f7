package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"

	"gitlab.com/piccolo_su/vegeta/cmd/portal/utils"
)

// 定义请求结构体
type CreateProjectRequest struct {
	ProjectName          string           `json:"projectName"`
	Url                  string           `json:"url"`
	GitType              int              `json:"gitType"`              // git类型  1 : gitlab  2 : github  3 : gitee  6 : gerrit  7 : bitbucket
	UrlHead              int              `json:"urlHead,omitempty"`    // git地址是否以https开头 0：否    1：是
	AuthenticationMethod int              `json:"authenticationMethod"` // git 认证类型  0.用户名密码认证（默认）  1.token认证 2.SSH密钥  凭据认证
	Token                string           `json:"token"`
	ProjectDesc          string           `json:"projectDesc"`
	Branch               string           `json:"branch"` // 分支名
	SpecifiedMemberList  *SpecifiedMember `json:"specifiedMemberList,omitempty"`
}

type SpecifiedMember struct {
	UserID         string `json:"userId"`
	RoleID         int    `json:"roleId"`
	PermissionType int    `json:"permissionType,omitempty"`
}

// 定义响应结构体
type CreateProjectResponse struct {
	Status  bool   `json:"status"`
	Message string `json:"message"`
	Data    struct {
		ProjectUuid string `json:"projectUuid"`
		OrgUuid     string `json:"orgUuid"`
	} `json:"data"`
}

func CreateProject() {
	// 定义请求URL
	apiURL := "http://1.95.60.107:28081/cs/api/v4/project/createProjectByGitInfo"

	// 定义请求体
	requestBody := CreateProjectRequest{
		ProjectName: "liuqianli341",
		// Url:                  "ssh://*************************:30022/microseg-agent/agent.git",
		Url:                  "https://github.com/qianliout/algorithm.git",
		GitType:              2,
		UrlHead:              0,
		AuthenticationMethod: 1,
		Token:                "*********************************************************************************************",
		ProjectDesc:          "liuqianli",
		Branch:               "master",
		// SpecifiedMemberList: &SpecifiedMember{
		// 	UserID:         "",
		// 	RoleID:         0,
		// 	PermissionType: 0,
		// },
	}
	// singeData := "projectName=liuqianli&specifiedMemberList=roleId=3&userId=70b00b10-1fb5-4a71-a028-4df42d822ecf"
	// singeData := "projectName=liuqianli"

	// 将请求体转换为JSON格式
	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		fmt.Println("Error marshaling JSON:", err)
		return
	}
	fmt.Println(string(jsonData))
	data := map[string]interface{}{}
	_ = json.Unmarshal(jsonData, &data)

	// 创建HTTP请求
	req, err := http.NewRequest("POST", apiURL, bytes.NewBuffer(jsonData))
	if err != nil {
		fmt.Println("Error creating request:", err)
		return
	}
	// accessKey := "7b214572-4f1d-4630-ba73-12731ecfdf02"
	// openToken := "eyJhbGciOiJIUzUxMiJ9.eyJub25jZSI6IjlkNTlhZDMyLTBhMzItNDAyNC04YjQ0LTYyZDUyOGY0Yzc3YiIsInN1YiI6IjdiMjE0NTcyLTRmMWQtNDYzMC1iYTczLTEyNzMxZWNmZGYwMiJ9.MbTQcgp_lzRq6ejwstlLoOsfgZkkEbwSbF08CfSBNbDSpPosCHD8zF7-a-yiFE8j6P_E_I-u-Nb2JElW47zCJA"
	accessKey := "7b214572-4f1d-4630-ba73-12731ecfdf02"
	openToken := "eyJhbGciOiJIUzUxMiJ9.eyJub25jZSI6IjlkNTlhZDMyLTBhMzItNDAyNC04YjQ0LTYyZDUyOGY0Yzc3YiIsInN1YiI6IjdiMjE0NTcyLTRmMWQtNDYzMC1iYTczLTEyNzMxZWNmZGYwMiJ9.MbTQcgp_lzRq6ejwstlLoOsfgZkkEbwSbF08CfSBNbDSpPosCHD8zF7-a-yiFE8j6P_E_I-u-Nb2JElW47zCJA"
	header := utils.BuildV2Header(data, accessKey, openToken, nil)

	// 设置自定义请求头
	for k, v := range header {
		req.Header.Set(k, v)
	}
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Println("Error sending request:", err)
		return
	}
	defer resp.Body.Close()

	// 解析响应体
	var response CreateProjectResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		fmt.Println("Error sending request:", err)
	}
	marshal, _ := json.Marshal(response)
	fmt.Println(string(marshal))

	// 输出响应结果
	fmt.Printf("Status: %v\n", response.Status)
	fmt.Printf("Message: %s\n", response.Message)
	fmt.Printf("Project UUID: %s\n", response.Data.ProjectUuid)
	fmt.Printf("Team UUID: %s\n", response.Data.OrgUuid)
}

func main() {
	CreateProject()
}
