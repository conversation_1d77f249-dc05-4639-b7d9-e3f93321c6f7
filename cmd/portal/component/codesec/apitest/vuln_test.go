package apitest

import (
	"testing"

	"github.com/smartystreets/goconvey/convey"
	"gitlab.com/piccolo_su/vegeta/cmd/portal/component/codesec/scan"
)

// TestGetAllVuln 测试GetAllVuln方法
func TestGetAllVuln(t *testing.T) {
	convey.Convey("TestGetAllVuln", t, func() {
		convey.Convey("应该能够成功获取所有漏洞", func() {
			// 跳过测试如果环境变量未设置
			if ProjectUuid == "" || TaskId == "" {
				convey.Printf("跳过测试，因为环境变量未设置: ProjectUuid=%s, TaskId=%s", ProjectUuid, TaskId)
				return
			}

			// 创建服务实例 - 使用环境变量，传入nil作为scanDal和projectDal参数（仅用于API测试）
			service := scan.NewScanAPISer(BaseURL, AccessKey, AccessSecret, nil, nil)

			// 调用GetAllVuln方法
			vulns, scr, err := service.GetAllVuln(ProjectUuid, TaskId)

			// 验证结果
			convey.So(err, convey.ShouldBeNil)
			convey.So(vulns, convey.ShouldNotBeNil)
			convey.So(len(vulns), convey.ShouldBeGreaterThan, 0)
			convey.So(scr, convey.ShouldNotBeNil)
		})
	})
}

// TestScanSubProject 测试ScanSubProject方法
func TestScanSubProject(t *testing.T) {
	convey.Convey("TestScanSubProject", t, func() {
		convey.Convey("应该能够成功发起扫描任务", func() {
			// 跳过测试如果环境变量未设置
			if ProjectUuid == "" || TaskId == "" {
				convey.Printf("跳过测试，因为环境变量未设置: ProjectUuid=%s, TaskId=%s", ProjectUuid, TaskId)
				return
			}

			// 创建服务实例 - 传入nil作为scanDal和projectDal参数（仅用于API测试）
			service := scan.NewScanAPISer(BaseURL, AccessKey, AccessSecret, nil, nil)

			// 调用ScanSubProject方法
			response, err := service.ScanSubProject(ProjectUuid, TaskId)

			// 验证结果
			convey.So(err, convey.ShouldBeNil)
			convey.So(response, convey.ShouldNotBeNil)
			convey.So(response.Status, convey.ShouldBeTrue)
			convey.So(response.Data.AppId, convey.ShouldNotBeEmpty)
			convey.So(response.Data.RecordId, convey.ShouldNotBeEmpty)

			// 验证返回的AppId应该与请求的TaskId一致
			convey.So(response.Data.AppId, convey.ShouldEqual, TaskId)
		})

		convey.Convey("当项目UUID为空时应该返回错误", func() {
			service := scan.NewScanAPISer(BaseURL, AccessKey, AccessSecret, nil, nil)

			response, err := service.ScanSubProject("", "test-task-id")

			convey.So(err, convey.ShouldNotBeNil)
			convey.So(response, convey.ShouldBeNil)
			convey.So(err.Error(), convey.ShouldContainSubstring, "projectUuid cannot be empty")
		})

		convey.Convey("当任务ID为空时应该返回错误", func() {
			service := scan.NewScanAPISer(BaseURL, AccessKey, AccessSecret, nil, nil)

			response, err := service.ScanSubProject("test-project-uuid", "")

			convey.So(err, convey.ShouldNotBeNil)
			convey.So(response, convey.ShouldBeNil)
			convey.So(err.Error(), convey.ShouldContainSubstring, "appId cannot be empty")
		})
	})
}

// TestGetScanProgress 测试GetScanProgress方法
func TestGetScanProgress(t *testing.T) {
	convey.Convey("TestGetScanProgress", t, func() {
		convey.Convey("应该能够成功调用查询扫描进度接口", func() {
			// 跳过测试如果环境变量未设置
			if ProjectUuid == "" || TaskId == "" {
				convey.Printf("跳过测试，因为环境变量未设置: ProjectUuid=%s, TaskId=%s", ProjectUuid, TaskId)
				return
			}

			// 创建服务实例 - 传入nil作为scanDal和projectDal参数（仅用于API测试）
			service := scan.NewScanAPISer(BaseURL, AccessKey, AccessSecret, nil, nil)

			// 调用GetScanProgress方法
			response, err := service.GetScanProgress(ProjectUuid, TaskId)

			// 验证结果 - 即使API返回业务错误，我们的实现也应该正确处理
			if err != nil {
				// 如果是API业务错误（如"失败"），这是正常的
				convey.Printf("API调用返回业务错误（这是正常的）: %v", err)
				convey.So(err.Error(), convey.ShouldContainSubstring, "API error:")
			} else {
				// 如果成功，验证响应结构
				convey.So(response, convey.ShouldNotBeNil)
				convey.So(response.Progress, convey.ShouldBeGreaterThanOrEqualTo, 0)
				convey.So(response.Progress, convey.ShouldBeLessThanOrEqualTo, 100)
			}
		})

		convey.Convey("当项目UUID为空时应该返回错误", func() {
			service := scan.NewScanAPISer(BaseURL, AccessKey, AccessSecret, nil, nil)

			response, err := service.GetScanProgress("", "test-task-id")

			convey.So(err, convey.ShouldNotBeNil)
			convey.So(response, convey.ShouldBeNil)
			convey.So(err.Error(), convey.ShouldContainSubstring, "projectUuid cannot be empty")
		})

		convey.Convey("当任务ID为空时应该返回错误", func() {
			service := scan.NewScanAPISer(BaseURL, AccessKey, AccessSecret, nil, nil)

			response, err := service.GetScanProgress("test-project-uuid", "")

			convey.So(err, convey.ShouldNotBeNil)
			convey.So(response, convey.ShouldBeNil)
			convey.So(err.Error(), convey.ShouldContainSubstring, "appId cannot be empty")
		})
	})
}
