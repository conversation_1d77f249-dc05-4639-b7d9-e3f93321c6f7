package apitest

import (
	"fmt"
	"testing"

	"github.com/smartystreets/goconvey/convey"
	
)

func TestSignatureComparison(t *testing.T) {
	convey.Convey("测试签名算法", t, func() {
		// 测试用例：模拟Java示例中的getListDetailByVulDataId调用
		accessKey := "fb9d6032-e3ff-4357-8c42-18a17fd855bf"
		accessSecret := "eyJhbGciOiJIUzUxMiJ9.eyJub25jZSI6IjY1ODYzMjQwLTYyMTYtNGJiOS1iOTZlLWI4Mjg4ZTZmYjAwMCIsInN1YiI6ImZiOWQ2MDMyLWUzZmYtNDM1Seven8YzQyLTE4YTE3ZmQ4NTViZiJ9.oq-dRoku8CGiMWGZIEj7aIEeKRiPVdpTW717iFTI69gmFKN54d0AtMPUgdEjiZZkDp2ONCb59924WLN2kR9w7w"

		// 模拟Java示例的URL和参数
		baseURL := "http://localhost:28081"
		projectUuid := "68aa740c-3f50-42a5-a550-d8b54270c615"
		appId := "11d4d19f-56ae-486c-b307-5ec0361569a3"

		// 构建完整URL（包含查询参数）
		fullURL := fmt.Sprintf("%s/cs/api/v2/project/%s/task/%s/getListDetailByVulDataId?vulDataId=9f8d4537-b9c1-4224-a29a-11433d525217&recordId=c38e7670-ef4e-429c-a3f4-ea9b0b86b31f&pageCurrent=1&pageSize=10",
			baseURL, projectUuid, appId)

		convey.Convey("测试URL路径提取", func() {
			extractedPath := utils.ExtractURLPath(fullURL)
			fmt.Printf("提取的URL路径: %s\n", extractedPath)

			// 根据Java版本的逻辑，对于/project/{uuid}/task/{uuid}/xxx的模式
			// 应该提取位置2和4的参数，即projectUuid和appId
			expectedPath := "&" + projectUuid + "&" + appId
			convey.So(extractedPath, convey.ShouldEqual, expectedPath)
		})

		convey.Convey("测试签名生成", func() {
			// 空的params，因为参数都在URL中
			params := map[string]interface{}{}

			headers := utils.BuildV2HeaderWithURL(fullURL, params, accessKey, accessSecret)

			fmt.Printf("生成的请求头:\n")
			for k, v := range headers {
				fmt.Printf("%s: %s\n", k, v)
			}

			convey.So(headers["accessKey"], convey.ShouldEqual, accessKey)
			convey.So(headers["x-cs-timestamp"], convey.ShouldNotBeEmpty)
			convey.So(headers["x-cs-nonce"], convey.ShouldNotBeEmpty)
			convey.So(headers["x-cs-signature"], convey.ShouldNotBeEmpty)
		})

		convey.Convey("测试简单URL签名", func() {
			// 测试一个简单的URL，不包含查询参数
			simpleURL := fmt.Sprintf("%s/cs/api/v2/project/%s/task/%s/scanSubProject",
				baseURL, projectUuid, appId)

			params := map[string]interface{}{}
			headers := utils.BuildV2HeaderWithURL(simpleURL, params, accessKey, accessSecret)

			fmt.Printf("简单URL签名结果:\n")
			for k, v := range headers {
				fmt.Printf("%s: %s\n", k, v)
			}

			convey.So(headers["x-cs-signature"], convey.ShouldNotBeEmpty)
		})
	})
}
