package apitest

import (
	"os"
)

// getEnvOrDefault 获取环境变量，如果不存在则返回默认值
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// 测试配置常量 - 从环境变量读取，避免敏感信息暴露
var (
	BaseURL      = getEnvOrDefault("CODESEC_BASE_URL", "http://localhost:28081")
	ProjectUuid  = getEnvOrDefault("CODESEC_PROJECT_UUID", "")
	TaskId       = getEnvOrDefault("CODESEC_TASK_ID", "") // 有些地方也叫 appID
	RecordId     = getEnvOrDefault("CODESEC_RECORD_ID", "")
	VulDataId    = getEnvOrDefault("CODESEC_VUL_DATA_ID", "")
	AccessSecret = getEnvOrDefault("CODESEC_ACCESS_SECRET", "")
	AccessKey    = getEnvOrDefault("CODESEC_ACCESS_KEY", "")
)
