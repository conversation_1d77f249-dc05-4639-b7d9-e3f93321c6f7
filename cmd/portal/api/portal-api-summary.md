# Portal API 完整接口文档

## 概述
Portal项目的完整API接口文档，包含用户管理、项目管理、扫描结果、导出任务、全局常量等所有接口。

## 接口列表

### 用户管理接口 (/api/v1/user)

| 方法 | 路径 | 描述 | 认证 |
|------|------|------|------|
| POST | /api/v1/user/register | 用户注册 | ✓ |
| POST | /api/v1/user/login | 用户登录 | ✗ |
| PUT | /api/v1/user/update | 更新用户信息 | ✓ |
| GET | /api/v1/user/detail | 获取用户详情 | ✓ |
| GET | /api/v1/user/list | 搜索用户列表 | ✓ |
| GET | /api/v1/user/token | 获取用户认证码 | ✓ |
| DELETE | /api/v1/user/delete | 删除用户 | ✓ |
| PUT | /api/v1/user/changePwd | 修改密码 | ✓ |
| PUT | /api/v1/user/resetPwd | 重置密码 | ✓ |
| POST | /api/v1/user/checkPasswd | 验证密码 | ✓ |
| GET | /api/v1/user/authCode | 获取认证码 | ✗ |

### 项目管理接口 (/api/v1/projects)

| 方法 | 路径 | 描述 | 认证 |
|------|------|------|------|
| GET | /api/v1/projects/list | 搜索项目列表 | ✓ |
| GET | /api/v1/projects/detail | 获取项目详情 | ✓ |
| POST | /api/v1/projects/create | 创建项目 | ✓ |
| PUT | /api/v1/projects/update | 更新项目 | ✓ |
| DELETE | /api/v1/projects/delete | 删除项目 | ✓ |

### 扫描结果接口 (/api/v1/scan)

| 方法 | 路径 | 描述 | 认证 |
|------|------|------|------|
| GET | /api/v1/scan/vuln/list/codesec | 查询CodeSec漏洞列表 | ✓ |
| GET | /api/v1/scan/summary/codesec | CodeSec扫描汇总 | ✓ |
| GET | /api/v1/scan/summary/sourceCheck | SourceCheck扫描汇总 | ✓ |
| GET | /api/v1/scan/vuln/list/sourceCheck | 查询SourceCheck漏洞列表 | ✓ |
| GET | /api/v1/scan/license/list/sourceCheck | 查询SourceCheck许可列表 | ✓ |
| GET | /api/v1/scan/component/list/sourceCheck | 查询SourceCheck组件列表 | ✓ |
| GET | /api/v1/scan/vuln/list/ci | 查询CI漏洞列表 | ✓ |
| GET | /api/v1/scan/image/list/ci | 查询CI镜像列表 | ✓ |
| POST | /api/v1/scan/startScan | 启动扫描 | ✓ |

### 导出任务接口 (/api/v1/export/task)

| 方法 | 路径 | 描述 | 认证 |
|------|------|------|------|
| GET | /api/v1/export/task/list | 查询导出任务列表 | ✓ |
| GET | /api/v1/export/task/download | 下载文件 | ✓ |
| POST | /api/v1/export/task/createTask | 创建导出任务 | ✓ |

### 全局常量接口 (/api/v1/global)

| 方法 | 路径 | 描述 | 认证 |
|------|------|------|------|
| GET | /api/v1/global/consts/list | 获取常量列表 | ✗ |

## 数据模型

### 用户相关模型

#### User - 用户数据模型
```json
{
  "id": "integer - 用户ID",
  "name": "string - 用户名",
  "portalEmail": "string - 登录邮箱",
  "pwdString": "string - 明文密码",
  "role": "string - 用户角色",
  "mobile": "string - 手机号",
  "comment": "string - 备注信息",
  "status": "string - 账号状态",
  "sourceCheckToken": "string - sourceCheck的访问令牌",
  "sourceCheckEmail": "string - sourceCheck的登录邮箱",
  "codesecAk": "string - codesec的UserUuid",
  "codesecSk": "string - codesec的AccessSecret",
  "codesecEmail": "string - codesec的登录邮箱",
  "tensorEmail": "string - tensor的登录邮箱",
  "tensorPwd": "string - tensor的登录密码",
  "createdAt": "integer - 创建时间（毫秒时间戳）",
  "updatedAt": "integer - 更新时间（毫秒时间戳）"
}
```

#### LoginResponse - 登录响应数据模型
```json
{
  "id": "integer - 用户ID",
  "portalEmail": "string - 登录邮箱",
  "token": "string - JWT token"
}
```

#### AuthCode - 认证码数据模型
```json
{
  "authCode": "string - 授权码",
  "userID": "integer - 用户ID"
}
```

### 项目相关模型

#### Project - 项目数据模型
```json
{
  "id": "integer - 项目ID",
  "uuid": "string - 项目标识符",
  "name": "string - 项目名称",
  "url": "string - 仓库地址",
  "userId": "integer - 用户ID",
  "description": "string - 项目描述",
  "gitType": "string - 仓库类型",
  "token": "string - Token",
  "branch": "string - 分支名",
  "tag": "string - tag名",
  "branchTag": "string - 分支名和tag名",
  "codesecScanStatus": "string - codesec扫描状态",
  "codesecScanMsg": "string - codesec扫描消息",
  "sourceCheckScanStatus": "string - sourcecheck扫描状态",
  "sourceCheckScanMsg": "string - sourcecheck扫描消息",
  "riskLevel": "string - 项目风险级别：高、中、低、未知",
  "codesecHighRisk": "string - codesec最高风险等级",
  "codesecHighSeverity": "integer - codesec最高风险等级",
  "sourceCheckHighRisk": "string - sourcecheck最高风险等级",
  "sourceCheckHighSeverity": "integer - sourcecheck最高风险等级",
  "lastScanAt": "integer - 最近扫描时间",
  "updater": "string - 最近一次更新人",
  "createdAt": "integer - 创建时间",
  "updatedAt": "integer - 更新时间"
}
```

### 扫描结果相关模型

#### CodeSecVuln - CodeSec漏洞数据模型
```json
{
  "id": "integer - 漏洞ID",
  "uniqueID": "string - 唯一标识ID",
  "projectUuid": "string - 项目UUID",
  "codesecAppId": "string - CodeSec应用ID",
  "vulName": "string - 漏洞名称",
  "severity": "string - 风险等级",
  "filename": "string - 文件名",
  "rowNum": "string - 文件行数",
  "vulFlag": "string - 缺陷跟踪类型"
}
```

#### SourceCheckVuln - SourceCheck漏洞数据模型
```json
{
  "id": "integer - 漏洞ID",
  "projectUuid": "string - 项目UUID",
  "uniqueID": "string - 唯一标识ID",
  "sourceCheckUuid": "string - SourceCheck应用UUID",
  "customCveNo": "string - CVE编号",
  "severity": "string - 风险等级描述",
  "cweName": "string - 弱点类型名称",
  "affectComponentCount": "integer - 影响组件数",
  "description": "string - 漏洞描述"
}
```

#### SourceCheckComponent - SourceCheck组件数据模型
```json
{
  "id": "integer - 组件ID",
  "projectUuid": "string - 项目UUID",
  "uniqueID": "string - 唯一标识ID",
  "sourceCheckUuid": "string - SourceCheck应用UUID",
  "artifactId": "string - 组件名称",
  "severity": "string - 风险等级",
  "jarInfoAddFrom": "string - 语言字典值",
  "licenseIds": "array - 许可名称列表",
  "version": "string - 当前版本",
  "recommendVersion": "string - 推荐版本",
  "latestVersion": "string - 最新版本",
  "countryChineseName": "string - 所属国家的中文名称"
}
```

### 通用响应模型

#### StandardResponse - 标准响应数据模型
```json
{
  "apiVersion": "string - API版本",
  "data": {
    "status": "integer - 状态码，0表示成功"
  }
}
```

#### ListResponse - 列表响应数据模型
```json
{
  "apiVersion": "string - API版本",
  "data": {
    "status": "integer - 状态码，0表示成功",
    "items": "array - 数据列表",
    "totalItems": "integer - 总数量",
    "itemsPerPage": "integer - 每页数量",
    "startIndex": "integer - 起始索引"
  }
}
```

## 认证说明

大部分接口需要在请求头中携带Authorization token：
```
Authorization: Bearer <token>
```

## 状态码说明

- 200: 成功
- 400: 参数错误
- 401: 未授权
- 404: 资源不存在
- 500: 服务器内部错误

## 文件说明

- `portal-api-complete.json`: 完整的Apifox格式API文档，包含用户管理和项目管理接口
- `portal-api-part2.json`: 扫描结果相关接口文档
- `portal-api-part3.json`: 导出任务和全局常量接口文档
- `portal-api-summary.md`: 本文档，API接口摘要说明

## 使用说明

1. 将JSON文件导入到Apifox中使用
2. 所有接口都放在portal目录下，不再分其他子目录
3. 接口文档按功能模块分为多个文件，便于维护和使用
