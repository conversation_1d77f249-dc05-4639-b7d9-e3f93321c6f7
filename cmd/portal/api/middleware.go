package api

import (
	"github.com/gin-gonic/gin"
	"gitlab.com/piccolo_su/vegeta/cmd/portal/component/token"
	"gitlab.com/piccolo_su/vegeta/cmd/portal/store"
	imagesecModel "gitlab.com/piccolo_su/vegeta/pkg/model/imagesec"
	"gitlab.com/piccolo_su/vegeta/pkg/util"
)

func AddLanguage(ctx *gin.Context) {
	ctx.Set(imagesecModel.AcceptLanguage, util.GetLanguage(ctx))
}

func GenTokenAuthMiddleware(ud store.UserDal) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从请求头中获取 Token
		// tm := token.NewJWTTokenManager(ud)
		// to := c.GetHeader("Authorization")
		// if to == "" {
		// 	to = c.GetHeader("authorization")
		// }
		// to = strings.TrimLeft(to, "Bearer")
		// to = strings.TrimSpace(to)
		p := token.Payload{
			ID:               5,
			Mobile:           "15097114231",
			PortalEmail:      "<EMAIL>",
			Role:             "operator",
			Status:           "normal",
			SourceCheckToken: "************************************************************************",
			CodesecKey:       "1bc44feb-15dd-4178-a815-46e3ea87312e",
			CodesecSecret:    "eyJhbGciOiJIUzUxMiJ9.eyJub25jZSI6ImE5NTNkZDE1LTkzYjUtNGU4MC1hNjRjLTU5NWE4Y2RkMzFhNyIsInN1YiI6IjFiYzQ0ZmViLTE1ZGQtNDE3OC1hODE1LTQ2ZTNlYTg3MzEyZSJ9.RTtrKJUy9ixCS5uzXOyWtYmYTnROginIUvrWUgtbY7xBGYCsBNKksvDlmTQFkptf5SvJoxJcQ2hMAWLn2YhKyw",
		}

		// p, err := tm.Verify(to)
		// if err != nil {
		// 	c.JSON(http.StatusUnauthorized, gin.H{"error": err.Error()})
		// 	c.Abort()
		// 	return
		// }
		c.Set("Account", p.PortalEmail)
		c.Set("Payload", p)
		// 继续处理请求
		c.Next()
	}
}
