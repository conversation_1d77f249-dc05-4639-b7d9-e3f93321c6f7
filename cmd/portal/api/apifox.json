{"apifoxProject": "1.0.0", "$schema": {"app": "apifox", "type": "project", "version": "1.2.0"}, "info": {"name": "Portal API文档", "description": "Portal项目的完整API接口文档，包含用户管理、项目管理、扫描结果、导出任务、全局常量等所有接口", "mockRule": {"rules": [], "enableSystemRule": true}}, "apiCollection": [{"name": "根目录", "id": 52124553, "auth": {}, "securityScheme": {}, "parentId": 0, "serverId": "", "description": "", "identityPattern": {"httpApi": {"type": "methodAndPath", "bodyType": "", "fields": []}}, "shareSettings": {}, "visibility": "SHARED", "moduleId": 1488373, "preProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "postProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "inheritPostProcessors": {}, "inheritPreProcessors": {}, "items": [{"name": "scan", "id": 59013634, "auth": {}, "securityScheme": {}, "parentId": 0, "serverId": "", "description": "", "identityPattern": {"httpApi": {"type": "inherit", "bodyType": "", "fields": []}}, "shareSettings": {}, "visibility": "INHERITED", "moduleId": 1488373, "preProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "postProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "inheritPostProcessors": {}, "inheritPreProcessors": {}, "items": [{"name": "查询CI镜像列表", "api": {"id": "312908638", "method": "get", "path": "/api/v1/scan/image/list/ci", "parameters": {"path": [], "query": [{"id": "pYDs6mar6v", "name": "projectUuid", "required": true, "description": "项目UUID", "type": "string", "schema": {"type": "string"}}, {"id": "wIhJyI5JBT", "name": "offset", "required": false, "description": "页码，从1开始", "type": "integer", "schema": {"type": "integer", "default": 1}}, {"id": "pZXz1lz3M6", "name": "limit", "required": false, "description": "每页数量", "type": "integer", "schema": {"type": "integer", "default": 10}}], "cookie": [], "header": [{"id": "OP4tw1ZodE", "name": "Authorization", "required": true, "description": "认证token", "example": "", "type": "string", "schema": {"type": "string"}}]}, "auth": {}, "securityScheme": {}, "commonParameters": {"query": [], "body": [], "cookie": [], "header": [{"name": "Authorization"}]}, "responses": [{"id": "705328203", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"type": "object", "properties": {"apiVersion": {"type": "string", "description": "API版本"}, "data": {"type": "object", "properties": {"status": {"type": "integer", "description": "状态码，0表示成功"}, "items": {"type": "array", "description": "数据列表", "items": {"$ref": "#/definitions/175906057"}}, "totalItems": {"type": "integer", "description": "总数量"}}, "required": ["status"], "x-apifox-orders": ["status", "items", "totalItems"]}}, "x-apifox-refs": {}, "x-apifox-orders": ["apiVersion", "data"], "required": ["apiVersion", "data"]}, "description": "成功返回CI漏洞列表", "contentType": "json", "mediaType": "application/json", "oasExtensions": ""}], "responseExamples": [], "requestBody": {"type": "none", "parameters": [], "examples": [], "mediaType": "", "oasExtensions": ""}, "description": "根据项目UUID查询CI扫描的漏洞列表，支持分页和排序", "tags": ["scan"], "status": "released", "serverId": "", "operationId": "", "sourceUrl": "", "ordering": 0, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": ["BLANK.705328203"], "visibility": "INHERITED", "moduleId": 1488373, "oasExtensions": "", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "查询SourceCheck漏洞列表", "api": {"id": "308513249", "method": "get", "path": "/api/v1/scan/vuln/list/sourceCheck", "parameters": {"path": [], "query": [{"id": "EyDZVyfEpO", "name": "projectUuid", "required": true, "description": "项目UUID", "type": "string", "schema": {"type": "string"}}, {"id": "WNUYhZ27l9", "name": "offset", "required": false, "description": "页码，从1开始", "type": "integer", "schema": {"type": "integer", "default": 1}}, {"id": "KhwPHfcTkP", "name": "limit", "required": false, "description": "每页数量", "type": "integer", "schema": {"type": "integer", "default": 10}}], "cookie": [], "header": [{"id": "2MJbEElxog", "name": "Authorization", "required": true, "description": "认证token", "example": "", "type": "string", "schema": {"type": "string"}}]}, "auth": {}, "securityScheme": {}, "commonParameters": {"query": [], "body": [], "cookie": [], "header": [{"name": "Authorization"}]}, "responses": [{"id": "697255352", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"type": "object", "properties": {"apiVersion": {"type": "string", "description": "API版本"}, "data": {"type": "object", "properties": {"status": {"type": "integer", "description": "状态码，0表示成功"}, "items": {"type": "array", "description": "数据列表", "items": {"$ref": "#/definitions/175906054"}}, "totalItems": {"type": "integer", "description": "总数量"}}, "required": ["status"], "x-apifox-orders": ["status", "items", "totalItems"]}}, "x-apifox-refs": {}, "x-apifox-orders": ["apiVersion", "data"], "required": ["apiVersion", "data"]}, "description": "成功返回漏洞列表", "contentType": "json", "mediaType": "application/json", "oasExtensions": ""}], "responseExamples": [], "requestBody": {"type": "none", "parameters": [], "examples": [], "mediaType": "", "oasExtensions": ""}, "description": "根据项目UUID查询SourceCheck扫描的漏洞列表，支持分页和排序", "tags": ["scan"], "status": "released", "serverId": "", "operationId": "", "sourceUrl": "", "ordering": 18, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": ["BLANK.697255352"], "visibility": "INHERITED", "moduleId": 1488373, "oasExtensions": "", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}]}]}], "socketCollection": [], "docCollection": [], "webSocketCollection": [], "socketIOCollection": [], "responseCollection": [{"_databaseId": 6274975, "updatedAt": "2025-03-13T02:03:23.000Z", "name": "根目录", "type": "root", "children": [], "moduleId": 1488373, "parentId": 0, "id": 6274975, "items": [{"_databaseId": 340658722, "name": "记录不存在", "moduleId": 1488373, "code": 404, "contentType": "json", "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string", "mock": {"mock": "Not found"}}}, "required": ["code", "message"], "x-apifox-orders": ["code", "message"]}, "defaultEnable": false, "folderId": 0, "id": 340658722, "databaseResponseExamples": [], "responseExamples": []}, {"_databaseId": 340658723, "name": "参数不正确", "moduleId": 1488373, "code": 400, "contentType": "json", "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "mock": {"mock": "400"}}, "message": {"type": "string", "mock": {"mock": "Invalid input"}}}, "required": ["code", "message"], "x-apifox-orders": ["code", "message"]}, "defaultEnable": false, "folderId": 0, "id": 340658723, "databaseResponseExamples": [], "responseExamples": []}]}], "schemaCollection": [{"id": 13311047, "name": "根目录", "visibility": "SHARED", "moduleId": 1488373, "items": [{"id": 14878177, "name": "<PERSON><PERSON><PERSON>", "visibility": "INHERITED", "moduleId": 1488373, "items": [{"name": "SourceCheckVuln", "displayName": "", "id": "#/definitions/175906054", "description": "SourceCheck漏洞数据模型", "schema": {"jsonSchema": {"type": "object", "description": "SourceCheck漏洞数据模型", "properties": {"id": {"type": "integer", "description": "漏洞ID"}, "projectUuid": {"type": "string", "description": "项目UUID"}, "uniqueID": {"type": "string", "description": "唯一标识ID"}, "sourceCheckUuid": {"type": "string", "description": "SourceCheck应用UUID"}, "customCveNo": {"type": "string", "description": "CVE编号"}, "severity": {"type": "string", "description": "风险等级描述"}, "cweName": {"type": "string", "description": "弱点类型名称"}, "affectComponentCount": {"type": "integer", "description": "影响组件数"}, "description": {"type": "string", "description": "漏洞描述"}}, "required": ["id", "projectUuid", "uniqueID", "severity"], "x-apifox-orders": ["id", "projectUuid", "uniqueID", "sourceCheckUuid", "customCveNo", "severity", "cweName", "affectComponentCount", "description"]}}, "visibility": "INHERITED", "moduleId": 1488373}, {"name": "CiVulns", "displayName": "", "id": "#/definitions/175906057", "description": "CI漏洞数据模型（直接使用scannerCi.CiVulns）", "schema": {"jsonSchema": {"type": "object", "description": "CI漏洞数据模型（直接使用scannerCi.CiVulns）", "properties": {"id": {"type": "integer", "description": "漏洞ID"}, "created_at": {"type": "integer", "description": "创建时间（毫秒时间戳）"}, "updated_at": {"type": "integer", "description": "更新时间（毫秒时间戳）"}, "target": {"type": "string", "description": "目标"}, "name": {"type": "string", "description": "漏洞名称，形如CVE-2021-28831"}, "namespace": {"type": "string", "description": "发行版名字：alpine，redhat等"}, "description": {"type": "string", "description": "描述"}, "severity": {"type": "string", "description": "威胁等级"}, "severity_int": {"type": "integer", "description": "威胁等级数值"}, "pkg_name": {"type": "string", "description": "软件包来源"}, "pkg_version": {"type": "string", "description": "软件包版本"}, "fixedby": {"type": "string", "description": "修复建议"}, "class": {"type": "string", "description": "代表是系统包还是语言包 os-pkgs"}, "language": {"type": "string", "description": "编程语言"}, "frame": {"type": "string", "description": "开发框架筛选"}}, "required": ["id", "name", "severity", "severity_int", "pkg_name", "pkg_version"], "x-apifox-orders": ["id", "created_at", "updated_at", "target", "name", "namespace", "description", "severity", "severity_int", "pkg_name", "pkg_version", "fixedby", "class", "language", "frame"]}}, "visibility": "INHERITED", "moduleId": 1488373}]}]}], "securitySchemeCollection": [], "requestCollection": [{"name": "根目录", "children": [], "ordering": ["requestFolder.6343780"], "items": []}], "environments": [], "commonScripts": [], "globalVariables": [{"id": "6015725", "variables": [{"name": "sourceCodeToken", "value": "BASIC-API:************************************************************************", "description": "", "isBindInitial": true, "initialValue": "BASIC-API:************************************************************************", "isSync": true}, {"name": "authCode", "value": "", "description": "", "isBindInitial": false, "initialValue": "", "isSync": true}]}], "commonParameters": {"id": 752317, "createdAt": "2025-04-10T15:46:54.000Z", "updatedAt": "2025-06-18T07:24:11.000Z", "deletedAt": null, "parameters": {"query": [], "body": [], "cookie": [], "header": [{"name": "Authorization", "defaultEnable": true, "type": "string", "id": "tJkUqedXHi", "defaultValue": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.MxQPIk-x5OSgTMIN1137X7TIU-xFHCwZmH38cnZcWUE", "schema": {"type": "string", "default": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.MxQPIk-x5OSgTMIN1137X7TIU-xFHCwZmH38cnZcWUE"}}]}, "projectId": 6015725, "creatorId": 2198346, "editorId": 653284}, "projectSetting": {"id": "6043435", "auth": {}, "securityScheme": {}, "servers": [{"id": "default", "name": "默认服务"}], "gateway": [], "language": "zh-CN", "apiStatuses": ["developing", "testing", "released", "deprecated"], "mockSettings": {}, "preProcessors": [], "postProcessors": [], "advancedSettings": {"enableJsonc": false, "enableBigint": false, "responseValidate": true, "enableTestScenarioSetting": false, "enableYAPICompatScript": false, "isDefaultUrlEncoding": 2, "publishedDocUrlRules": {"defaultRule": "RESOURCE_KEY_ONLY", "resourceKeyStandard": "NEW"}, "folderShareExpandModeSettings": {"expandId": [], "mode": "AUTO"}}, "initialDisabledMockIds": [], "cloudMock": {"security": "free", "enable": false, "tokenKey": "apifoxToken"}}, "customFunctions": [], "projectAssociations": []}