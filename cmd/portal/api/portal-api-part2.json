{"name": "扫描结果管理", "id": 59013636, "auth": {}, "securityScheme": {}, "parentId": 52124553, "serverId": "", "description": "扫描结果相关接口", "identityPattern": {"httpApi": {"type": "inherit", "bodyType": "", "fields": []}}, "shareSettings": {}, "visibility": "INHERITED", "moduleId": 1488373, "preProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "postProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "inheritPostProcessors": {}, "inheritPreProcessors": {}, "items": [{"name": "查询CodeSec漏洞列表", "api": {"id": "scan_vuln_codesec", "method": "get", "path": "/api/v1/scan/vuln/list/codesec", "description": "查询CodeSec扫描的漏洞列表", "tags": ["扫描结果"], "status": "released", "parameters": {"header": [{"name": "Authorization", "required": true, "description": "认证token", "type": "string"}], "query": [{"name": "projectUuid", "required": true, "description": "项目UUID", "type": "string"}, {"name": "offset", "required": false, "description": "页码，从1开始", "type": "integer", "default": 1}, {"name": "limit", "required": false, "description": "每页数量", "type": "integer", "default": 10}]}, "responses": [{"code": 200, "description": "成功获取CodeSec漏洞列表", "jsonSchema": {"type": "object", "properties": {"apiVersion": {"type": "string"}, "data": {"type": "object", "properties": {"status": {"type": "integer"}, "items": {"type": "array", "items": {"$ref": "#/definitions/CodeSecVuln"}}, "totalItems": {"type": "integer"}, "itemsPerPage": {"type": "integer"}, "startIndex": {"type": "integer"}}}}}}]}}, {"name": "CodeSec扫描汇总", "api": {"id": "scan_summary_codesec", "method": "get", "path": "/api/v1/scan/summary/codesec", "description": "获取CodeSec扫描汇总信息", "tags": ["扫描结果"], "status": "released", "parameters": {"header": [{"name": "Authorization", "required": true, "description": "认证token", "type": "string"}], "query": [{"name": "projectUuid", "required": true, "description": "项目UUID", "type": "string"}]}, "responses": [{"code": 200, "description": "成功获取CodeSec扫描汇总", "jsonSchema": {"type": "object", "properties": {"apiVersion": {"type": "string"}, "data": {"type": "object", "properties": {"status": {"type": "integer"}, "item": {"$ref": "#/definitions/CodesecSummary"}}}}}}]}}, {"name": "SourceCheck扫描汇总", "api": {"id": "scan_summary_sourcecheck", "method": "get", "path": "/api/v1/scan/summary/sourceCheck", "description": "获取SourceCheck扫描汇总信息", "tags": ["扫描结果"], "status": "released", "parameters": {"header": [{"name": "Authorization", "required": true, "description": "认证token", "type": "string"}], "query": [{"name": "projectUuid", "required": true, "description": "项目UUID", "type": "string"}]}, "responses": [{"code": 200, "description": "成功获取SourceCheck扫描汇总", "jsonSchema": {"type": "object", "properties": {"apiVersion": {"type": "string"}, "data": {"type": "object", "properties": {"status": {"type": "integer"}, "item": {"$ref": "#/definitions/SourceCheckSummary"}}}}}}]}}, {"name": "查询SourceCheck漏洞列表", "api": {"id": "scan_vuln_sourcecheck", "method": "get", "path": "/api/v1/scan/vuln/list/sourceCheck", "description": "查询SourceCheck扫描的漏洞列表", "tags": ["扫描结果"], "status": "released", "parameters": {"header": [{"name": "Authorization", "required": true, "description": "认证token", "type": "string"}], "query": [{"name": "projectUuid", "required": true, "description": "项目UUID", "type": "string"}, {"name": "offset", "required": false, "description": "页码，从1开始", "type": "integer", "default": 1}, {"name": "limit", "required": false, "description": "每页数量", "type": "integer", "default": 10}]}, "responses": [{"code": 200, "description": "成功获取SourceCheck漏洞列表", "jsonSchema": {"type": "object", "properties": {"apiVersion": {"type": "string"}, "data": {"type": "object", "properties": {"status": {"type": "integer"}, "items": {"type": "array", "items": {"$ref": "#/definitions/SourceCheckVuln"}}, "totalItems": {"type": "integer"}, "itemsPerPage": {"type": "integer"}, "startIndex": {"type": "integer"}}}}}}]}}, {"name": "查询SourceCheck许可列表", "api": {"id": "scan_license_sourcecheck", "method": "get", "path": "/api/v1/scan/license/list/sourceCheck", "description": "查询SourceCheck扫描的许可列表", "tags": ["扫描结果"], "status": "released", "parameters": {"header": [{"name": "Authorization", "required": true, "description": "认证token", "type": "string"}], "query": [{"name": "projectUuid", "required": true, "description": "项目UUID", "type": "string"}, {"name": "offset", "required": false, "description": "页码，从1开始", "type": "integer", "default": 1}, {"name": "limit", "required": false, "description": "每页数量", "type": "integer", "default": 10}]}, "responses": [{"code": 200, "description": "成功获取SourceCheck许可列表", "jsonSchema": {"type": "object", "properties": {"apiVersion": {"type": "string"}, "data": {"type": "object", "properties": {"status": {"type": "integer"}, "items": {"type": "array", "items": {"$ref": "#/definitions/SourceCheckLicense"}}, "totalItems": {"type": "integer"}, "itemsPerPage": {"type": "integer"}, "startIndex": {"type": "integer"}}}}}}]}}, {"name": "查询SourceCheck组件列表", "api": {"id": "scan_component_sourcecheck", "method": "get", "path": "/api/v1/scan/component/list/sourceCheck", "description": "查询SourceCheck扫描的组件列表", "tags": ["扫描结果"], "status": "released", "parameters": {"header": [{"name": "Authorization", "required": true, "description": "认证token", "type": "string"}], "query": [{"name": "projectUuid", "required": true, "description": "项目UUID", "type": "string"}, {"name": "offset", "required": false, "description": "页码，从1开始", "type": "integer", "default": 1}, {"name": "limit", "required": false, "description": "每页数量", "type": "integer", "default": 10}]}, "responses": [{"code": 200, "description": "成功获取SourceCheck组件列表", "jsonSchema": {"type": "object", "properties": {"apiVersion": {"type": "string"}, "data": {"type": "object", "properties": {"status": {"type": "integer"}, "items": {"type": "array", "items": {"$ref": "#/definitions/SourceCheckComponent"}}, "totalItems": {"type": "integer"}, "itemsPerPage": {"type": "integer"}, "startIndex": {"type": "integer"}}}}}}]}}, {"name": "查询CI漏洞列表", "api": {"id": "scan_vuln_ci", "method": "get", "path": "/api/v1/scan/vuln/list/ci", "description": "查询CI扫描的漏洞列表", "tags": ["扫描结果"], "status": "released", "parameters": {"header": [{"name": "Authorization", "required": true, "description": "认证token", "type": "string"}], "query": [{"name": "projectUuid", "required": true, "description": "项目UUID", "type": "string"}, {"name": "offset", "required": false, "description": "页码，从1开始", "type": "integer", "default": 1}, {"name": "limit", "required": false, "description": "每页数量", "type": "integer", "default": 10}]}, "responses": [{"code": 200, "description": "成功获取CI漏洞列表", "jsonSchema": {"type": "object", "properties": {"apiVersion": {"type": "string"}, "data": {"type": "object", "properties": {"status": {"type": "integer"}, "items": {"type": "array", "items": {"$ref": "#/definitions/CiVulns"}}, "totalItems": {"type": "integer"}, "itemsPerPage": {"type": "integer"}, "startIndex": {"type": "integer"}}}}}}]}}, {"name": "查询CI镜像列表", "api": {"id": "scan_image_ci", "method": "get", "path": "/api/v1/scan/image/list/ci", "description": "查询CI扫描的镜像列表", "tags": ["扫描结果"], "status": "released", "parameters": {"header": [{"name": "Authorization", "required": true, "description": "认证token", "type": "string"}], "query": [{"name": "projectUuid", "required": true, "description": "项目UUID", "type": "string"}, {"name": "offset", "required": false, "description": "页码，从1开始", "type": "integer", "default": 1}, {"name": "limit", "required": false, "description": "每页数量", "type": "integer", "default": 10}]}, "responses": [{"code": 200, "description": "成功获取CI镜像列表", "jsonSchema": {"type": "object", "properties": {"apiVersion": {"type": "string"}, "data": {"type": "object", "properties": {"status": {"type": "integer"}, "items": {"type": "array", "items": {"$ref": "#/definitions/CiVulns"}}, "totalItems": {"type": "integer"}, "itemsPerPage": {"type": "integer"}, "startIndex": {"type": "integer"}}}}}}]}}, {"name": "启动扫描", "api": {"id": "scan_start", "method": "post", "path": "/api/v1/scan/startScan", "description": "启动项目扫描任务", "tags": ["扫描结果"], "status": "released", "parameters": {"header": [{"name": "Authorization", "required": true, "description": "认证token", "type": "string"}]}, "requestBody": {"type": "application/json", "jsonSchema": {"type": "object", "properties": {"projectIds": {"type": "array", "description": "项目ID列表", "items": {"type": "integer"}}, "scanTypes": {"type": "array", "description": "扫描类型列表", "items": {"type": "string"}}}, "required": ["projectIds", "scanTypes"]}}, "responses": [{"code": 200, "description": "成功启动扫描任务", "jsonSchema": {"type": "object", "properties": {"apiVersion": {"type": "string"}, "data": {"type": "object", "properties": {"status": {"type": "integer"}, "item": {"type": "object", "properties": {"message": {"type": "string"}, "projectIds": {"type": "array", "items": {"type": "integer"}}, "scanTypes": {"type": "array", "items": {"type": "string"}}}}}}}}}]}}]}