{"exportTaskManagement": {"name": "导出任务管理", "id": 59013637, "auth": {}, "securityScheme": {}, "parentId": 52124553, "serverId": "", "description": "导出任务相关接口", "identityPattern": {"httpApi": {"type": "inherit", "bodyType": "", "fields": []}}, "shareSettings": {}, "visibility": "INHERITED", "moduleId": 1488373, "preProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "postProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "inheritPostProcessors": {}, "inheritPreProcessors": {}, "items": [{"name": "查询导出任务列表", "api": {"id": "export_task_list", "method": "get", "path": "/api/v1/export/task/list", "description": "查询导出任务列表", "tags": ["导出任务"], "status": "released", "parameters": {"header": [{"name": "Authorization", "required": true, "description": "认证token", "type": "string"}], "query": [{"name": "offset", "required": false, "description": "页码，从1开始", "type": "integer", "default": 1}, {"name": "limit", "required": false, "description": "每页数量", "type": "integer", "default": 10}]}, "responses": [{"code": 200, "description": "成功获取导出任务列表", "jsonSchema": {"type": "object", "properties": {"apiVersion": {"type": "string"}, "data": {"type": "object", "properties": {"status": {"type": "integer"}, "items": {"type": "array", "items": {"$ref": "#/definitions/ExportTask"}}, "totalItems": {"type": "integer"}, "itemsPerPage": {"type": "integer"}, "startIndex": {"type": "integer"}, "notFinished": {"type": "integer"}}}}}}]}}, {"name": "下载文件", "api": {"id": "export_task_download", "method": "get", "path": "/api/v1/export/task/download", "description": "下载导出的文件", "tags": ["导出任务"], "status": "released", "parameters": {"header": [{"name": "Authorization", "required": true, "description": "认证token", "type": "string"}], "query": [{"name": "id", "required": true, "description": "任务ID", "type": "integer"}]}, "responses": [{"code": 200, "description": "成功下载文件", "headers": [{"name": "Content-Disposition", "description": "文件下载头", "type": "string"}], "contentType": "application/octet-stream"}]}}, {"name": "创建导出任务", "api": {"id": "export_task_create", "method": "post", "path": "/api/v1/export/task/createTask", "description": "创建导出任务", "tags": ["导出任务"], "status": "released", "parameters": {"header": [{"name": "Authorization", "required": true, "description": "认证token", "type": "string"}]}, "requestBody": {"type": "application/json", "jsonSchema": {"type": "object", "properties": {"parameter": {"$ref": "#/definitions/PortalProjectExportParam"}, "creator": {"type": "string", "description": "任务创建人"}, "taskType": {"type": "string", "description": "任务类型", "default": "excel"}, "filename": {"type": "string", "description": "文件名"}}, "required": ["parameter", "creator"]}}, "responses": [{"code": 200, "description": "成功创建导出任务", "jsonSchema": {"type": "object", "properties": {"apiVersion": {"type": "string"}, "data": {"type": "object", "properties": {"status": {"type": "integer"}, "item": {"type": "object", "properties": {"taskId": {"type": "integer"}}}}}}}}]}}]}, "globalConstants": {"name": "全局常量", "id": 59013638, "auth": {}, "securityScheme": {}, "parentId": 52124553, "serverId": "", "description": "全局常量相关接口", "identityPattern": {"httpApi": {"type": "inherit", "bodyType": "", "fields": []}}, "shareSettings": {}, "visibility": "INHERITED", "moduleId": 1488373, "preProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "postProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "inheritPostProcessors": {}, "inheritPreProcessors": {}, "items": [{"name": "获取常量列表", "api": {"id": "global_consts", "method": "get", "path": "/api/v1/global/consts/list", "description": "获取系统常量列表", "tags": ["全局常量"], "status": "released", "parameters": {"query": [{"name": "type", "required": false, "description": "常量类型：projectType, userRole, scanStatus, severity, userStatus, riskLevel", "type": "string"}]}, "responses": [{"code": 200, "description": "成功获取常量列表", "jsonSchema": {"type": "object", "properties": {"apiVersion": {"type": "string"}, "data": {"type": "object", "properties": {"status": {"type": "integer"}, "item": {"type": "object", "description": "常量对象，包含各种类型的常量数组"}}}}}}]}}]}, "additionalUserApis": {"name": "用户管理补充接口", "items": [{"name": "修改密码", "api": {"id": "user_change_pwd", "method": "put", "path": "/api/v1/user/changePwd", "description": "修改用户密码", "tags": ["用户管理"], "status": "released", "parameters": {"header": [{"name": "Authorization", "required": true, "description": "认证token", "type": "string"}]}, "requestBody": {"type": "application/json", "jsonSchema": {"type": "object", "properties": {"oldPassword": {"type": "string", "description": "旧密码"}, "newPassword": {"type": "string", "description": "新密码"}}, "required": ["oldPassword", "newPassword"]}}, "responses": [{"code": 200, "description": "成功修改密码", "jsonSchema": {"$ref": "#/definitions/StandardResponse"}}]}}, {"name": "重置密码", "api": {"id": "user_reset_pwd", "method": "put", "path": "/api/v1/user/resetPwd", "description": "重置用户密码", "tags": ["用户管理"], "status": "released", "parameters": {"header": [{"name": "Authorization", "required": true, "description": "认证token", "type": "string"}]}, "requestBody": {"type": "application/json", "jsonSchema": {"type": "object", "properties": {"userId": {"type": "integer", "description": "用户ID"}, "newPassword": {"type": "string", "description": "新密码"}}, "required": ["userId", "newPassword"]}}, "responses": [{"code": 200, "description": "成功重置密码", "jsonSchema": {"$ref": "#/definitions/StandardResponse"}}]}}, {"name": "验证密码", "api": {"id": "user_check_pwd", "method": "post", "path": "/api/v1/user/checkPasswd", "description": "验证用户密码", "tags": ["用户管理"], "status": "released", "parameters": {"header": [{"name": "Authorization", "required": true, "description": "认证token", "type": "string"}]}, "requestBody": {"type": "application/json", "jsonSchema": {"type": "object", "properties": {"password": {"type": "string", "description": "密码"}}, "required": ["password"]}}, "responses": [{"code": 200, "description": "密码验证结果", "jsonSchema": {"$ref": "#/definitions/StandardResponse"}}]}}]}, "additionalProjectApis": {"name": "项目管理补充接口", "items": [{"name": "获取项目详情", "api": {"id": "project_detail", "method": "get", "path": "/api/v1/projects/detail", "description": "获取项目详细信息", "tags": ["项目管理"], "status": "released", "parameters": {"header": [{"name": "Authorization", "required": true, "description": "认证token", "type": "string"}], "query": [{"name": "id", "required": true, "description": "项目ID", "type": "integer"}]}, "responses": [{"code": 200, "description": "成功获取项目详情", "jsonSchema": {"type": "object", "properties": {"apiVersion": {"type": "string"}, "data": {"type": "object", "properties": {"status": {"type": "integer"}, "item": {"$ref": "#/definitions/Project"}}}}}}]}}, {"name": "创建项目", "api": {"id": "project_create", "method": "post", "path": "/api/v1/projects/create", "description": "创建新项目", "tags": ["项目管理"], "status": "released", "parameters": {"header": [{"name": "Authorization", "required": true, "description": "认证token", "type": "string"}]}, "requestBody": {"type": "application/json", "jsonSchema": {"$ref": "#/definitions/Project"}}, "responses": [{"code": 200, "description": "成功创建项目", "jsonSchema": {"$ref": "#/definitions/StandardResponse"}}]}}, {"name": "更新项目", "api": {"id": "project_update", "method": "put", "path": "/api/v1/projects/update", "description": "更新项目信息", "tags": ["项目管理"], "status": "released", "parameters": {"header": [{"name": "Authorization", "required": true, "description": "认证token", "type": "string"}], "query": [{"name": "id", "required": true, "description": "项目ID", "type": "integer"}]}, "requestBody": {"type": "application/json", "jsonSchema": {"$ref": "#/definitions/Project"}}, "responses": [{"code": 200, "description": "成功更新项目", "jsonSchema": {"type": "object", "properties": {"apiVersion": {"type": "string"}, "data": {"type": "object", "properties": {"status": {"type": "integer"}, "item": {"$ref": "#/definitions/Project"}}}}}}]}}, {"name": "删除项目", "api": {"id": "project_delete", "method": "delete", "path": "/api/v1/projects/delete", "description": "删除项目", "tags": ["项目管理"], "status": "released", "parameters": {"header": [{"name": "Authorization", "required": true, "description": "认证token", "type": "string"}], "query": [{"name": "id", "required": false, "description": "项目ID", "type": "integer"}, {"name": "ids", "required": false, "description": "项目ID列表", "type": "array", "items": {"type": "integer"}}]}, "responses": [{"code": 200, "description": "成功删除项目", "jsonSchema": {"$ref": "#/definitions/StandardResponse"}}]}}]}}