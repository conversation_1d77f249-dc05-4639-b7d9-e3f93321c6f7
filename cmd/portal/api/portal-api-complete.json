{"apifoxProject": "1.0.0", "$schema": {"app": "apifox", "type": "project", "version": "1.2.0"}, "info": {"name": "Portal API完整文档", "description": "Portal项目的完整API接口文档，包含用户管理、项目管理、扫描结果、导出任务、全局常量等所有接口", "mockRule": {"rules": [], "enableSystemRule": true}}, "apiCollection": [{"name": "portal", "id": 52124553, "auth": {}, "securityScheme": {}, "parentId": 0, "serverId": "", "description": "Portal项目API接口集合", "identityPattern": {"httpApi": {"type": "methodAndPath", "bodyType": "", "fields": []}}, "shareSettings": {}, "visibility": "SHARED", "moduleId": 1488373, "preProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "postProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "inheritPostProcessors": {}, "inheritPreProcessors": {}, "items": [{"name": "用户管理", "id": 59013634, "auth": {}, "securityScheme": {}, "parentId": 52124553, "serverId": "", "description": "用户相关接口", "identityPattern": {"httpApi": {"type": "inherit", "bodyType": "", "fields": []}}, "shareSettings": {}, "visibility": "INHERITED", "moduleId": 1488373, "preProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "postProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "inheritPostProcessors": {}, "inheritPreProcessors": {}, "items": [{"name": "用户注册", "api": {"id": "user_register", "method": "post", "path": "/api/v1/user/register", "description": "注册新用户账号", "tags": ["用户管理"], "status": "released", "parameters": {"header": [{"name": "Authorization", "required": true, "description": "认证token", "type": "string"}]}, "requestBody": {"type": "application/json", "jsonSchema": {"$ref": "#/definitions/User"}}, "responses": [{"code": 200, "description": "成功注册用户", "jsonSchema": {"$ref": "#/definitions/StandardResponse"}}]}}, {"name": "用户登录", "api": {"id": "user_login", "method": "post", "path": "/api/v1/user/login", "description": "用户登录接口", "tags": ["用户管理"], "status": "released", "requestBody": {"type": "application/json", "jsonSchema": {"type": "object", "properties": {"portalEmail": {"type": "string", "description": "登录邮箱"}, "pwdString": {"type": "string", "description": "登录密码"}}, "required": ["portalEmail", "pwdString"]}}, "responses": [{"code": 200, "description": "成功登录", "jsonSchema": {"type": "object", "properties": {"apiVersion": {"type": "string"}, "data": {"type": "object", "properties": {"status": {"type": "integer"}, "item": {"$ref": "#/definitions/LoginResponse"}}}}}}]}}, {"name": "更新用户", "api": {"id": "user_update", "method": "put", "path": "/api/v1/user/update", "description": "更新用户信息", "tags": ["用户管理"], "status": "released", "parameters": {"header": [{"name": "Authorization", "required": true, "description": "认证token", "type": "string"}], "query": [{"name": "id", "required": true, "description": "用户ID", "type": "integer"}]}, "requestBody": {"type": "application/json", "jsonSchema": {"$ref": "#/definitions/User"}}, "responses": [{"code": 200, "description": "成功更新用户", "jsonSchema": {"$ref": "#/definitions/StandardResponse"}}]}}, {"name": "获取用户详情", "api": {"id": "user_detail", "method": "get", "path": "/api/v1/user/detail", "description": "获取用户详细信息", "tags": ["用户管理"], "status": "released", "parameters": {"header": [{"name": "Authorization", "required": true, "description": "认证token", "type": "string"}], "query": [{"name": "id", "required": false, "description": "用户ID", "type": "integer"}, {"name": "authCode", "required": false, "description": "认证码", "type": "string"}]}, "responses": [{"code": 200, "description": "成功获取用户信息", "jsonSchema": {"type": "object", "properties": {"apiVersion": {"type": "string"}, "data": {"type": "object", "properties": {"status": {"type": "integer"}, "item": {"$ref": "#/definitions/User"}}}}}}]}}, {"name": "搜索用户列表", "api": {"id": "user_list", "method": "get", "path": "/api/v1/user/list", "description": "搜索用户列表", "tags": ["用户管理"], "status": "released", "parameters": {"header": [{"name": "Authorization", "required": true, "description": "认证token", "type": "string"}], "query": [{"name": "name", "required": false, "description": "用户名", "type": "string"}, {"name": "portalEmail", "required": false, "description": "邮箱", "type": "string"}, {"name": "mobile", "required": false, "description": "手机号", "type": "string"}, {"name": "status", "required": false, "description": "状态", "type": "string"}, {"name": "offset", "required": false, "description": "页码，从1开始", "type": "integer", "default": 1}, {"name": "limit", "required": false, "description": "每页数量", "type": "integer", "default": 10}]}, "responses": [{"code": 200, "description": "成功获取用户列表", "jsonSchema": {"$ref": "#/definitions/ListResponse"}}]}}, {"name": "获取用户认证码", "api": {"id": "user_token", "method": "get", "path": "/api/v1/user/token", "description": "获取用户认证码", "tags": ["用户管理"], "status": "released", "parameters": {"header": [{"name": "Authorization", "required": true, "description": "认证token", "type": "string"}], "query": [{"name": "id", "required": false, "description": "用户ID", "type": "integer"}, {"name": "portalEmail", "required": false, "description": "邮箱", "type": "string"}]}, "responses": [{"code": 200, "description": "成功获取认证码", "jsonSchema": {"type": "object", "properties": {"apiVersion": {"type": "string"}, "data": {"type": "object", "properties": {"status": {"type": "integer"}, "item": {"$ref": "#/definitions/AuthCode"}}}}}}]}}, {"name": "删除用户", "api": {"id": "user_delete", "method": "delete", "path": "/api/v1/user/delete", "description": "删除用户", "tags": ["用户管理"], "status": "released", "parameters": {"header": [{"name": "Authorization", "required": true, "description": "认证token", "type": "string"}], "query": [{"name": "id", "required": false, "description": "用户ID", "type": "integer"}, {"name": "ids", "required": false, "description": "用户ID列表", "type": "array", "items": {"type": "integer"}}]}, "responses": [{"code": 200, "description": "成功删除用户", "jsonSchema": {"$ref": "#/definitions/StandardResponse"}}]}}]}, {"name": "项目管理", "id": 59013635, "auth": {}, "securityScheme": {}, "parentId": 52124553, "serverId": "", "description": "项目相关接口", "identityPattern": {"httpApi": {"type": "inherit", "bodyType": "", "fields": []}}, "shareSettings": {}, "visibility": "INHERITED", "moduleId": 1488373, "preProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "postProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "inheritPostProcessors": {}, "inheritPreProcessors": {}, "items": [{"name": "搜索项目列表", "api": {"id": "project_list", "method": "get", "path": "/api/v1/projects/list", "description": "搜索项目列表", "tags": ["项目管理"], "status": "released", "parameters": {"header": [{"name": "Authorization", "required": true, "description": "认证token", "type": "string"}], "query": [{"name": "name", "required": false, "description": "项目名称", "type": "string"}, {"name": "url", "required": false, "description": "仓库地址", "type": "string"}, {"name": "gitType", "required": false, "description": "仓库类型", "type": "array", "items": {"type": "string"}}, {"name": "riskLevel", "required": false, "description": "风险级别", "type": "array", "items": {"type": "string"}}, {"name": "offset", "required": false, "description": "页码，从1开始", "type": "integer", "default": 1}, {"name": "limit", "required": false, "description": "每页数量", "type": "integer", "default": 10}]}, "responses": [{"code": 200, "description": "成功获取项目列表", "jsonSchema": {"$ref": "#/definitions/ListResponse"}}]}}, {"name": "获取项目详情", "api": {"id": "project_detail", "method": "get", "path": "/api/v1/projects/detail", "description": "获取项目详细信息", "tags": ["项目管理"], "status": "released", "parameters": {"header": [{"name": "Authorization", "required": true, "description": "认证token", "type": "string"}], "query": [{"name": "id", "required": true, "description": "项目ID", "type": "integer"}]}, "responses": [{"code": 200, "description": "成功获取项目详情", "jsonSchema": {"type": "object", "properties": {"apiVersion": {"type": "string"}, "data": {"type": "object", "properties": {"status": {"type": "integer"}, "item": {"$ref": "#/definitions/Project"}}}}}}]}}, {"name": "创建项目", "api": {"id": "project_create", "method": "post", "path": "/api/v1/projects/create", "description": "创建新项目", "tags": ["项目管理"], "status": "released", "parameters": {"header": [{"name": "Authorization", "required": true, "description": "认证token", "type": "string"}]}, "requestBody": {"type": "application/json", "jsonSchema": {"$ref": "#/definitions/Project"}}, "responses": [{"code": 200, "description": "成功创建项目", "jsonSchema": {"$ref": "#/definitions/StandardResponse"}}]}}, {"name": "更新项目", "api": {"id": "project_update", "method": "put", "path": "/api/v1/projects/update", "description": "更新项目信息", "tags": ["项目管理"], "status": "released", "parameters": {"header": [{"name": "Authorization", "required": true, "description": "认证token", "type": "string"}], "query": [{"name": "id", "required": true, "description": "项目ID", "type": "integer"}]}, "requestBody": {"type": "application/json", "jsonSchema": {"$ref": "#/definitions/Project"}}, "responses": [{"code": 200, "description": "成功更新项目", "jsonSchema": {"type": "object", "properties": {"apiVersion": {"type": "string"}, "data": {"type": "object", "properties": {"status": {"type": "integer"}, "item": {"$ref": "#/definitions/Project"}}}}}}]}}, {"name": "删除项目", "api": {"id": "project_delete", "method": "delete", "path": "/api/v1/projects/delete", "description": "删除项目", "tags": ["项目管理"], "status": "released", "parameters": {"header": [{"name": "Authorization", "required": true, "description": "认证token", "type": "string"}], "query": [{"name": "id", "required": false, "description": "项目ID", "type": "integer"}, {"name": "ids", "required": false, "description": "项目ID列表", "type": "array", "items": {"type": "integer"}}]}, "responses": [{"code": 200, "description": "成功删除项目", "jsonSchema": {"$ref": "#/definitions/StandardResponse"}}]}}]}, {"name": "扫描结果管理", "id": 59013636, "auth": {}, "securityScheme": {}, "parentId": 52124553, "serverId": "", "description": "扫描结果相关接口", "identityPattern": {"httpApi": {"type": "inherit", "bodyType": "", "fields": []}}, "shareSettings": {}, "visibility": "INHERITED", "moduleId": 1488373, "preProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "postProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "inheritPostProcessors": {}, "inheritPreProcessors": {}, "items": [{"name": "查询CodeSec漏洞列表", "api": {"id": "scan_vuln_codesec", "method": "get", "path": "/api/v1/scan/vuln/list/codesec", "description": "查询CodeSec扫描的漏洞列表", "tags": ["扫描结果"], "status": "released", "parameters": {"header": [{"name": "Authorization", "required": true, "description": "认证token", "type": "string"}], "query": [{"name": "projectUuid", "required": true, "description": "项目UUID", "type": "string"}, {"name": "offset", "required": false, "description": "页码，从1开始", "type": "integer", "default": 1}, {"name": "limit", "required": false, "description": "每页数量", "type": "integer", "default": 10}]}, "responses": [{"code": 200, "description": "成功获取CodeSec漏洞列表", "jsonSchema": {"$ref": "#/definitions/ListResponse"}}]}}]}]}], "socketCollection": [], "docCollection": [], "webSocketCollection": [], "socketIOCollection": [], "responseCollection": [{"_databaseId": 6274975, "updatedAt": "2025-03-13T02:03:23.000Z", "name": "根目录", "type": "root", "children": [], "moduleId": 1488373, "parentId": 0, "id": 6274975, "items": [{"_databaseId": 340658722, "name": "记录不存在", "moduleId": 1488373, "code": 404, "contentType": "json", "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string", "mock": {"mock": "Not found"}}}, "required": ["code", "message"], "x-apifox-orders": ["code", "message"]}, "defaultEnable": false, "folderId": 0, "id": 340658722, "databaseResponseExamples": [], "responseExamples": []}, {"_databaseId": 340658723, "name": "参数不正确", "moduleId": 1488373, "code": 400, "contentType": "json", "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "mock": {"mock": "400"}}, "message": {"type": "string", "mock": {"mock": "Invalid input"}}}, "required": ["code", "message"], "x-apifox-orders": ["code", "message"]}, "defaultEnable": false, "folderId": 0, "id": 340658723, "databaseResponseExamples": [], "responseExamples": []}]}], "schemaCollection": [{"id": 13311047, "name": "根目录", "visibility": "SHARED", "moduleId": 1488373, "items": [{"id": 14878177, "name": "<PERSON><PERSON><PERSON>", "visibility": "INHERITED", "moduleId": 1488373, "items": [{"name": "User", "displayName": "", "id": "#/definitions/User", "description": "用户数据模型", "schema": {"jsonSchema": {"type": "object", "description": "用户数据模型", "properties": {"id": {"type": "integer", "description": "用户ID"}, "name": {"type": "string", "description": "用户名"}, "portalEmail": {"type": "string", "description": "登录邮箱"}, "pwdString": {"type": "string", "description": "明文密码"}, "role": {"type": "string", "description": "用户角色"}, "mobile": {"type": "string", "description": "手机号"}, "comment": {"type": "string", "description": "备注信息"}, "status": {"type": "string", "description": "账号状态"}, "sourceCheckToken": {"type": "string", "description": "sourceCheck的访问令牌"}, "sourceCheckEmail": {"type": "string", "description": "sourceCheck的登录邮箱"}, "codesecAk": {"type": "string", "description": "codesec的UserUuid"}, "codesecSk": {"type": "string", "description": "codesec的AccessSecret"}, "codesecEmail": {"type": "string", "description": "codesec的登录邮箱"}, "tensorEmail": {"type": "string", "description": "tensor的登录邮箱"}, "tensorPwd": {"type": "string", "description": "tensor的登录密码"}, "createdAt": {"type": "integer", "description": "创建时间（毫秒时间戳）"}, "updatedAt": {"type": "integer", "description": "更新时间（毫秒时间戳）"}}, "required": ["name", "portalEmail", "role"], "x-apifox-orders": ["id", "name", "portalEmail", "pwdString", "role", "mobile", "comment", "status", "sourceCheckToken", "sourceCheckEmail", "codesecAk", "codesecSk", "codesecEmail", "tensorEmail", "tensorPwd", "createdAt", "updatedAt"]}}, "visibility": "INHERITED", "moduleId": 1488373}, {"name": "LoginResponse", "displayName": "", "id": "#/definitions/LoginResponse", "description": "登录响应数据模型", "schema": {"jsonSchema": {"type": "object", "description": "登录响应数据模型", "properties": {"id": {"type": "integer", "description": "用户ID"}, "portalEmail": {"type": "string", "description": "登录邮箱"}, "token": {"type": "string", "description": "JWT token"}}, "required": ["id", "portalEmail", "token"], "x-apifox-orders": ["id", "portalEmail", "token"]}}, "visibility": "INHERITED", "moduleId": 1488373}, {"name": "AuthCode", "displayName": "", "id": "#/definitions/AuthCode", "description": "认证码数据模型", "schema": {"jsonSchema": {"type": "object", "description": "认证码数据模型", "properties": {"authCode": {"type": "string", "description": "授权码"}, "userID": {"type": "integer", "description": "用户ID"}}, "required": ["authCode", "userID"], "x-apifox-orders": ["authCode", "userID"]}}, "visibility": "INHERITED", "moduleId": 1488373}, {"name": "StandardResponse", "displayName": "", "id": "#/definitions/StandardResponse", "description": "标准响应数据模型", "schema": {"jsonSchema": {"type": "object", "description": "标准响应数据模型", "properties": {"apiVersion": {"type": "string", "description": "API版本"}, "data": {"type": "object", "properties": {"status": {"type": "integer", "description": "状态码，0表示成功"}}, "required": ["status"], "x-apifox-orders": ["status"]}}, "required": ["apiVersion", "data"], "x-apifox-orders": ["apiVersion", "data"]}}, "visibility": "INHERITED", "moduleId": 1488373}, {"name": "ListResponse", "displayName": "", "id": "#/definitions/ListResponse", "description": "列表响应数据模型", "schema": {"jsonSchema": {"type": "object", "description": "列表响应数据模型", "properties": {"apiVersion": {"type": "string", "description": "API版本"}, "data": {"type": "object", "properties": {"status": {"type": "integer", "description": "状态码，0表示成功"}, "items": {"type": "array", "description": "数据列表", "items": {"type": "object"}}, "totalItems": {"type": "integer", "description": "总数量"}, "itemsPerPage": {"type": "integer", "description": "每页数量"}, "startIndex": {"type": "integer", "description": "起始索引"}}, "required": ["status"], "x-apifox-orders": ["status", "items", "totalItems", "itemsPerPage", "startIndex"]}}, "required": ["apiVersion", "data"], "x-apifox-orders": ["apiVersion", "data"]}}, "visibility": "INHERITED", "moduleId": 1488373}, {"name": "Project", "displayName": "", "id": "#/definitions/Project", "description": "项目数据模型", "schema": {"jsonSchema": {"type": "object", "description": "项目数据模型", "properties": {"id": {"type": "integer", "description": "项目ID"}, "uuid": {"type": "string", "description": "项目标识符"}, "name": {"type": "string", "description": "项目名称"}, "url": {"type": "string", "description": "仓库地址"}, "userId": {"type": "integer", "description": "用户ID"}, "description": {"type": "string", "description": "项目描述"}, "gitType": {"type": "string", "description": "仓库类型"}, "token": {"type": "string", "description": "Token"}, "branch": {"type": "string", "description": "分支名"}, "tag": {"type": "string", "description": "tag名"}, "branchTag": {"type": "string", "description": "分支名和tag名"}, "codesecScanStatus": {"type": "string", "description": "codesec扫描状态"}, "codesecScanMsg": {"type": "string", "description": "codesec扫描消息"}, "sourceCheckScanStatus": {"type": "string", "description": "sourcecheck扫描状态"}, "sourceCheckScanMsg": {"type": "string", "description": "sourcecheck扫描消息"}, "riskLevel": {"type": "string", "description": "项目风险级别：高、中、低、未知"}, "codesecHighRisk": {"type": "string", "description": "codesec最高风险等级"}, "codesecHighSeverity": {"type": "integer", "description": "codesec最高风险等级"}, "sourceCheckHighRisk": {"type": "string", "description": "sourcecheck最高风险等级"}, "sourceCheckHighSeverity": {"type": "integer", "description": "sourcecheck最高风险等级"}, "lastScanAt": {"type": "integer", "description": "最近扫描时间"}, "updater": {"type": "string", "description": "最近一次更新人"}, "createdAt": {"type": "integer", "description": "创建时间"}, "updatedAt": {"type": "integer", "description": "更新时间"}}, "required": ["name", "url", "gitType"], "x-apifox-orders": ["id", "uuid", "name", "url", "userId", "description", "gitType", "token", "branch", "tag", "branchTag", "codesecScanStatus", "codesecScanMsg", "sourceCheckScanStatus", "sourceCheckScanMsg", "riskLevel", "codesecHighRisk", "codesecHighSeverity", "sourceCheckHighRisk", "sourceCheckHighSeverity", "lastScanAt", "updater", "createdAt", "updatedAt"]}}, "visibility": "INHERITED", "moduleId": 1488373}, {"name": "CodeSecVuln", "displayName": "", "id": "#/definitions/CodeSecVuln", "description": "CodeSec漏洞数据模型", "schema": {"jsonSchema": {"type": "object", "description": "CodeSec漏洞数据模型", "properties": {"id": {"type": "integer", "description": "漏洞ID"}, "uniqueID": {"type": "string", "description": "唯一标识ID"}, "projectUuid": {"type": "string", "description": "项目UUID"}, "codesecAppId": {"type": "string", "description": "CodeSec应用ID"}, "vulName": {"type": "string", "description": "漏洞名称"}, "severity": {"type": "string", "description": "风险等级"}, "filename": {"type": "string", "description": "文件名"}, "rowNum": {"type": "string", "description": "文件行数"}, "vulFlag": {"type": "string", "description": "缺陷跟踪类型"}}, "required": ["id", "projectUuid", "vulName", "severity"], "x-apifox-orders": ["id", "uniqueID", "projectUuid", "codesecAppId", "vulName", "severity", "filename", "row<PERSON>um", "vulFlag"]}}, "visibility": "INHERITED", "moduleId": 1488373}, {"name": "SourceCheckVuln", "displayName": "", "id": "#/definitions/SourceCheckVuln", "description": "SourceCheck漏洞数据模型", "schema": {"jsonSchema": {"type": "object", "description": "SourceCheck漏洞数据模型", "properties": {"id": {"type": "integer", "description": "漏洞ID"}, "projectUuid": {"type": "string", "description": "项目UUID"}, "uniqueID": {"type": "string", "description": "唯一标识ID"}, "sourceCheckUuid": {"type": "string", "description": "SourceCheck应用UUID"}, "customCveNo": {"type": "string", "description": "CVE编号"}, "severity": {"type": "string", "description": "风险等级描述"}, "cweName": {"type": "string", "description": "弱点类型名称"}, "affectComponentCount": {"type": "integer", "description": "影响组件数"}, "description": {"type": "string", "description": "漏洞描述"}}, "required": ["id", "projectUuid", "uniqueID", "severity"], "x-apifox-orders": ["id", "projectUuid", "uniqueID", "sourceCheckUuid", "customCveNo", "severity", "cweName", "affectComponentCount", "description"]}}, "visibility": "INHERITED", "moduleId": 1488373}, {"name": "SourceCheckComponent", "displayName": "", "id": "#/definitions/SourceCheckComponent", "description": "SourceCheck组件数据模型", "schema": {"jsonSchema": {"type": "object", "description": "SourceCheck组件数据模型", "properties": {"id": {"type": "integer", "description": "组件ID"}, "projectUuid": {"type": "string", "description": "项目UUID"}, "uniqueID": {"type": "string", "description": "唯一标识ID"}, "sourceCheckUuid": {"type": "string", "description": "SourceCheck应用UUID"}, "artifactId": {"type": "string", "description": "组件名称"}, "severity": {"type": "string", "description": "风险等级"}, "jarInfoAddFrom": {"type": "string", "description": "语言字典值"}, "licenseIds": {"type": "array", "description": "许可名称列表", "items": {"type": "string"}}, "version": {"type": "string", "description": "当前版本"}, "recommendVersion": {"type": "string", "description": "推荐版本"}, "latestVersion": {"type": "string", "description": "最新版本"}, "countryChineseName": {"type": "string", "description": "所属国家的中文名称"}}, "required": ["id", "projectUuid", "uniqueID", "artifactId"], "x-apifox-orders": ["id", "projectUuid", "uniqueID", "sourceCheckUuid", "artifactId", "severity", "jarInfoAddFrom", "licenseIds", "version", "recommendVersion", "latestVersion", "countryChineseName"]}}, "visibility": "INHERITED", "moduleId": 1488373}]}]}], "securitySchemeCollection": [], "requestCollection": [{"name": "根目录", "children": [], "ordering": [], "items": []}], "environments": [], "commonScripts": [], "globalVariables": [], "commonParameters": {"id": 752317, "createdAt": "2025-04-10T15:46:54.000Z", "updatedAt": "2025-06-18T07:24:11.000Z", "deletedAt": null, "parameters": {"query": [], "body": [], "cookie": [], "header": [{"name": "Authorization", "defaultEnable": true, "type": "string", "id": "tJkUqedXHi", "defaultValue": "Bearer token_here", "schema": {"type": "string", "default": "Bearer token_here"}}]}, "projectId": 6015725, "creatorId": 2198346, "editorId": 653284}, "projectSetting": {"id": "6043435", "auth": {}, "securityScheme": {}, "servers": [{"id": "default", "name": "默认服务"}], "gateway": [], "language": "zh-CN", "apiStatuses": ["developing", "testing", "released", "deprecated"], "mockSettings": {}, "preProcessors": [], "postProcessors": [], "advancedSettings": {"enableJsonc": false, "enableBigint": false, "responseValidate": true, "enableTestScenarioSetting": false, "enableYAPICompatScript": false, "isDefaultUrlEncoding": 2, "publishedDocUrlRules": {"defaultRule": "RESOURCE_KEY_ONLY", "resourceKeyStandard": "NEW"}, "folderShareExpandModeSettings": {"expandId": [], "mode": "AUTO"}}, "initialDisabledMockIds": [], "cloudMock": {"security": "free", "enable": false, "tokenKey": "apifoxToken"}}, "customFunctions": [], "projectAssociations": []}