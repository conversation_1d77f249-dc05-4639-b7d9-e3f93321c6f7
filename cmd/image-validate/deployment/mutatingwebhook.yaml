apiVersion: admissionregistration.k8s.io/v1beta1
kind: MutatingWebhookConfiguration
metadata:
  name: tensorsec-mutating-sidecar
  annotations:
    cert-manager.io/inject-ca-from: tensorsec-tensorwall/tensorsec-injector-certs
  labels:
    app: tensor-webhook
webhooks:
  - name: tensorwall.tensorsecurity.cn
    clientConfig:
      #      url: "https://192.168.254.1:9443/mutating"
      service:
        name: tensorsec-image-validator
        namespace: tensorsec-tensorwall
        path: "/mutating"
    rules:
      - operations: [ "CREATE", "UPDATE" ]
        apiGroups: ["apps", "extensions", ""]
        apiVersions: ["v1", "v1beta1"]
        resources: [ "pods"]
        scope: "Namespaced"
    sideEffects: None
