holmes rules file pack
=======

1. Encode holmes rule file and encrypt it.  
2. Encode the version with it.  

# arguments 
  
```go
outputRulesFilename := flag.String("output",
		"./tensorsec-holmes.thr",
		"Binary for holmes update, `./tensorsec-holmes.thr` is an example.")

	inputRulesFilename := flag.String("input",
		"./falco_rules.local.yaml",
		"Rules file for holmes to work, `./falco_rules.local.yaml` is an example.")

	version := flag.String("version",
		"1.0",
		"Version for pack rulesfile, `1.0` is an example.")
```
  
# V1 Version Spec
A.B  
A: the version generation method version. for v1 version, it's always "1".  
B: the secondary version generated by the number of commits in thegit logs with MR merge commit.  
It will guarantee that the secondary version will be monotonic increasing by the releases of file.  
You could dig the details by looking into the bash file: $REPO_ROOT/build_holmes_rules_thr.sh.  
```bash
#!/usr/bin/env bash

set -e

V0=1
V1=$(git log --merges | grep "into 'master'" | wc -l) 
V=$(printf "%s.%s" $V0 $V1)
echo HOLMES RULES THR VERSION: $V
./dist/holmes-rules-pack --input configs/holmes/rules/holmes_rules.yaml --output ./dist/holmes-rules.thr --version "$V"
```

