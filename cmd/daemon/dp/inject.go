package dp

import (
	"bufio"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"os/exec"
	"path"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"syscall"

	"gitlab.com/piccolo_su/vegeta/cmd/daemon/pkg/container"
	"gitlab.com/piccolo_su/vegeta/cmd/daemon/pkg/degrade"
	"gitlab.com/piccolo_su/vegeta/cmd/daemon/pkg/nodeinfo"
	"gitlab.com/piccolo_su/vegeta/pkg/model"
	"gitlab.com/piccolo_su/vegeta/pkg/util"
	"gitlab.com/security-rd/go-pkg/logging"
	"gitlab.com/security-rd/go-pkg/mq"

	"github.com/docker/docker/pkg/system"
	"github.com/moby/sys/mountinfo"
	"gitlab.com/piccolo_su/vegeta/cmd/daemon/pkg/container/nsenter"
)

const (
	// excludeImage = "daemon"
	containerIDFilePathTemplate      = "/host/proc/%d/root/.container_id"
	containerBasePathTemplate        = "/host/proc/%d/root"
	containerETCLDConfigPathTemplate = "/host/proc/%d/root/etc/ld.so.preload"
	supportOSConfigFilePath          = "/etc/support-os/support-os.conf"
	hostLDConfigPathTemplate         = "/host/var/lib/tensor/containers/%s/ld.so.preload"
)

type Injector struct {
	mountInfo        mountinfo.Info
	subRoot          string
	subPath          string
	npw              *nodeinfo.NodePodsWatcher
	podResInfo       *nodeinfo.PodResInfo
	write            mq.Writer
	excludeNamespace []string
	// containerCommandSeq [][]string
	resourceSyncLock     *sync.Mutex
	resourceContainerMap map[uint32][]string
}

var (
	errUnknownOSParam = errors.New("unknown os")
)

var (
	HostTensorPath              = path.Join(degrade.DriftPath, "mnt")
	HostEtcPreloadPathPrefix    = path.Join(degrade.DriftPath, "containers")
	procPrefix                  = "/host/proc/"
	ContainerTensorPath         = "/.tensor"
	containerTmpMnt             = "/tmpmnt"
	containerEtcPreloadPath     = "/etc/ld.so.preload"
	blockDevPath                = "/dev/tensor"
	logOutputPath               = "/tmp/drift-prevention.log"
	containerOSFilePathTemplate = []string{
		"/host/proc/%d/root/etc/os-release",
		"/host/proc/%d/root/etc/debian_release",
		"/host/proc/%d/root/etc/centos-release",
		"/host/proc/%d/root/etc/VERSION",
		"/host/proc/%d/root/etc/redhat-release",
	}

	supportOSTargets = []string{
		"ubuntu-22.04",
		"ubuntu-21.04",
		"ubuntu-20.04",
		"ubuntu-18.04",
		"ubuntu-16.04",
		"debian-11",
		"debian-10",
		"debian-9",
		"centos-8",
		"centos-7",
		"rhel-8.5",
		"rhel-8.4",
		"rhel-7.9",
		"rhel-6.5",
		"photon-4.0",
		"photon-3.0",
		"photon-2.0",
		"photon-1.0",
		"opensuse-42.3",
	}
)

func (i *Injector) EnableDriftByContainerID(containerID string) error {
	logging.Get().Debug().Msgf("enable drift for container %s", containerID)
	configPath := fmt.Sprintf(hostLDConfigPathTemplate, containerID)
	// cmd := exec.Command("sed", "-i", "'s/^#\\/.tensor\\/dp.so/\\/.tensor\\/dp.so/'", configPath)
	// The "sed" command cannot be used, sed will generate a new file(inode num change), run "strace sed -i '1,100d' test_file" show detail
	f, err := os.Open(configPath)
	if err != nil {
		logging.Get().Error().Msgf("Failed to open ld.so.preload %v", err)
		return err
	}
	defer f.Close()
	buf := bufio.NewReader(f)
	result := ""
	for {
		line, _, c := buf.ReadLine()
		if c == io.EOF {
			break
		}
		if strings.Contains(string(line), "#/.tensor/dp.so") {
			result += strings.Replace(string(line), "#/.tensor/dp.so", "/.tensor/dp.so", 1) + "\n"
			continue
		}
		result += string(line) + "\n"
	}

	fw, err := os.OpenFile(configPath, os.O_WRONLY|os.O_TRUNC, 0755)
	if err != nil {
		logging.Get().Error().Msgf("Failed to open ld.so.preload %v", err)
		return err
	}
	w := bufio.NewWriter(fw)
	_, err = w.WriteString(result)
	if err != nil {
		logging.Get().Error().Msgf("Failed to write ld.so.preload %v", err)
		return err
	}
	err = w.Flush()
	return err
}

func disableDriftByContainerID(containerID string) error {
	logging.Get().Debug().Msgf("disable drift for container %s", containerID)
	configPath := fmt.Sprintf(hostLDConfigPathTemplate, containerID)
	f, err := os.Open(configPath)
	if err != nil {
		logging.Get().Error().Msgf("Failed to open ld.so.preload %v", err)
		return err
	}
	defer f.Close()
	buf := bufio.NewReader(f)
	result := ""
	for {
		line, _, c := buf.ReadLine()
		if c == io.EOF {
			break
		}
		if strings.Contains(string(line), "/.tensor/dp.so") && !strings.Contains(string(line), "#/.tensor/dp.so") {
			result += strings.Replace(string(line), "/.tensor/dp.so", "#/.tensor/dp.so", 1) + "\n"
			continue
		}
		result += string(line) + "\n"
	}

	fw, err := os.OpenFile(configPath, os.O_WRONLY|os.O_TRUNC, 0755)
	if err != nil {
		logging.Get().Error().Msgf("Failed to open ld.so.preload %v", err)
		return err
	}
	w := bufio.NewWriter(fw)
	_, err = w.WriteString(result)
	if err != nil {
		logging.Get().Error().Msgf("Failed to write ld.so.preload %v", err)
		return err
	}
	err = w.Flush()
	return err
}

func copyFile(src, dst string) error {
	sFile, err := os.Open(src)
	if err != nil {
		logging.Get().Error().Msgf("Failed to open ld.so.preload %v", err)
		return err
	}
	defer sFile.Close()

	dstFile, err := os.OpenFile(dst, os.O_WRONLY|os.O_CREATE, 0755)
	if err != nil {
		logging.Get().Error().Msgf("Failed to open ld.so.preload %v", err)
		return err
	}
	defer dstFile.Close()

	_, err = io.Copy(dstFile, sFile)

	return err
}

func prepareFiles() error {
	if _, err := os.Stat(HostTensorPath); err != nil {
		os.Mkdir(HostTensorPath, os.FileMode(0755))
	}

	if _, err := os.Stat(HostEtcPreloadPathPrefix); err != nil {
		os.Mkdir(HostEtcPreloadPathPrefix, os.FileMode(0755))
	}

	// err := copyFile("/tensor/ld.so.preload", HostEtcPreloadPathPrefix)
	// if err != nil {
	// 	logging.Get().Error().Msgf("Failed to copy ld.so.preload %v", err)
	// 	return err
	// }

	err := copyFile("/tensor/dp.so", HostTensorPath+"/dp.so")
	if err != nil {
		logging.Get().Error().Msgf("Failed to copy dp.so %v", err)
		return err
	}
	return nil
}

func (ij *Injector) initCommandSeq(containerID string) [][]string {
	tensorDir := containerTmpMnt + ij.subRoot + ij.subPath
	logging.Get().Info().Msgf("tensorDir: %s", tensorDir)
	tmpCommandSeq := [][]string{
		{"touch", logOutputPath},
		{"chmod", "777", logOutputPath},
		{"mkdir", containerTmpMnt},
		{"mount", blockDevPath, containerTmpMnt},
		{"mkdir", ContainerTensorPath},
		{"mount", "-o", "bind", tensorDir, ContainerTensorPath},
		{"umount", containerEtcPreloadPath},
		{"rm", containerEtcPreloadPath},
		{"touch", containerEtcPreloadPath},
		{"mount", "-o", "bind,ro", filepath.Dir(tensorDir) + "/containers/" + containerID + "/ld.so.preload", containerEtcPreloadPath},
		{"umount", containerTmpMnt},
		{"rmdir", containerTmpMnt},
	}
	return tmpCommandSeq
}

func getExcludeNamespaces() []string {

	namespaces := []string{
		"kube-system",
		"kube-public",
		"kube-node-lease",
		"kube-node-lease-renewer",
		"kube-node-lease-maintenance",
		"kube-node-lease-reclaim",
		"kube-node-lease-preemptor",
		"kube-node-lease-preemptor-maintenance",
		"kube-node-lease-preemptor-renewer",
		"kube-node-lease-preemptor-reclaim",
	}
	excludeNamespacesEnv := os.Getenv("EXCLUDE_NAMESPACES")
	if excludeNamespacesEnv != "" {
		tmpNamespaces := strings.Split(excludeNamespacesEnv, ":")
		namespaces = append(namespaces, tmpNamespaces...)

	}
	logging.Get().Info().Msgf("Exclude namespaces %v", namespaces)
	return namespaces
}

func NewInjector(npw *nodeinfo.NodePodsWatcher, podResInfo *nodeinfo.PodResInfo, write mq.Writer) (*Injector, error) {
	ij := &Injector{}

	err := prepareFiles()
	if err != nil {
		logging.Get().Error().Msgf("Failed to prepare files %v", err)
		return nil, err
	}

	err = ij.getDev(HostTensorPath)
	if err != nil {
		logging.Get().Error().Msgf("Failed to get dev %v", err)
		return nil, err
	}

	ij.npw = npw
	ij.podResInfo = podResInfo
	ij.excludeNamespace = getExcludeNamespaces()
	ij.write = write
	ij.resourceContainerMap = make(map[uint32][]string)
	ij.resourceSyncLock = new(sync.Mutex)
	return ij, err
}

func (ij *Injector) getDev(path string) error {
	// get mount point from directory
	mountInfo, err := mountinfo.GetMounts(mountinfo.ParentsFilter(path))
	if err != nil {
		logging.Get().Err(err).Msg("Failed to get mount info")
		return err
	}
	if len(mountInfo) > 0 {
		// log.Debugf("info %+v", mountInfo[0])
		for _, v := range mountInfo {
			logging.Get().Debug().Msgf("mountInfo %+v", v)
		}
		ij.mountInfo = *mountInfo[len(mountInfo)-1]

	} else {
		return errors.New("Failed to get mount info")
	}

	ij.subRoot = ij.mountInfo.Root
	ij.subPath = strings.TrimPrefix(path, ij.mountInfo.Mountpoint)
	initOSTargetsList(supportOSConfigFilePath)
	logging.Get().Info().Msgf("support os targets: %v", supportOSTargets)
	logging.Get().Info().Msgf("mount info: %#v", ij)

	return nil
}

func (ij *Injector) mknodInProc(pid int) error {
	path := procPrefix + strconv.Itoa(pid) + "/root" + blockDevPath
	if err := os.RemoveAll(path); err != nil {
		logging.Get().Error().Msg(err.Error())
	}

	logging.Get().Trace().Msgf("mknod %s", path)
	dev := int(system.Mkdev(int64(ij.mountInfo.Major), int64(ij.mountInfo.Minor)))
	return syscall.Mknod(path, syscall.S_IFBLK|uint32(os.FileMode(0660)), dev)
}

func (ij *Injector) rmOldConfigBeforeInject(pid int) error {
	injectPaths := []string{fmt.Sprintf(containerBasePathTemplate, pid) + containerEtcPreloadPath,
		fmt.Sprintf(containerBasePathTemplate, pid) + ContainerTensorPath,
	}
	for _, p := range injectPaths {
		if err := os.RemoveAll(p); err != nil {
			logging.Get().Err(err).Msg("rm old path fail")
		}

	}
	return nil
}

func (ij *Injector) putContainerID2File(pid int, cid string) error {
	path := fmt.Sprintf(containerIDFilePathTemplate, pid)
	logging.Get().Trace().Msgf("put container id %s to %s", cid, path)
	return ioutil.WriteFile(path, []byte(cid), 0644)
}

func prepareLDPreloadConfig(containerID string, pid int) error {
	configDir := HostEtcPreloadPathPrefix + "/" + containerID
	if _, err := os.Stat(configDir); err != nil {
		os.Mkdir(configDir, os.FileMode(0755))
	}

	err := copyFile("/tensor/ld.so.preload", configDir+"/ld.so.preload")
	if err != nil {
		logging.Get().Error().Msgf("Failed to copy ld.so.preload %v", err)
		defer os.RemoveAll(configDir)
		return err
	}

	etcConfigPath := fmt.Sprintf(containerETCLDConfigPathTemplate, pid)
	cmd := exec.Command("cat", etcConfigPath, ">>", configDir+"/ld.so.preload")
	err = cmd.Run()
	if err != nil {
		logging.Get().Warn().Msgf("Failed to merge ld.so.preload %v", err)
	}

	return nil
}

func (ij *Injector) GetContainersFromResourceMap(resourceUUID uint32) []string {
	if _, ok := ij.resourceContainerMap[resourceUUID]; !ok {
		ij.resourceContainerMap[resourceUUID] = make([]string, 0)
	}
	return ij.resourceContainerMap[resourceUUID]
}

func (ij *Injector) PutContainersToResourceMap(resourceUUID uint32, containers []string) {
	ij.resourceSyncLock.Lock()
	defer ij.resourceSyncLock.Unlock()
	ij.resourceContainerMap[resourceUUID] = containers
}

func (ij *Injector) RemoveContainersFromResourceMap(resourceUUID uint32) {
	ij.resourceSyncLock.Lock()
	defer ij.resourceSyncLock.Unlock()
	delete(ij.resourceContainerMap, resourceUUID)
}

func (ij *Injector) UpdateContainerDriftSwitch(policies map[uint32]model.DriftPolicy) error {
	// for  _, policy := range policies{
	// 	uuid := util.GenerateUUID(policy.ClusterKey, policy.Namespace, policy.ResourceKind, policy.Resource)
	// }
	logging.Get().Debug().Msgf("Update drift switch %+v", policies)
	for uuid, policy := range policies {
		containers := ij.GetContainersFromResourceMap(uuid)
		if policy.Enable == 1 {
			for _, containerID := range containers {
				err := ij.EnableDriftByContainerID(containerID)
				if err != nil {
					logging.Get().Warn().Msgf("Failed to enable drift for container %s", containerID)
				}
			}
		} else {
			for _, containerID := range containers {
				err := disableDriftByContainerID(containerID)
				if err != nil {
					logging.Get().Warn().Msgf("Failed to disable drift for container %s", containerID)
				}
			}
		}
	}
	return nil
}

func (ij *Injector) DoInject(cm container.ContainerMeta) (bool, uint32, error) {
	// logging.Get().Info().Msgf("Injecting %d", cm.ProcessID)

	osTarget, err := getOSTarget(cm.ProcessID)
	if err != nil {
		logging.Get().Warn().Msgf("Skip inject %d %v", cm.ProcessID, cm.Name)
		return false, 0, err
	}

	isSupport := isSupportOS(osTarget)
	supportInfo, err := GetContainerPodInfo(cm.PodUID, ij.npw, ij.podResInfo)
	if err != nil {
		logging.Get().Err(err).Msg("failed to get container pod info")
		return false, 0, err
	}

	//pause image
	supportInfo.IsSupportDrift = isSupport
	supportInfo.OSTarget = osTarget
	supportInfo.ContainerID = cm.ID
	supportInfo.ScannerStatus = -1
	if osTarget == "" {
		osTarget = "unknown"
		supportInfo.IsSupportDrift = false
	}
	// logging.Get().Info().Str("support info:", fmt.Sprintf("%+v", supportInfo)).Msg("")

	msg, err := json.Marshal(supportInfo)
	if err != nil {
		logging.Get().Err(err).Msg("failed to marshal support info")
	} else {
		if err := Send2Kafka(ij.write, model.SubjectOfDriftSupportEvent, msg); err != nil {
			logging.Get().Err(err).Msg("failed to send msg to kafka")
		}
	}

	if !isSupport {
		// logging.Get().Warn().Interface("containerMeta", cm).Msg("not support os,ignore inject")
		return false, 0, nil
	}

	// excludeNamespaces
	if err != nil || supportInfo.Namespace == "" {
		logging.Get().Error().Msgf("Failed to get container pod info %v", err)
		return false, 0, err
	}
	for _, v := range ij.excludeNamespace {
		if v == supportInfo.Namespace {
			logging.Get().Info().
				Str("containerID", cm.ID).
				Str("namespace", v).
				Msg("skip inject,namespace contains exclude namespace")
			return false, 0, nil
		}
	}

	err = ij.putContainerID2File(cm.ProcessID, cm.ID)
	if err != nil {
		logging.Get().Err(err).Int("ProcessID", cm.ProcessID).Str("containerID", cm.ID).Msg("put container id failed")
		return false, 0, err
	}

	resourceUUID := util.GenerateUUID(supportInfo.Cluster, supportInfo.Namespace, supportInfo.ResourceKind, supportInfo.ResourceName)
	existContainers := ij.GetContainersFromResourceMap(resourceUUID)
	existContainers = append(existContainers, cm.ID)
	ij.PutContainersToResourceMap(resourceUUID, existContainers)

	// check if injected
	injected, err := IsInjected(cm.ProcessID)
	if err != nil {
		logging.Get().Err(err).Str("containerID", cm.ID).Msg("container inject failed")
		return false, 0, nil
	}
	if injected {
		logging.Get().Info().Str("containerID", cm.ID).Msg("container already injected")
		return true, 0, nil
	}

	// inject
	config := nsenter.Config{
		Mount:     true, // Execute into mount namespace
		MountFile: fmt.Sprintf("/host/proc/%d/ns/mnt", cm.ProcessID),
		Target:    cm.ProcessID,
	}

	err = ij.mknodInProc(cm.ProcessID)
	if err != nil {
		logging.Get().Err(err).Int("ProcessID", cm.ProcessID).Str("containerID", cm.ID).Msg("mknod failed")
	}

	err = ij.rmOldConfigBeforeInject(cm.ProcessID)
	if err != nil {
		logging.Get().Warn().Int("ProcessID", cm.ProcessID).Str("containerID", cm.ID).Msg(err.Error())
	}

	err = prepareLDPreloadConfig(cm.ID, cm.ProcessID)
	if err != nil {
		logging.Get().Err(err).Int("ProcessID", cm.ProcessID).Str("containerID", cm.ID).Msg("prepare ld preload config failed")
	}

	commandSeq := ij.initCommandSeq(cm.ID)

	for index, cmd := range commandSeq {
		if len(cmd) == 0 {
			break
		}
		// _, _, err := config.Execute(cmd[0], cmd[1:]...)
		_, _, err := config.Execute(cmd[0], cmd[1:]...)
		if err != nil {
			// encrypted log msg which contain inject detail
			// msg := fmt.Sprintf("index:%d,%s,%s,%v", index, outMsg, errMsg, err)
			// normalMsg := fmt.Sprintf("index:%d,inject failed.", index)
			// logging.Get().Err(err).Str("container", cm.ID).Int("PID", cm.ProcessID).Str("outMsg", outMsg).Str("errMsg", errMsg).Msg("inject err detail")

			// only log index
			logging.Get().Err(err).Str("container", cm.ID).Int("PID", cm.ProcessID).Int("index", index).Msg("inject err")
			continue
		}
	}

	if err != nil {
		logging.Get().
			Err(err).
			Int("processID", cm.ProcessID).
			Str("containerID", cm.ID).
			Msg("inject err")
	}
	isInjectd, err := IsInjected(cm.ProcessID)

	return isInjectd, resourceUUID, err
}

func getRHELOSTargetFromFile(path string) (string, error) {
	targetStr := "rhel-"

	f, err := os.Open(path)
	if err != nil {
		logging.Get().Error().Msgf("Failed to read file %s", path)
		return "", err
	}
	defer f.Close()
	br := bufio.NewReader(f)
	for {
		line, _, c := br.ReadLine()
		if c == io.EOF {
			break
		}
		lineStr := string(line)
		splitList := strings.Split(lineStr, " ")
		if len(splitList) > 1 {
			targetStr += splitList[len(splitList)-2]
		}
	}
	return targetStr, nil
}

func getOSTargetFromFile(path string) (string, error) {
	if strings.Contains(path, "redhat-release") {
		return getRHELOSTargetFromFile(path)
	}

	targetStr := ""
	osName := ""
	osVersion := ""
	fileStr := ""
	f, err := os.Open(path)
	if err != nil {
		logging.Get().Error().Msgf("Failed to read file %s", path)
		return "", err
	}
	defer f.Close()
	br := bufio.NewReader(f)
	for {
		line, _, c := br.ReadLine()
		if c == io.EOF {
			break
		}
		lineStr := string(line)
		kv := strings.Split(lineStr, "=")
		if len(kv) != 2 {
			continue
		}
		if kv[0] == "ID" {
			osName = strings.Trim(strings.Trim(strings.Trim(kv[1], "\n"), " "), "\"")
		}
		if kv[0] == "VERSION_ID" {
			osVersion = strings.Trim(strings.Trim(strings.Trim(kv[1], "\n"), " "), "\"")
		}
		fileStr += lineStr + " "
	}

	if osName == "" || osVersion == "" {
		return fileStr, errUnknownOSParam
	}

	targetStr = osName + "-" + osVersion
	return strings.ToLower(targetStr), nil

}

func getOSTarget(pid int) (string, error) {

	for _, v := range containerOSFilePathTemplate {
		path := fmt.Sprintf(v, pid)
		// logging.Get().Info().Msgf("try path: %v\n", path)
		if _, err := os.Stat(path); err != nil {
			continue
		}
		osTarget, err := getOSTargetFromFile(path)
		if err != nil {
			logging.Get().Error().Msgf("Failed to get os target from file %s,try next", path)
			continue
		}
		return osTarget, nil
	}

	return "", errUnknownOSParam
}

func isSupportOS(osTarget string) bool {
	for _, v := range supportOSTargets {
		if v == osTarget {
			// logging.Get().Info().Str("osTarget:", osTarget).Msg("")
			return true
		}
	}
	logging.Get().Info().Msgf("unsupport os target %s, try to support\n", osTarget)
	return false
}

func initOSTargetsList(configFile string) error {
	f, err := os.Open(configFile)
	if err != nil {
		logging.Get().Warn().Msgf("Failed to read file %s", configFile)
		return err
	}
	defer f.Close()
	br := bufio.NewReader(f)
	for {
		line, _, c := br.ReadLine()
		if c == io.EOF {
			break
		}
		lineStr := strings.Trim(strings.Trim(strings.Trim(string(line), "\n"), " "), "\"")
		if lineStr == "" {
			continue
		}
		supportOSTargets = append(supportOSTargets, strings.ToLower(lineStr))
	}

	return nil
}
