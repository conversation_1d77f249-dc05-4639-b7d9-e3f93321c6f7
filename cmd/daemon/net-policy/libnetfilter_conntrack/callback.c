/*
 * (C) 2005-2011 by <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify it
 * under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 */

#include "internal/internal.h"
#include <libmnl/libmnl.h>

static int __parse_message(const struct nlmsghdr *nlh)
{
    uint16_t type = NFNL_MSG_TYPE(nlh->nlmsg_type);
    uint16_t flags = nlh->nlmsg_flags;
    int ret = NFCT_T_UNKNOWN;

    switch (type)
    {
    case IPCTNL_MSG_CT_NEW: /* same value for IPCTNL_MSG_EXP_NEW. */
        if (flags & (NLM_F_CREATE | NLM_F_EXCL))
            ret = NFCT_T_NEW;
        else
            ret = NFCT_T_UPDATE;
        break;
    case IPCTNL_MSG_CT_DELETE: /* same value for IPCTNL_MSG_EXP_DELETE. */
        ret = NFCT_T_DESTROY;
        break;
    }
    return ret;
}

/* This function uses libmnl helpers, the nfa[] array is intentionally not used
 * since it has a different layout.
 */
int __callback(struct nlmsghdr *nlh, struct nfattr *nfa[], void *data)
{
    int ret = NFNL_CB_STOP;
    unsigned int type;
    struct nf_conntrack *ct = NULL;
    struct __data_container *container = data;
    uint8_t subsys = NFNL_SUBSYS_ID(nlh->nlmsg_type);

    if (nlh->nlmsg_len < NLMSG_LENGTH(sizeof(struct nfgenmsg)))
    {
        errno = EINVAL;
        return NFNL_CB_FAILURE;
    }
    type = __parse_message(nlh);
    if (!(type & container->type))
        return NFNL_CB_CONTINUE;

    switch (subsys)
    {
    case NFNL_SUBSYS_CTNETLINK:
        ct = nfct_new();
        if (ct == NULL)
            return NFNL_CB_FAILURE;

        nfct_nlmsg_parse(nlh, ct);

        if (container->h->cb)
        {
            ret = container->h->cb(type, ct, container->data);
        }
        else if (container->h->cb2)
        {
            ret = container->h->cb2(nlh, type, ct, container->data);
        }
        break;

    default:
        errno = ENOTSUP;
        ret = NFNL_CB_FAILURE;
        break;
    }

    if (ret == NFCT_CB_STOLEN)
        return NFNL_CB_CONTINUE;

    if (ct)
        nfct_destroy(ct);

    return ret;
}
