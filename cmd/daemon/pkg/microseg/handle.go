package microseg

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"github.com/avast/retry-go"
	"net/http"

	heavyagent "gitlab.com/piccolo_su/vegeta/cmd/daemon/pkg/heavy-agent"
	"gitlab.com/piccolo_su/vegeta/pkg/util"
)

const postUrlPath = "/internal/microseg/event"

type MicrosegHandler struct {
	httpClient *http.Client
	postUrl    string
}

// handle implements heavyagent.Handler.
func (mh *MicrosegHandler) Handle(ctx context.Context, obj interface{}) error {
	//log.Info().Msg("process microseg event")
	event, err := json.Marshal(obj)
	if err != nil {
		return err
	}
	// ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	// defer cancel()
	err = mh.sendMessage(ctx, event)
	if err != nil {
		log.Err(err).Msg("post microseg event err")
	}
	return nil
}

func (mh *MicrosegHandler) sendMessage(ctx context.Context, message []byte) error {
	request, err := http.NewRequestWithContext(ctx, http.MethodPost, mh.postUrl, bytes.NewReader(message))
	if err != nil {
		return err
	}

	respHandler := func(resp *http.Response, err error) error {
		if err != nil {
			log.Err(err).Msgf("post cluster info err : %v", err)
			return err
		}
		if resp.StatusCode != http.StatusOK {
			log.Error().Msgf("http resp error: %s", resp.Status)
			return fmt.Errorf("http resp error: %s", resp.Status)
		}
		return nil
	}

	return util.HTTPRequest(ctx, mh.httpClient, request, respHandler, retry.Attempts(3))
}

func NewHandler(clusterManagerSvc string) heavyagent.Handler {
	return &MicrosegHandler{
		httpClient: http.DefaultClient,
		postUrl:    clusterManagerSvc + postUrlPath,
	}
}
