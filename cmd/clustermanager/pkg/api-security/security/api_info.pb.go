// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.21.12
// source: api_info.proto

package security

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ApiInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClusterKey  string `protobuf:"bytes,1,opt,name=clusterKey,proto3" json:"clusterKey,omitempty"`
	Namespace   string `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	PodName     string `protobuf:"bytes,3,opt,name=podName,proto3" json:"podName,omitempty"`
	RemoteIp    string `protobuf:"bytes,4,opt,name=remoteIp,proto3" json:"remoteIp,omitempty"`
	RemotePort  int32  `protobuf:"varint,5,opt,name=remotePort,proto3" json:"remotePort,omitempty"`
	Path        string `protobuf:"bytes,6,opt,name=path,proto3" json:"path,omitempty"`
	Method      string `protobuf:"bytes,7,opt,name=method,proto3" json:"method,omitempty"`
	ContentType string `protobuf:"bytes,8,opt,name=contentType,proto3" json:"contentType,omitempty"`
	Scheme      string `protobuf:"bytes,9,opt,name=scheme,proto3" json:"scheme,omitempty"`
	OwnerName   string `protobuf:"bytes,10,opt,name=ownerName,proto3" json:"ownerName,omitempty"`
	OwnerKind   string `protobuf:"bytes,11,opt,name=ownerKind,proto3" json:"ownerKind,omitempty"`
}

func (x *ApiInfo) Reset() {
	*x = ApiInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_info_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApiInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApiInfo) ProtoMessage() {}

func (x *ApiInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_info_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApiInfo.ProtoReflect.Descriptor instead.
func (*ApiInfo) Descriptor() ([]byte, []int) {
	return file_api_info_proto_rawDescGZIP(), []int{0}
}

func (x *ApiInfo) GetClusterKey() string {
	if x != nil {
		return x.ClusterKey
	}
	return ""
}

func (x *ApiInfo) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *ApiInfo) GetPodName() string {
	if x != nil {
		return x.PodName
	}
	return ""
}

func (x *ApiInfo) GetRemoteIp() string {
	if x != nil {
		return x.RemoteIp
	}
	return ""
}

func (x *ApiInfo) GetRemotePort() int32 {
	if x != nil {
		return x.RemotePort
	}
	return 0
}

func (x *ApiInfo) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *ApiInfo) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

func (x *ApiInfo) GetContentType() string {
	if x != nil {
		return x.ContentType
	}
	return ""
}

func (x *ApiInfo) GetScheme() string {
	if x != nil {
		return x.Scheme
	}
	return ""
}

func (x *ApiInfo) GetOwnerName() string {
	if x != nil {
		return x.OwnerName
	}
	return ""
}

func (x *ApiInfo) GetOwnerKind() string {
	if x != nil {
		return x.OwnerKind
	}
	return ""
}

var File_api_info_proto protoreflect.FileDescriptor

var file_api_info_proto_rawDesc = []byte{
	0x0a, 0x0e, 0x61, 0x70, 0x69, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x08, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x22, 0xbf, 0x02, 0x0a, 0x07, 0x41,
	0x70, 0x69, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x4b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x4b, 0x65, 0x79, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x6f, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x6f, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x49, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x49, 0x70, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65,
	0x6d, 0x6f, 0x74, 0x65, 0x50, 0x6f, 0x72, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a,
	0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61,
	0x74, 0x68, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x16,
	0x0a, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x63, 0x68, 0x65,
	0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65,
	0x12, 0x1c, 0x0a, 0x09, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c,
	0x0a, 0x09, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x4b, 0x69, 0x6e, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x4b, 0x69, 0x6e, 0x64, 0x42, 0x0c, 0x5a, 0x0a,
	0x2e, 0x2f, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_api_info_proto_rawDescOnce sync.Once
	file_api_info_proto_rawDescData = file_api_info_proto_rawDesc
)

func file_api_info_proto_rawDescGZIP() []byte {
	file_api_info_proto_rawDescOnce.Do(func() {
		file_api_info_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_info_proto_rawDescData)
	})
	return file_api_info_proto_rawDescData
}

var file_api_info_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_info_proto_goTypes = []interface{}{
	(*ApiInfo)(nil), // 0: security.ApiInfo
}
var file_api_info_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_info_proto_init() }
func file_api_info_proto_init() {
	if File_api_info_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_info_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApiInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_info_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_info_proto_goTypes,
		DependencyIndexes: file_api_info_proto_depIdxs,
		MessageInfos:      file_api_info_proto_msgTypes,
	}.Build()
	File_api_info_proto = out.File
	file_api_info_proto_rawDesc = nil
	file_api_info_proto_goTypes = nil
	file_api_info_proto_depIdxs = nil
}
