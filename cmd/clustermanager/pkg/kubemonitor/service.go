package kubemonitor

import (
	"context"
	"errors"
	"fmt"
	"hash/fnv"
	"os"
	"runtime/debug"
	"strings"
	"sync/atomic"
	"time"

	"gitlab.com/piccolo_su/vegeta/pkg/echelper"
	"gitlab.com/piccolo_su/vegeta/pkg/k8s"
	pkg "gitlab.com/piccolo_su/vegeta/pkg/kubemonitor"
	"gitlab.com/piccolo_su/vegeta/pkg/util"
	"gitlab.com/security-rd/go-pkg/logging"
	"gitlab.com/security-rd/go-pkg/pb"
	"gitlab.com/security-rd/go-pkg/sdk/palace"
	"gopkg.in/yaml.v2"
)

const (
	eventsCategory   = "kubeMonitor"
	eventsCategoryCN = "集群风险监控"
	eventsModule     = "ContainerSecurity"
	eventsModuleCN   = "容器安全"
)

var (
	rules    []*pkg.RiskyRoleItem
	parseErr error

	defaultResourceRules = []pkg.ResourceMonitorRule{
		pkg.ResourceRiskyCapsRule{},
		pkg.ResourceRiskyVolumeRule{},
		pkg.ResourceWithPrivContainerRule{},
		pkg.ResourcesWithHostNamespaceRule{},
		pkg.ResourcesWithInsecureSecretsEnvRule{},
		pkg.ResourcesWithDefaultSARule{},
		pkg.ResourcesWithRequestLimitSetRule{},
		pkg.ResourceWithSecContextRule{},
	}
)

func init() {
	if err := parseRules(); err != nil {
		logging.Get().Err(err).Msg("parse rules error for kubemonitor")
	}
}

type Service struct {
	monitor     *pkg.KubeRiskyMonitor
	myNamespace string

	registerOK    int32
	dupCache      *DupCache
	palaceHandler *palace.Palace
}

func parseRules() error {
	parseErr = yaml.Unmarshal([]byte(rulesDatastring), &rules)
	if parseErr != nil {
		logging.Get().Err(parseErr).Msg("parse kubemonitor rules error")
	}
	return parseErr
}
func NewService() (*Service, error) {
	namespace := os.Getenv("MY_POD_NAMESPACE")
	monitor, err := pkg.NewKubeRiskMonitor(pkg.Configuration{
		RBRules:       rules,
		ResourceRules: defaultResourceRules,
	}, pkg.NewMemStorage)
	if err != nil {
		return nil, err
	}

	palaceHandler, err := palace.Init()
	if err != nil {
		logging.Get().Err(err).Msgf("failed to init palaceHandler, %v", err)
		return nil, fmt.Errorf("failed to init palaceHandler, %v", err)
	}

	svc := &Service{
		monitor:       monitor,
		myNamespace:   namespace,
		dupCache:      newDupCache(24 * time.Hour),
		palaceHandler: &palaceHandler,
	}
	svc.asyncRiskMonitor()
	return svc, nil
}

func (s *Service) RiskMonitor() *pkg.KubeRiskyMonitor {
	return s.monitor
}

func getDescriptionForRole(rawRule *pkg.RiskyRoleItem, lang, kind string) string {
	cnt := ""
	switch lang {
	case "zh":
		cnt = rawRule.Metadata.Description.RiskCN
	case "en":
		cnt = rawRule.Metadata.Description.Risk
	default:
		cnt = rawRule.Metadata.Description.Risk
	}
	return fmt.Sprintf("%s: %s", kind, cnt)
}

func getEventsRuleName(kind, ruleName string) string {
	return fmt.Sprintf("%s: %s", kind, ruleName)
}

func getSeverity(rawRule *pkg.RiskyRoleItem) uint32 {
	// 0-3: 高危
	// 4-5: 中危
	// 6-7: 低危
	switch rawRule.Metadata.Priority {
	case pkg.PriorityCritical:
		return 0
	case pkg.PriorityHigh:
		return 2
	case pkg.PriorityMedium:
		return 4
	case pkg.PriorityLow:
		return 6
	case pkg.PriorityNone:
		return 7
	default:
		return 5
	}

}

func (s *Service) newDetectionRuleForResRule(raw pkg.ResourceMonitorRule) *pb.DetectionRule {
	rule := new(pb.DetectionRule)
	rule.Name = raw.RuleName()
	rule.Description = raw.Description()["en"]
	rule.Module = eventsModule
	rule.Category = eventsCategory
	rule.Severity = raw.Severity()
	rule.MultiLanguage = map[string]*pb.MultiLanguageValue{
		"description": {
			ValueHash: map[string]string{
				"zh": raw.Description()["zh"],
				"en": raw.Description()["en"],
			},
		},
		"category": {
			ValueHash: map[string]string{
				"zh": eventsCategoryCN,
				"en": eventsCategory,
			},
		},
		"module": {
			ValueHash: map[string]string{
				"zh": eventsModuleCN,
				"en": eventsModule,
			},
		},
	}
	for _, kv := range raw.KVs() {
		zhVal := kv.ValueMulti["zh"]
		if zhVal == "" {
			zhVal = kv.DefaultValue
		}
		enVal := kv.ValueMulti["en"]
		if enVal == "" {
			enVal = kv.DefaultValue
		}
		rule.CustomKV = append(rule.CustomKV, &pb.MultiLanguageKV{
			KVHash: map[string]*pb.KV{
				"zh": {Key: kv.KeyMulti["zh"], Value: zhVal},
				"en": {Key: kv.KeyMulti["en"], Value: enVal},
			},
		})
	}
	return rule
}
func (s *Service) newDetectionRule(rawRule *pkg.RiskyRoleItem) []*pb.DetectionRule {
	rules := make([]*pb.DetectionRule, 0, 2)
	roleRule := pb.DetectionRule{}
	roleRule.Description = getDescriptionForRole(rawRule, "en", "role")
	roleRule.Name = pkg.GetRuleNameFromRBRule(rawRule, "role")
	roleRule.Module = eventsModule
	roleRule.Category = eventsCategory
	roleRule.Severity = getSeverity(rawRule)
	roleRule.MultiLanguage = map[string]*pb.MultiLanguageValue{
		"description": {
			ValueHash: map[string]string{
				"zh": getDescriptionForRole(rawRule, "zh", "role"),
				"en": getDescriptionForRole(rawRule, "en", "role"),
			},
		},
		"category": {
			ValueHash: map[string]string{
				"zh": eventsCategoryCN,
				"en": eventsCategory,
			},
		},
		"module": {
			ValueHash: map[string]string{
				"zh": eventsModuleCN,
				"en": eventsModule,
			},
		},
	}
	if len(rawRule.Metadata.Description.Example) > 0 {
		roleRule.CustomKV = append(roleRule.CustomKV, &pb.MultiLanguageKV{
			KVHash: map[string]*pb.KV{
				"zh": {Key: "样例", Value: rawRule.Metadata.Description.Example},
				"en": {Key: "Exmaple", Value: rawRule.Metadata.Description.Example},
			},
		})
	}
	if len(rawRule.Metadata.Description.Verb) > 0 {
		roleRule.CustomKV = append(roleRule.CustomKV, &pb.MultiLanguageKV{
			KVHash: map[string]*pb.KV{
				"zh": {Key: "动作", Value: rawRule.Metadata.Description.Verb},
				"en": {Key: "Verb", Value: rawRule.Metadata.Description.Verb},
			},
		})
	}
	if len(rawRule.Metadata.Description.Resources) > 0 {
		roleRule.CustomKV = append(roleRule.CustomKV, &pb.MultiLanguageKV{
			KVHash: map[string]*pb.KV{
				"zh": {Key: "资源", Value: rawRule.Metadata.Description.Resources},
				"en": {Key: "Resources", Value: rawRule.Metadata.Description.Resources},
			},
		})
	}
	rules = append(rules, &roleRule)

	// nolint
	croleRule := roleRule
	croleRule.Name = pkg.GetRuleNameFromRBRule(rawRule, "clusterRole")
	croleRule.Description = getDescriptionForRole(rawRule, "en", "role")
	croleRule.MultiLanguage = map[string]*pb.MultiLanguageValue{
		"description": {
			ValueHash: map[string]string{
				"zh": getDescriptionForRole(rawRule, "zh", "clusterRole"),
				"en": getDescriptionForRole(rawRule, "en", "clusterRole"),
			},
		},
		"category": {
			ValueHash: map[string]string{
				"zh": eventsCategoryCN,
				"en": eventsCategory,
			},
		},
		"module": {
			ValueHash: map[string]string{
				"zh": eventsModuleCN,
				"en": eventsModule,
			},
		},
	}

	rules = append(rules, &croleRule)
	return rules
}

func (s *Service) setRegisterOK() {
	atomic.StoreInt32(&s.registerOK, 1)
}

func (s *Service) isRegisterOK() bool {
	return atomic.LoadInt32(&s.registerOK) == 1
}

func uuid(evt *pkg.KubeMonitorEvent, now time.Time) uint64 {
	bs := evt.Identity()

	key := fmt.Sprintf("%s-%d", bs, now.UnixNano())
	h := fnv.New64a()
	_, _ = h.Write([]byte(key))
	return h.Sum64()
}

func getEventTargetID(name string, kind string) string {
	return fmt.Sprintf("%s (%s)", name, kind)
}

func genPalaceSignalParams(evt *pkg.KubeMonitorEvent) (palace.RuleKey, []palace.Scope, map[string]interface{}) {
	ruleKey := palace.RuleKey{
		Category: eventsCategory,
		Name:     evt.RuleName,
	}

	// 告警信号的工作负载、影响范围
	// scopes为列表格式，存在递进关系： container -> cluster
	// kind、name 为必填，id若有则需填写
	var clusterName = evt.ClusterKey
	clusterManager, ok := k8s.GetClusterManager()
	if ok {
		var err error
		clusterName, err = clusterManager.GetClusterName(evt.ClusterKey)
		if err != nil {
			logging.Get().Warn().Err(err).Msg("GetClusterName err")
		}
	}

	scopes := []palace.Scope{
		{
			Kind: palace.ScopeKindCluster,
			ID:   evt.ClusterKey,
			Name: clusterName, // cluster name
		},
		{
			Kind: palace.ScopeKindResource,
			Name: fmt.Sprintf("%s(%s)", evt.TargetObject.Name, evt.TargetObject.Kind),
		},
	}
	if evt.TargetObject.Namespace != "" {
		scopes = append(scopes, palace.Scope{
			Kind: palace.ScopeKindNamespace,
			Name: evt.TargetObject.Namespace, // namespace name
		})
	}

	signalContext := map[string]interface{}{}
	for k, v := range evt.Context {
		signalContext[k] = v
	}

	return ruleKey, scopes, signalContext
}

func (s *Service) handleMonitorEvent(ctx context.Context, evt *pkg.KubeMonitorEvent) error {
	defer func() {
		if r := recover(); r != nil {
			logging.Get().Error().Msgf("Panic: %v. Stack: %s", r, debug.Stack())
		}
	}()

	if s.dupCache.checkDuplicate(evt) {
		logging.Get().Info().Str("event id", evt.Identity()).Msg("find duplicated. canceled")
		return nil
	}
	if evt.TargetObject.Namespace == s.myNamespace || evt.TargetObject.Namespace == "kube-system" {
		return nil
	}
	if (evt.TargetObject.Kind == "role" || evt.TargetObject.Kind == "clusterRole") && strings.Index(evt.TargetObject.Name, "system:") == 0 {
		return nil
	}

	ruleKey, scopes, signalContext := genPalaceSignalParams(evt)
	err := s.palaceHandler.SendSignal(ruleKey, scopes, signalContext)
	if err != nil {
		logging.Get().Err(err).Str("args", fmt.Sprintf("%+v", evt)).Msg("kubeMonitor send signal to palace fails!")
	} else {
		s.dupCache.addEvent(evt)
	}

	return nil
}

func (s *Service) asyncRiskMonitor() {
	go func() {
		defer func() {
			if r := recover(); r != nil {
				logging.Get().Error().Msgf("Panic: %v. Stack: %s", r, debug.Stack())
			}
		}()

		err := s.doRegisterEventsCenterRules(context.Background())
		if err != nil {
			logging.Get().Err(err).Msg("register event center rules not all ok. will retry...")
			s.asyncRegisterEventsCenterRules()
		}

		for evt := range s.monitor.OutputChannel() {
			if err := s.handleMonitorEvent(context.Background(), evt); err != nil {
				logging.Get().Err(err).Msgf("handle monitor event error. event: %+v", evt)
			}
		}
	}()
}

func (s *Service) asyncRegisterEventsCenterRules() {
	go func() {
		defer func() {
			if r := recover(); r != nil {
				logging.Get().Error().Msgf("Panic when registering eventsCenter: %v. stack: %s", r, debug.Stack())
			}
		}()

		ticker := time.NewTicker(5 * time.Minute)
		defer ticker.Stop()

		stop := false
		for !stop {
			for range ticker.C {
				if err := s.doRegisterEventsCenterRules(context.Background()); err == nil {
					stop = true
				} else {
					logging.Get().Err(err).Msg("register event center rules not all ok. will retry...")
				}
			}
		}
	}()

}
func (s *Service) doRegisterEventsCenterRules(ctx context.Context) error {
	if s.isRegisterOK() {
		return nil
	}

	defer func() {
		if r := recover(); r != nil {
			logging.Get().Error().Msgf("Panic when registerint eventsCenter: %v. stack: %s", r, debug.Stack())
		}
	}()

	allSuccess := true
	allRules := make([]*pb.DetectionRule, 0, 50)
	for _, resRule := range defaultResourceRules {
		resDRule := s.newDetectionRuleForResRule(resRule)
		allRules = append(allRules, resDRule)
	}

	for _, rule := range rules {
		drules := s.newDetectionRule(rule)
		for _, drule := range drules {
			allRules = append(allRules, drule)
		}
	}

	for _, r := range allRules {
		func(detectionRule *pb.DetectionRule) {
			oneCtx, cancel := context.WithTimeout(ctx, 2*time.Second)
			defer cancel()

			err := util.RetryWithBackoff(oneCtx, func() error {
				sherlockClient := echelper.NewSherlockClient(os.Getenv("SHERLOCK_URL"))
				return sherlockClient.AddDetectionRule(oneCtx, detectionRule)
			})

			if err != nil {
				logging.Get().Err(err).Msgf("add rule for eventsCenter error. data: %+v", detectionRule)
				allSuccess = false
			}
		}(r)
	}

	if allSuccess {
		s.setRegisterOK()
		return nil
	} else {
		return errors.New("not all success")
	}
}
