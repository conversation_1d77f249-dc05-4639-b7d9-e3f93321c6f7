-- MySQL dump 10.13  Distrib 8.0.29, for Linux (x86_64)
--
-- Host: ************    Database: ivan
-- ------------------------------------------------------
-- Server version	8.0.28

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `ivan_export_html_prepare`
--

DROP TABLE IF EXISTS `ivan_export_html_prepare`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ivan_export_html_prepare` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `task_id` bigint unsigned NOT NULL DEFAULT '0',
  `data_type` tinyint NOT NULL DEFAULT '0',
  `data` longtext NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `task_id` (`task_id`,`data_type`)
) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ivan_export_html_prepare`
--
-- WHERE:  1 LIMIT 1000

LOCK TABLES `ivan_export_html_prepare` WRITE;
/*!40000 ALTER TABLE `ivan_export_html_prepare` DISABLE KEYS */;
INSERT INTO `ivan_export_html_prepare` VALUES (1,7,2,'22'),(2,7,1,'{\"imageCount\":1,\"onlineImageCount\":0,\"trustedImageCount\":0,\"hasFixedVulnImageCount\":1,\"reinforcedImageCount\":0,\"vulnSum\":0,\"vulnSeverity\":{\"critical\":0,\"high\":0,\"medium\":0,\"low\":0,\"unknown\":0},\"imageSecurity\":{\"hasVuln\":0,\"hasMalicious\":0,\"hasSensitive\":1,\"hasWebshell\":0,\"hasSoftware\":0,\"hasExceptEnv\":0,\"exceptionBoot\":1,\"hasExceptLicense\":0}}'),(4,9,2,'52'),(5,9,1,'{\"imageCount\":22,\"onlineImageCount\":22,\"trustedImageCount\":0,\"hasFixedVulnImageCount\":4,\"reinforcedImageCount\":0,\"vulnSum\":979,\"vulnSeverity\":{\"critical\":10,\"high\":105,\"medium\":155,\"low\":709,\"unknown\":0},\"imageSecurity\":{\"hasVuln\":4,\"hasMalicious\":0,\"hasSensitive\":2,\"hasWebshell\":0,\"hasSoftware\":0,\"hasExceptEnv\":0,\"exceptionBoot\":11,\"hasExceptLicense\":0}}'),(6,12,2,'75'),(7,12,1,'{\"imageCount\":1,\"onlineImageCount\":0,\"trustedImageCount\":0,\"hasFixedVulnImageCount\":1,\"reinforcedImageCount\":0,\"vulnSum\":86,\"vulnSeverity\":{\"critical\":0,\"high\":0,\"medium\":45,\"low\":41,\"unknown\":0},\"imageSecurity\":{\"hasVuln\":1,\"hasMalicious\":0,\"hasSensitive\":1,\"hasWebshell\":0,\"hasSoftware\":0,\"hasExceptEnv\":0,\"exceptionBoot\":1,\"hasExceptLicense\":0}}'),(8,14,2,'77'),(9,14,1,'{\"imageCount\":1,\"onlineImageCount\":0,\"trustedImageCount\":0,\"hasFixedVulnImageCount\":1,\"reinforcedImageCount\":0,\"vulnSum\":483,\"vulnSeverity\":{\"critical\":43,\"high\":121,\"medium\":195,\"low\":123,\"unknown\":1},\"imageSecurity\":{\"hasVuln\":1,\"hasMalicious\":0,\"hasSensitive\":1,\"hasWebshell\":0,\"hasSoftware\":0,\"hasExceptEnv\":1,\"exceptionBoot\":1,\"hasExceptLicense\":0}}'),(10,15,2,'78'),(11,15,1,'{\"imageCount\":1,\"onlineImageCount\":0,\"trustedImageCount\":0,\"hasFixedVulnImageCount\":1,\"reinforcedImageCount\":0,\"vulnSum\":2902,\"vulnSeverity\":{\"critical\":226,\"high\":973,\"medium\":1196,\"low\":495,\"unknown\":12},\"imageSecurity\":{\"hasVuln\":1,\"hasMalicious\":0,\"hasSensitive\":1,\"hasWebshell\":0,\"hasSoftware\":0,\"hasExceptEnv\":1,\"exceptionBoot\":1,\"hasExceptLicense\":0}}'),(12,18,2,'81'),(13,18,1,'{\"imageCount\":1,\"onlineImageCount\":0,\"trustedImageCount\":0,\"hasFixedVulnImageCount\":1,\"reinforcedImageCount\":0,\"vulnSum\":483,\"vulnSeverity\":{\"critical\":43,\"high\":121,\"medium\":195,\"low\":123,\"unknown\":1},\"imageSecurity\":{\"hasVuln\":1,\"hasMalicious\":0,\"hasSensitive\":1,\"hasWebshell\":0,\"hasSoftware\":0,\"hasExceptEnv\":1,\"exceptionBoot\":1,\"hasExceptLicense\":0}}'),(14,19,2,'146'),(15,19,1,'{\"imageCount\":65,\"onlineImageCount\":0,\"trustedImageCount\":0,\"hasFixedVulnImageCount\":65,\"reinforcedImageCount\":0,\"vulnSum\":1329,\"vulnSeverity\":{\"critical\":2,\"high\":69,\"medium\":678,\"low\":580,\"unknown\":0},\"imageSecurity\":{\"hasVuln\":58,\"hasMalicious\":0,\"hasSensitive\":65,\"hasWebshell\":0,\"hasSoftware\":0,\"hasExceptEnv\":10,\"exceptionBoot\":55,\"hasExceptLicense\":0}}'),(16,20,2,'147'),(17,20,1,'{\"imageCount\":1,\"onlineImageCount\":0,\"trustedImageCount\":0,\"hasFixedVulnImageCount\":1,\"reinforcedImageCount\":0,\"vulnSum\":2902,\"vulnSeverity\":{\"critical\":226,\"high\":973,\"medium\":1196,\"low\":495,\"unknown\":12},\"imageSecurity\":{\"hasVuln\":1,\"hasMalicious\":0,\"hasSensitive\":1,\"hasWebshell\":0,\"hasSoftware\":0,\"hasExceptEnv\":1,\"exceptionBoot\":1,\"hasExceptLicense\":0}}'),(18,22,2,'149'),(19,22,1,'{\"imageCount\":1,\"onlineImageCount\":0,\"trustedImageCount\":0,\"hasFixedVulnImageCount\":1,\"reinforcedImageCount\":0,\"vulnSum\":918,\"vulnSeverity\":{\"critical\":10,\"high\":106,\"medium\":142,\"low\":660,\"unknown\":0},\"imageSecurity\":{\"hasVuln\":1,\"hasMalicious\":0,\"hasSensitive\":0,\"hasWebshell\":0,\"hasSoftware\":0,\"hasExceptEnv\":0,\"exceptionBoot\":1,\"hasExceptLicense\":0}}'),(20,25,2,'151'),(21,25,1,'{\"imageCount\":1,\"onlineImageCount\":0,\"trustedImageCount\":0,\"hasFixedVulnImageCount\":1,\"reinforcedImageCount\":0,\"vulnSum\":483,\"vulnSeverity\":{\"critical\":43,\"high\":121,\"medium\":195,\"low\":123,\"unknown\":1},\"imageSecurity\":{\"hasVuln\":1,\"hasMalicious\":0,\"hasSensitive\":1,\"hasWebshell\":0,\"hasSoftware\":0,\"hasExceptEnv\":1,\"exceptionBoot\":1,\"hasExceptLicense\":0}}');
/*!40000 ALTER TABLE `ivan_export_html_prepare` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ivan_export_task`
--

DROP TABLE IF EXISTS `ivan_export_task`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ivan_export_task` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `task_type` varchar(100) NOT NULL DEFAULT '',
  `execute_type` varchar(50) DEFAULT '',
  `parameter` longtext,
  `lang` varchar(10) NOT NULL DEFAULT '' COMMENT '导出语言',
  `file_path` text,
  `creator` varchar(200) NOT NULL DEFAULT '',
  `start_at` bigint NOT NULL DEFAULT '0',
  `finish_at` bigint NOT NULL DEFAULT '0',
  `err_msg` longtext,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_execute_type` (`execute_type`)
) ENGINE=InnoDB AUTO_INCREMENT=33 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ivan_export_task`
--
-- WHERE:  1 LIMIT 1000

LOCK TABLES `ivan_export_task` WRITE;
/*!40000 ALTER TABLE `ivan_export_task` DISABLE KEYS */;
/*!40000 ALTER TABLE `ivan_export_task` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ivan_export_task_image`
--

DROP TABLE IF EXISTS `ivan_export_task_image`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ivan_export_task_image` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `task_id` bigint unsigned NOT NULL DEFAULT '0',
  `image_id` bigint unsigned NOT NULL DEFAULT '0',
  `image_name` varchar(200) NOT NULL DEFAULT '',
  `image_unique_id` bigint unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `task_image` (`task_id`,`image_id`)
) ENGINE=InnoDB AUTO_INCREMENT=158 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ivan_export_task_image`
--
-- WHERE:  1 LIMIT 1000

LOCK TABLES `ivan_export_task_image` WRITE;
/*!40000 ALTER TABLE `ivan_export_task_image` DISABLE KEYS */;
INSERT INTO `ivan_export_task_image` VALUES (1,62,11779,'https://harbor.tensorsecurity.com/vulhub/xj-test_image:latest',17126803256156859545);
/*!40000 ALTER TABLE `ivan_export_task_image` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ivan_export_vuln_image`
--

DROP TABLE IF EXISTS `ivan_export_vuln_image`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ivan_export_vuln_image` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `task_id` bigint unsigned NOT NULL DEFAULT '0',
  `unique_vuln` bigint unsigned NOT NULL DEFAULT '0',
  `severity` tinyint unsigned NOT NULL DEFAULT '0',
  `can_fixed` tinyint(1) NOT NULL DEFAULT '0',
  `images` longtext,
  PRIMARY KEY (`id`),
  UNIQUE KEY `task_vuln` (`task_id`,`unique_vuln`),
  KEY `task_vuln_severity` (`task_id`,`severity`,`unique_vuln`)
) ENGINE=InnoDB AUTO_INCREMENT=10607 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ivan_export_vuln_image`
--
-- WHERE:  1 LIMIT 1000

LOCK TABLES `ivan_export_vuln_image` WRITE;
/*!40000 ALTER TABLE `ivan_export_vuln_image` DISABLE KEYS */;
/*!40000 ALTER TABLE `ivan_export_vuln_image` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ivan_scanner_scan_export_task`
--

DROP TABLE IF EXISTS `ivan_scanner_scan_export_task`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ivan_scanner_scan_export_task` (
  `status` tinyint unsigned DEFAULT NULL,
  `check_type` varchar(255) DEFAULT NULL,
  `cluster_id` varchar(255) DEFAULT NULL,
  `task_id` varchar(255) DEFAULT NULL,
  `filename` varchar(255) DEFAULT NULL,
  `username` varchar(255) DEFAULT NULL,
  `created_at` bigint DEFAULT NULL,
  `finished_at` bigint DEFAULT NULL,
  `content` mediumblob
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ivan_scanner_scan_export_task`
--
-- WHERE:  1 LIMIT 1000

LOCK TABLES `ivan_scanner_scan_export_task` WRITE;
/*!40000 ALTER TABLE `ivan_scanner_scan_export_task` DISABLE KEYS */;
/*!40000 ALTER TABLE `ivan_scanner_scan_export_task` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-06-17 11:22:22
