apiVersion: apps/v1
kind: Deployment
metadata:
  name: faulty
  namespace: tensorsec
  labels:
    app: faulty
spec:
  replicas: 1
  selector:
    matchLabels:
      app: faulty
  template:
    metadata:
      labels:
        app: faulty
    spec:
      nodeSelector:
        kubernetes.io/hostname: ecs-4751
      hostPID: true
      containers:
      - name: faulty
        image: harbor.tensorsecurity.com/tensorsecurity/faulty:v1.5
        imagePullPolicy: IfNotPresent
        stdin: true
        tty: true
        securityContext:
          privileged: true
          capabilities:
            add:
              - SYS_PTRACE
        envFrom:
        - secretRef:
            name: common-secret
      imagePullSecrets:
        - name: harbor-admin-secret
